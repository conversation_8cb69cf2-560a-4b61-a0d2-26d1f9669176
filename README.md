# 荔枝TV - Android TV应用

一个使用Kotlin和Jetpack Compose开发的Android TV应用，提供丰富的媒体内容浏览体验。

## 功能特性

- 🎨 **暗黑主题设计** - 专为TV观看环境优化的深色界面
- 📱 **可收缩侧边导航** - 左侧导航栏支持展开/收缩，节省屏幕空间
- 🎯 **TV遥控器优化** - 完全支持遥控器导航和焦点管理
- 📺 **多种内容类型** - 支持搜索、主页、直播、电影、电视剧、收藏等功能
- 🎮 **现代UI框架** - 基于Jetpack Compose构建的现代化界面

## 项目结构

```
app/
├── src/main/java/com/google/chuangke/
│   ├── MainActivity.kt                 # 主活动
│   ├── navigation/                     # 导航相关
│   │   └── NavigationItem.kt          # 导航项定义
│   ├── ui/
│   │   ├── components/                # UI组件
│   │   │   ├── SideNavigation.kt      # 侧边导航栏
│   │   │   └── TVFocusable.kt         # TV专用焦点组件
│   │   ├── screens/                   # 页面
│   │   │   ├── MainScreen.kt          # 主页面布局
│   │   │   ├── SearchScreen.kt        # 搜索页面
│   │   │   ├── HomeScreen.kt          # 主页
│   │   │   ├── LiveScreen.kt          # 直播页面
│   │   │   ├── MoviesScreen.kt        # 电影页面
│   │   │   ├── TVShowsScreen.kt       # 电视剧页面
│   │   │   ├── FavoritesScreen.kt     # 收藏页面
│   │   │   └── SettingsScreen.kt      # 设置页面
│   │   └── theme/                     # 主题配置
│   │       ├── Color.kt               # 颜色定义
│   │       ├── Theme.kt               # 主题配置
│   │       └── Type.kt                # 字体配置
│   └── res/                           # 资源文件
```

## 技术栈

- **Kotlin** - 主要开发语言
- **Jetpack Compose** - 现代化UI框架
- **Material Design 3** - 设计系统
- **Navigation Compose** - 导航管理
- **Android TV Support** - TV平台支持

## 主要功能模块

### 1. 侧边导航栏
- 可展开/收缩的左侧导航
- 展开时显示图标和文字
- 收缩时仅显示图标
- 支持遥控器焦点导航

### 2. 内容页面
- **搜索页面** - 内容搜索功能
- **主页** - 推荐内容展示
- **直播** - 直播频道列表
- **电影** - 电影内容浏览
- **电视剧** - 电视剧内容浏览
- **收藏** - 用户收藏管理
- **设置** - 应用设置配置

### 3. TV优化
- 遥控器按键支持
- 焦点管理和视觉反馈
- TV友好的UI组件
- 适配大屏幕显示

## 开发环境要求

- Android Studio Arctic Fox 或更高版本
- Kotlin 1.9.10+
- Android SDK 21+
- 目标SDK 34

## 构建和运行

1. 克隆项目到本地
2. 使用Android Studio打开项目
3. 等待Gradle同步完成
4. 连接Android TV设备或启动TV模拟器
5. 点击运行按钮

## 包名配置

应用包名：`com.google.chuangke`

## 许可证

本项目仅供学习和演示使用。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
