# 荔枝TV - Plex风格Android TV媒体中心

一个使用Kotlin和Jetpack Compose开发的Android TV应用，采用类似Plex的设计风格，提供专业的媒体中心体验。

## 功能特性

- 🎨 **Plex风格设计** - 专业的媒体中心界面，暗黑主题优化
- 📱 **可收缩侧边导航** - 左侧导航栏支持展开/收缩，节省屏幕空间
- 🎯 **TV遥控器优化** - 完全支持遥控器导航和焦点管理
- 🎬 **海报墙展示** - 类似Plex的海报墙布局，支持多种尺寸
- 📺 **继续观看** - 智能跟踪观看进度，快速恢复播放
- 📊 **观看历史** - 完整的观看记录和进度管理
- 🔍 **智能筛选** - 按类型、评分、时间等多维度筛选内容
- 📖 **详细信息页** - 丰富的媒体元数据展示，包括演员、导演、简介等
- 🎮 **现代UI框架** - 基于Jetpack Compose构建的现代化界面

## 项目结构

```
app/
├── src/main/java/com/google/chuangke/
│   ├── MainActivity.kt                 # 主活动
│   ├── data/                          # 数据层
│   │   ├── model/                     # 数据模型
│   │   │   └── MediaModels.kt         # 媒体相关数据模型
│   │   └── repository/                # 数据仓库
│   │       ├── SampleData.kt          # 示例数据
│   │       └── WatchHistoryRepository.kt # 观看历史管理
│   ├── navigation/                     # 导航相关
│   │   └── NavigationItem.kt          # 导航项定义
│   ├── ui/
│   │   ├── components/                # UI组件
│   │   │   ├── SideNavigation.kt      # 侧边导航栏
│   │   │   ├── TVFocusable.kt         # TV专用焦点组件
│   │   │   └── MediaPosterWall.kt     # 媒体海报墙组件
│   │   ├── screens/                   # 页面
│   │   │   ├── MainScreen.kt          # 主页面布局
│   │   │   ├── SearchScreen.kt        # 搜索页面
│   │   │   ├── HomeScreen.kt          # Plex风格主页
│   │   │   ├── LiveScreen.kt          # 直播页面
│   │   │   ├── MoviesScreen.kt        # 电影库页面
│   │   │   ├── TVShowsScreen.kt       # 电视剧页面
│   │   │   ├── FavoritesScreen.kt     # 收藏页面
│   │   │   ├── SettingsScreen.kt      # 设置页面
│   │   │   └── MediaDetailScreen.kt   # 媒体详情页面
│   │   └── theme/                     # 主题配置
│   │       ├── Color.kt               # 颜色定义
│   │       ├── Theme.kt               # 主题配置
│   │       └── Type.kt                # 字体配置
│   └── res/                           # 资源文件
```

## 技术栈

- **Kotlin** - 主要开发语言
- **Jetpack Compose** - 现代化UI框架
- **Material Design 3** - 设计系统
- **Navigation Compose** - 导航管理
- **Android TV Support** - TV平台支持

## 主要功能模块

### 1. Plex风格主页
- **英雄区域** - 特色内容展示，支持直接播放
- **继续观看** - 智能显示未完成的内容，支持进度恢复
- **最近添加** - 展示新加入的媒体内容
- **个性化推荐** - 基于观看历史的智能推荐
- **媒体库快速访问** - 快速进入各个媒体库

### 2. 媒体海报墙
- **多尺寸支持** - 小、中、大、超大四种海报尺寸
- **进度显示** - 观看进度条和已观看标识
- **评分展示** - 媒体评分和星级显示
- **焦点效果** - TV遥控器友好的焦点交互

### 3. 媒体库管理
- **智能筛选** - 按类型、评分、年份等多维度筛选
- **排序选项** - 支持多种排序方式
- **网格布局** - 自适应的海报网格展示
- **详细信息** - 丰富的媒体元数据

### 4. 观看历史与进度
- **播放进度跟踪** - 自动记录观看进度
- **继续观看管理** - 智能管理未完成内容
- **观看统计** - 详细的观看数据统计
- **历史记录** - 完整的观看历史追踪

### 5. 媒体详情页面
- **详细信息展示** - 包括简介、演员、导演等
- **操作按钮** - 播放、收藏、分享等功能
- **演员阵容** - 演员信息和角色展示
- **相关推荐** - 基于当前内容的推荐

### 6. TV优化
- **遥控器按键支持** - 完整的D-pad和确认键支持
- **焦点管理** - 智能的焦点导航和视觉反馈
- **TV友好组件** - 专为TV设计的UI组件
- **大屏幕适配** - 针对TV屏幕的布局优化

## 开发环境要求

- Android Studio Arctic Fox 或更高版本
- Kotlin 1.9.10+
- Android SDK 21+
- 目标SDK 34

## 构建和运行

1. 克隆项目到本地
2. 使用Android Studio打开项目
3. 等待Gradle同步完成
4. 连接Android TV设备或启动TV模拟器
5. 点击运行按钮

## 包名配置

应用包名：`com.google.chuangke`

## 许可证

本项目仅供学习和演示使用。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
