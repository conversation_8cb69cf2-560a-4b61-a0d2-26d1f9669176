package com.google.chuangke.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector

enum class Screen {
    SEARCH,
    HOME,
    LIVE,
    MOVIES,
    TV_SHOWS,
    FAVORITES,
    SETTINGS
}

data class NavigationItem(
    val screen: Screen,
    val title: String,
    val icon: ImageVector,
    val route: String
)

val navigationItems = listOf(
    NavigationItem(
        screen = Screen.SEARCH,
        title = "搜索",
        icon = Icons.Default.Search,
        route = "search"
    ),
    NavigationItem(
        screen = Screen.HOME,
        title = "主页",
        icon = Icons.Default.Home,
        route = "home"
    ),
    NavigationItem(
        screen = Screen.LIVE,
        title = "直播",
        icon = Icons.Default.LiveTv,
        route = "live"
    ),
    NavigationItem(
        screen = Screen.MOVIES,
        title = "电影",
        icon = Icons.Default.Movie,
        route = "movies"
    ),
    NavigationItem(
        screen = Screen.TV_SHOWS,
        title = "电视剧",
        icon = Icons.Default.Tv,
        route = "tv_shows"
    ),
    NavigationItem(
        screen = Screen.FAVORITES,
        title = "收藏",
        icon = Icons.Default.Favorite,
        route = "favorites"
    ),
    NavigationItem(
        screen = Screen.SETTINGS,
        title = "设置",
        icon = Icons.Default.Settings,
        route = "settings"
    )
)
