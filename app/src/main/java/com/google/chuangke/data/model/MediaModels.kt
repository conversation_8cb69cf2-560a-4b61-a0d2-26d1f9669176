package com.google.chuangke.data.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * 媒体类型枚举
 */
enum class MediaType {
    MOVIE,
    TV_SHOW,
    EPISODE,
    LIVE_TV,
    MUSIC,
    PHOTO
}

/**
 * 媒体项目基础数据类
 */
data class MediaItem(
    val id: String,
    val title: String,
    val type: MediaType,
    val posterUrl: String? = null,
    val backdropUrl: String? = null,
    val overview: String? = null,
    val releaseDate: String? = null,
    val rating: Double? = null,
    val duration: Int? = null, // 分钟
    val genres: List<String> = emptyList(),
    val cast: List<Person> = emptyList(),
    val director: String? = null,
    val studio: String? = null,
    val year: Int? = null,
    val isWatched: Boolean = false,
    val watchProgress: Float = 0f, // 0.0 - 1.0
    val lastWatchedDate: Long? = null,
    val addedDate: Long = System.currentTimeMillis()
)

/**
 * 电视剧特有数据
 */
data class TVShow(
    val mediaItem: MediaItem,
    val totalSeasons: Int,
    val totalEpisodes: Int,
    val seasons: List<Season> = emptyList(),
    val status: String? = null, // "Continuing", "Ended", etc.
    val network: String? = null
)

/**
 * 季度数据
 */
data class Season(
    val id: String,
    val seasonNumber: Int,
    val title: String,
    val episodeCount: Int,
    val posterUrl: String? = null,
    val overview: String? = null,
    val airDate: String? = null,
    val episodes: List<Episode> = emptyList()
)

/**
 * 剧集数据
 */
data class Episode(
    val id: String,
    val episodeNumber: Int,
    val seasonNumber: Int,
    val title: String,
    val overview: String? = null,
    val stillUrl: String? = null,
    val airDate: String? = null,
    val duration: Int? = null,
    val isWatched: Boolean = false,
    val watchProgress: Float = 0f,
    val lastWatchedDate: Long? = null
)

/**
 * 人员信息（演员、导演等）
 */
data class Person(
    val id: String,
    val name: String,
    val role: String? = null, // 角色名或职位
    val profileUrl: String? = null,
    val character: String? = null // 饰演的角色
)

/**
 * 媒体库类型
 */
enum class LibraryType(val displayName: String, val icon: ImageVector) {
    MOVIES("电影", Icons.Default.Movie),
    TV_SHOWS("电视剧", Icons.Default.Tv),
    LIVE_TV("直播", Icons.Default.LiveTv),
    MUSIC("音乐", Icons.Default.MusicNote),
    PHOTOS("照片", Icons.Default.Photo)
}

/**
 * 媒体库
 */
data class MediaLibrary(
    val id: String,
    val name: String,
    val type: LibraryType,
    val itemCount: Int,
    val lastUpdated: Long,
    val recentlyAdded: List<MediaItem> = emptyList()
)

/**
 * 播放历史记录
 */
data class WatchHistory(
    val id: String,
    val mediaId: String,
    val mediaTitle: String,
    val mediaType: MediaType,
    val watchedAt: Long,
    val duration: Int, // 观看时长（秒）
    val progress: Float, // 观看进度 0.0 - 1.0
    val completed: Boolean = false
)

/**
 * 继续观看项目
 */
data class ContinueWatchingItem(
    val mediaItem: MediaItem,
    val progress: Float,
    val lastWatchedDate: Long,
    val nextEpisode: Episode? = null // 对于电视剧，下一集信息
)

/**
 * 推荐内容
 */
data class Recommendation(
    val id: String,
    val title: String,
    val reason: String, // 推荐理由
    val items: List<MediaItem>
)

/**
 * 搜索结果
 */
data class SearchResult(
    val query: String,
    val movies: List<MediaItem> = emptyList(),
    val tvShows: List<TVShow> = emptyList(),
    val people: List<Person> = emptyList(),
    val totalResults: Int = 0
)
