package com.google.chuangke.data.repository

import com.google.chuangke.data.model.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 观看历史和进度管理仓库
 */
class WatchHistoryRepository {
    
    private val _watchHistory = MutableStateFlow<List<WatchHistory>>(emptyList())
    val watchHistory: StateFlow<List<WatchHistory>> = _watchHistory.asStateFlow()
    
    private val _continueWatching = MutableStateFlow<List<ContinueWatchingItem>>(SampleData.continueWatchingItems)
    val continueWatching: StateFlow<List<ContinueWatchingItem>> = _continueWatching.asStateFlow()
    
    private val _mediaProgress = MutableStateFlow<Map<String, Float>>(emptyMap())
    val mediaProgress: StateFlow<Map<String, Float>> = _mediaProgress.asStateFlow()
    
    /**
     * 记录观看历史
     */
    fun recordWatchHistory(
        mediaId: String,
        mediaTitle: String,
        mediaType: MediaType,
        duration: Int,
        progress: Float
    ) {
        val history = WatchHistory(
            id = generateHistoryId(),
            mediaId = mediaId,
            mediaTitle = mediaTitle,
            mediaType = mediaType,
            watchedAt = System.currentTimeMillis(),
            duration = duration,
            progress = progress,
            completed = progress >= 0.9f
        )
        
        val currentHistory = _watchHistory.value.toMutableList()
        // 移除同一媒体的旧记录
        currentHistory.removeAll { it.mediaId == mediaId }
        // 添加新记录到开头
        currentHistory.add(0, history)
        // 限制历史记录数量
        if (currentHistory.size > 100) {
            currentHistory.removeAt(currentHistory.size - 1)
        }
        
        _watchHistory.value = currentHistory
    }
    
    /**
     * 更新媒体播放进度
     */
    fun updateMediaProgress(mediaId: String, progress: Float) {
        val currentProgress = _mediaProgress.value.toMutableMap()
        currentProgress[mediaId] = progress
        _mediaProgress.value = currentProgress
        
        // 如果进度大于5%且小于95%，添加到继续观看列表
        if (progress > 0.05f && progress < 0.95f) {
            updateContinueWatching(mediaId, progress)
        } else if (progress >= 0.95f) {
            // 如果已观看完成，从继续观看列表中移除
            removeContinueWatching(mediaId)
        }
    }
    
    /**
     * 获取媒体播放进度
     */
    fun getMediaProgress(mediaId: String): Float {
        return _mediaProgress.value[mediaId] ?: 0f
    }
    
    /**
     * 更新继续观看列表
     */
    private fun updateContinueWatching(mediaId: String, progress: Float) {
        val currentList = _continueWatching.value.toMutableList()
        
        // 查找是否已存在
        val existingIndex = currentList.indexOfFirst { it.mediaItem.id == mediaId }
        
        if (existingIndex != -1) {
            // 更新现有项目
            val existingItem = currentList[existingIndex]
            currentList[existingIndex] = existingItem.copy(
                progress = progress,
                lastWatchedDate = System.currentTimeMillis()
            )
        } else {
            // 添加新项目（这里需要从媒体库中获取完整的MediaItem）
            // 为了演示，我们使用示例数据
            val mediaItem = SampleData.sampleMovies.find { it.id == mediaId }
                ?: SampleData.sampleTVShows.find { it.mediaItem.id == mediaId }?.mediaItem
            
            mediaItem?.let { item ->
                val newItem = ContinueWatchingItem(
                    mediaItem = item,
                    progress = progress,
                    lastWatchedDate = System.currentTimeMillis()
                )
                currentList.add(0, newItem)
            }
        }
        
        // 按最后观看时间排序
        currentList.sortByDescending { it.lastWatchedDate }
        
        // 限制列表大小
        if (currentList.size > 20) {
            currentList.removeAt(currentList.size - 1)
        }
        
        _continueWatching.value = currentList
    }
    
    /**
     * 从继续观看列表中移除项目
     */
    fun removeContinueWatching(mediaId: String) {
        val currentList = _continueWatching.value.toMutableList()
        currentList.removeAll { it.mediaItem.id == mediaId }
        _continueWatching.value = currentList
    }
    
    /**
     * 获取最近观看的媒体
     */
    fun getRecentlyWatched(limit: Int = 10): List<WatchHistory> {
        return _watchHistory.value.take(limit)
    }
    
    /**
     * 获取观看统计信息
     */
    fun getWatchStats(): WatchStats {
        val history = _watchHistory.value
        val totalWatchTime = history.sumOf { it.duration }
        val completedItems = history.count { it.completed }
        val favoriteGenres = history
            .groupBy { it.mediaType }
            .mapValues { it.value.size }
        
        return WatchStats(
            totalWatchTimeMinutes = totalWatchTime / 60,
            completedItems = completedItems,
            totalItems = history.size,
            favoriteMediaType = favoriteGenres.maxByOrNull { it.value }?.key
        )
    }
    
    /**
     * 清除观看历史
     */
    fun clearWatchHistory() {
        _watchHistory.value = emptyList()
    }
    
    /**
     * 标记媒体为已观看
     */
    fun markAsWatched(mediaId: String) {
        updateMediaProgress(mediaId, 1.0f)
        removeContinueWatching(mediaId)
    }
    
    /**
     * 标记媒体为未观看
     */
    fun markAsUnwatched(mediaId: String) {
        val currentProgress = _mediaProgress.value.toMutableMap()
        currentProgress.remove(mediaId)
        _mediaProgress.value = currentProgress
        removeContinueWatching(mediaId)
    }
    
    private fun generateHistoryId(): String {
        return "history_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }
    
    companion object {
        @Volatile
        private var INSTANCE: WatchHistoryRepository? = null
        
        fun getInstance(): WatchHistoryRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WatchHistoryRepository().also { INSTANCE = it }
            }
        }
    }
}

/**
 * 观看统计数据
 */
data class WatchStats(
    val totalWatchTimeMinutes: Int,
    val completedItems: Int,
    val totalItems: Int,
    val favoriteMediaType: MediaType?
)
