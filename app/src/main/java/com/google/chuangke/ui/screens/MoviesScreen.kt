package com.google.chuangke.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.chuangke.data.repository.SampleData
import com.google.chuangke.ui.components.MediaPosterGrid
import com.google.chuangke.ui.components.PosterSize
import com.google.chuangke.ui.components.TVCard

@Composable
fun MoviesScreen() {
    val movies = remember { SampleData.sampleMovies }
    var selectedGenre by remember { mutableStateOf("全部") }
    var selectedSort by remember { mutableStateOf("最新添加") }

    val genres = remember {
        listOf("全部", "科幻", "动作", "剧情", "喜剧", "动画", "悬疑", "爱情")
    }

    val sortOptions = remember {
        listOf("最新添加", "评分最高", "最近观看", "片名排序")
    }

    val filteredMovies = remember(selectedGenre, selectedSort) {
        var filtered = if (selectedGenre == "全部") {
            movies
        } else {
            movies.filter { it.genres.contains(selectedGenre) }
        }

        when (selectedSort) {
            "评分最高" -> filtered.sortedByDescending { it.rating ?: 0.0 }
            "最近观看" -> filtered.sortedByDescending { it.lastWatchedDate ?: 0L }
            "片名排序" -> filtered.sortedBy { it.title }
            else -> filtered.sortedByDescending { it.addedDate }
        }
    }

    LazyColumn(
        modifier = Modifier.fillMaxSize()
    ) {
        item {
            // Header with title and filters
            Column(
                modifier = Modifier.padding(24.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(bottom = 24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Movie,
                        contentDescription = null,
                        modifier = Modifier.size(32.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(12.dp))
                    Text(
                        text = "电影库",
                        fontSize = 32.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onBackground
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    Text(
                        text = "${filteredMovies.size} 部电影",
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                // Filter chips
                FilterSection(
                    genres = genres,
                    sortOptions = sortOptions,
                    selectedGenre = selectedGenre,
                    selectedSort = selectedSort,
                    onGenreSelected = { selectedGenre = it },
                    onSortSelected = { selectedSort = it }
                )
            }
        }

        item {
            // Movies grid
            MediaPosterGrid(
                items = filteredMovies,
                onItemClick = { /* TODO: 打开电影详情 */ },
                size = PosterSize.MEDIUM
            )
        }
    }
}

/**
 * 筛选区域组件
 */
@Composable
private fun FilterSection(
    genres: List<String>,
    sortOptions: List<String>,
    selectedGenre: String,
    selectedSort: String,
    onGenreSelected: (String) -> Unit,
    onSortSelected: (String) -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 类型筛选
        Column {
            Text(
                text = "类型",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(genres) { genre ->
                    FilterChip(
                        onClick = { onGenreSelected(genre) },
                        label = { Text(genre) },
                        selected = selectedGenre == genre
                    )
                }
            }
        }

        // 排序选项
        Column {
            Text(
                text = "排序",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(sortOptions) { option ->
                    FilterChip(
                        onClick = { onSortSelected(option) },
                        label = { Text(option) },
                        selected = selectedSort == option
                    )
                }
            }
        }
    }
}
