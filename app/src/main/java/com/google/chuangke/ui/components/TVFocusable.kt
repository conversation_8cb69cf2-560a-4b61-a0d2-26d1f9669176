package com.google.chuangke.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.*
import androidx.compose.ui.unit.dp

/**
 * TV专用的可聚焦组件，提供遥控器友好的交互体验
 */
@Composable
fun TVFocusable(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    focusedBackgroundColor: Color = MaterialTheme.colorScheme.primaryContainer,
    unfocusedBackgroundColor: Color = Color.Transparent,
    focusedBorderColor: Color = MaterialTheme.colorScheme.primary,
    borderWidth: androidx.compose.ui.unit.Dp = 2.dp,
    cornerRadius: androidx.compose.ui.unit.Dp = 8.dp,
    content: @Composable BoxScope.(isFocused: Boolean) -> Unit
) {
    var isFocused by remember { mutableStateOf(false) }

    Box(
        modifier = modifier
            .clip(RoundedCornerShape(cornerRadius))
            .background(
                if (isFocused && enabled) focusedBackgroundColor 
                else unfocusedBackgroundColor
            )
            .then(
                if (isFocused && enabled) {
                    Modifier.border(
                        width = borderWidth,
                        color = focusedBorderColor,
                        shape = RoundedCornerShape(cornerRadius)
                    )
                } else Modifier
            )
            .clickable(enabled = enabled) { onClick() }
            .focusable(enabled = enabled)
            .onFocusChanged { isFocused = it.isFocused }
            .onKeyEvent { keyEvent ->
                if (enabled && keyEvent.type == KeyEventType.KeyUp) {
                    when (keyEvent.key) {
                        Key.DirectionCenter, Key.Enter -> {
                            onClick()
                            true
                        }
                        else -> false
                    }
                } else false
            }
    ) {
        content(isFocused && enabled)
    }
}

/**
 * TV专用的卡片组件，带有焦点效果
 */
@Composable
fun TVCard(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable BoxScope.(isFocused: Boolean) -> Unit
) {
    TVFocusable(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        focusedBackgroundColor = MaterialTheme.colorScheme.surfaceVariant,
        unfocusedBackgroundColor = MaterialTheme.colorScheme.surface,
        focusedBorderColor = MaterialTheme.colorScheme.primary,
        borderWidth = 3.dp,
        cornerRadius = 12.dp,
        content = content
    )
}

/**
 * TV专用的按钮组件，带有焦点效果
 */
@Composable
fun TVButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable BoxScope.(isFocused: Boolean) -> Unit
) {
    TVFocusable(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        focusedBackgroundColor = MaterialTheme.colorScheme.primary,
        unfocusedBackgroundColor = MaterialTheme.colorScheme.primaryContainer,
        focusedBorderColor = MaterialTheme.colorScheme.onPrimary,
        borderWidth = 2.dp,
        cornerRadius = 8.dp,
        content = content
    )
}
