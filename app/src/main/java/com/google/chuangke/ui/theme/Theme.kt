package com.google.chuangke.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable

private val DarkColorScheme = darkColorScheme(
    primary = Purple80,
    onPrimary = OnDarkPrimary,
    primaryContainer = Purple20,
    onPrimaryContainer = OnDarkBackground,
    secondary = Teal80,
    onSecondary = OnDarkSecondary,
    secondaryContainer = Teal40,
    onSecondaryContainer = OnDarkBackground,
    tertiary = Red80,
    onTertiary = OnDarkPrimary,
    tertiaryContainer = Red40,
    onTertiaryContainer = OnDarkBackground,
    error = Red80,
    onError = OnDarkPrimary,
    errorContainer = Red40,
    onErrorContainer = OnDarkBackground,
    background = DarkBackground,
    onBackground = OnDarkBackground,
    surface = DarkSurface,
    onSurface = OnDarkSurface,
    surfaceVariant = DarkSurfaceVariant,
    onSurfaceVariant = OnDarkSurfaceVariant,
    outline = DarkOutline,
    outlineVariant = DarkOutlineVariant,
    inverseSurface = OnDarkBackground,
    inverseOnSurface = DarkBackground,
    inversePrimary = Purple40
)

private val LightColorScheme = lightColorScheme(
    primary = Purple40,
    onPrimary = OnDarkBackground,
    primaryContainer = Purple80,
    onPrimaryContainer = OnDarkPrimary,
    secondary = Teal40,
    onSecondary = OnDarkBackground,
    secondaryContainer = Teal80,
    onSecondaryContainer = OnDarkPrimary,
    tertiary = Red40,
    onTertiary = OnDarkBackground,
    tertiaryContainer = Red80,
    onTertiaryContainer = OnDarkPrimary,
    error = Red40,
    onError = OnDarkBackground,
    errorContainer = Red80,
    onErrorContainer = OnDarkPrimary,
    background = OnDarkBackground,
    onBackground = OnDarkPrimary,
    surface = OnDarkBackground,
    onSurface = OnDarkPrimary,
    surfaceVariant = OnDarkSurfaceVariant,
    onSurfaceVariant = DarkSurfaceVariant,
    outline = DarkOutlineVariant,
    outlineVariant = DarkOutline,
    inverseSurface = DarkBackground,
    inverseOnSurface = OnDarkBackground,
    inversePrimary = Purple80
)

@Composable
fun LitchiTVTheme(
    darkTheme: Boolean = true, // 默认使用暗黑主题，适合TV
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
