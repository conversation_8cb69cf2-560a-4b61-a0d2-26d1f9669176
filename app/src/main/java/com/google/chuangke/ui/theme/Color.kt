package com.google.chuangke.ui.theme

import androidx.compose.ui.graphics.Color

// Primary colors
val Purple80 = Color(0xFFBB86FC)
val Purple40 = Color(0xFF6200EE)
val Purple20 = Color(0xFF3700B3)

// Secondary colors
val Teal80 = Color(0xFF03DAC6)
val Teal40 = Color(0xFF018786)

// Error colors
val Red80 = Color(0xFFCF6679)
val Red40 = Color(0xFFB00020)

// Background colors for dark theme
val DarkBackground = Color(0xFF121212)
val DarkSurface = Color(0xFF1E1E1E)
val DarkSurfaceVariant = Color(0xFF2C2C2C)

// Text colors
val OnDarkPrimary = Color(0xFF000000)
val OnDarkSecondary = Color(0xFF000000)
val OnDarkBackground = Color(0xFFFFFFFF)
val OnDarkSurface = Color(0xFFFFFFFF)
val OnDarkSurfaceVariant = Color(0xFFCCCCCC)

// Outline colors
val DarkOutline = Color(0xFF666666)
val DarkOutlineVariant = Color(0xFF444444)
