package com.google.chuangke.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun HomeScreen() {
    val recommendedMovies = remember {
        listOf("推荐电影 1", "推荐电影 2", "推荐电影 3", "推荐电影 4", "推荐电影 5")
    }
    
    val popularTVShows = remember {
        listOf("热门剧集 1", "热门剧集 2", "热门剧集 3", "热门剧集 4", "热门剧集 5")
    }
    
    val liveChannels = remember {
        listOf("直播频道 1", "直播频道 2", "直播频道 3", "直播频道 4")
    }

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        verticalArrangement = Arrangement.spacedBy(32.dp)
    ) {
        item {
            // Welcome section
            Column {
                Text(
                    text = "欢迎使用荔枝TV",
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onBackground
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "发现精彩内容，享受观影时光",
                    fontSize = 18.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        item {
            // Recommended movies section
            ContentSection(
                title = "推荐电影",
                items = recommendedMovies
            )
        }

        item {
            // Popular TV shows section
            ContentSection(
                title = "热门剧集",
                items = popularTVShows
            )
        }

        item {
            // Live channels section
            ContentSection(
                title = "直播频道",
                items = liveChannels
            )
        }
    }
}

@Composable
private fun ContentSection(
    title: String,
    items: List<String>
) {
    Column {
        Text(
            text = title,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onBackground,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(items) { item ->
                ContentCard(title = item)
            }
        }
    }
}

@Composable
private fun ContentCard(title: String) {
    Card(
        modifier = Modifier
            .width(200.dp)
            .height(120.dp),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = title,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}
