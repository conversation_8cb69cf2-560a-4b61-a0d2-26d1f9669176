package com.google.chuangke.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.chuangke.data.model.MediaItem
import com.google.chuangke.data.repository.SampleData
import com.google.chuangke.ui.components.MediaPosterRow
import com.google.chuangke.ui.components.PosterSize
import com.google.chuangke.ui.components.TVCard

@Composable
fun HomeScreen() {
    val continueWatchingItems = remember { SampleData.continueWatchingItems }
    val recentlyAdded = remember { SampleData.recentlyAdded }
    val recommendations = remember { SampleData.recommendations }
    val mediaLibraries = remember { SampleData.mediaLibraries }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        item {
            // Hero section with featured content
            HeroSection(
                featuredItem = SampleData.sampleMovies.first(),
                onPlayClick = { /* TODO: 播放 */ },
                onInfoClick = { /* TODO: 详情 */ }
            )
        }

        // 继续观看
        if (continueWatchingItems.isNotEmpty()) {
            item {
                ContinueWatchingSection(
                    items = continueWatchingItems,
                    onItemClick = { /* TODO: 继续播放 */ }
                )
            }
        }

        // 最近添加
        item {
            MediaPosterRow(
                title = "最近添加",
                items = recentlyAdded,
                onItemClick = { /* TODO: 播放 */ },
                onSeeAllClick = { /* TODO: 查看全部 */ },
                size = PosterSize.MEDIUM
            )
        }

        // 推荐内容
        items(recommendations) { recommendation ->
            MediaPosterRow(
                title = recommendation.title,
                items = recommendation.items,
                onItemClick = { /* TODO: 播放 */ },
                size = PosterSize.MEDIUM
            )
        }

        // 媒体库快速访问
        item {
            MediaLibrariesSection(
                libraries = mediaLibraries,
                onLibraryClick = { /* TODO: 打开媒体库 */ }
            )
        }

        // 底部间距
        item {
            Spacer(modifier = Modifier.height(24.dp))
        }
    }
}

/**
 * 主页英雄区域 - 展示特色内容
 */
@Composable
private fun HeroSection(
    featuredItem: MediaItem,
    onPlayClick: () -> Unit,
    onInfoClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(400.dp)
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        MaterialTheme.colorScheme.background
                    )
                )
            )
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧内容信息
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "特色推荐",
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = featuredItem.title,
                    fontSize = 48.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onBackground
                )
                Spacer(modifier = Modifier.height(12.dp))
                featuredItem.overview?.let { overview ->
                    Text(
                        text = overview,
                        fontSize = 18.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 3
                    )
                }
                Spacer(modifier = Modifier.height(24.dp))

                // 操作按钮
                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Button(
                        onClick = onPlayClick,
                        modifier = Modifier.height(48.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = if (featuredItem.watchProgress > 0) "继续播放" else "立即播放",
                            fontSize = 16.sp
                        )
                    }

                    OutlinedButton(
                        onClick = onInfoClick,
                        modifier = Modifier.height(48.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "详细信息",
                            fontSize = 16.sp
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.width(48.dp))

            // 右侧海报
            Box(
                modifier = Modifier
                    .width(300.dp)
                    .height(450.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant)
            ) {
                // 模拟海报 - 实际应用中使用AsyncImage
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.primary.copy(alpha = 0.3f),
                                    MaterialTheme.colorScheme.secondary.copy(alpha = 0.5f)
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Movie,
                        contentDescription = null,
                        modifier = Modifier.size(80.dp),
                        tint = Color.White.copy(alpha = 0.7f)
                    )
                }

                // 观看进度
                if (featuredItem.watchProgress > 0f) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                            .height(6.dp)
                            .background(Color.Black.copy(alpha = 0.3f))
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth(featuredItem.watchProgress)
                                .fillMaxHeight()
                                .background(MaterialTheme.colorScheme.primary)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 继续观看区域
 */
@Composable
private fun ContinueWatchingSection(
    items: List<com.google.chuangke.data.model.ContinueWatchingItem>,
    onItemClick: (com.google.chuangke.data.model.ContinueWatchingItem) -> Unit
) {
    Column {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.PlayArrow,
                contentDescription = null,
                modifier = Modifier.size(28.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                text = "继续观看",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground
            )
        }

        LazyRow(
            contentPadding = PaddingValues(horizontal = 24.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(items) { item ->
                ContinueWatchingCard(
                    item = item,
                    onClick = { onItemClick(item) }
                )
            }
        }
    }
}

/**
 * 继续观看卡片
 */
@Composable
private fun ContinueWatchingCard(
    item: com.google.chuangke.data.model.ContinueWatchingItem,
    onClick: () -> Unit
) {
    TVCard(
        onClick = onClick,
        modifier = Modifier.size(280.dp, 200.dp)
    ) { isFocused ->
        Column {
            // 缩略图区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(140.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant)
            ) {
                // 模拟缩略图
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Brush.horizontalGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.primary.copy(alpha = 0.3f),
                                    MaterialTheme.colorScheme.tertiary.copy(alpha = 0.4f)
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = Color.White.copy(alpha = 0.8f)
                    )
                }

                // 观看进度条
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth()
                        .height(4.dp)
                        .background(Color.Black.copy(alpha = 0.3f))
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(item.progress)
                            .fillMaxHeight()
                            .background(MaterialTheme.colorScheme.primary)
                    )
                }

                // 播放按钮覆盖层
                if (isFocused) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.Black.copy(alpha = 0.3f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "继续播放",
                            modifier = Modifier.size(64.dp),
                            tint = Color.White
                        )
                    }
                }
            }

            // 标题和进度信息
            Column(
                modifier = Modifier.padding(8.dp)
            ) {
                Text(
                    text = item.mediaItem.title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (isFocused) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    },
                    maxLines = 1
                )
                Text(
                    text = "${(item.progress * 100).toInt()}% 已观看",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

/**
 * 媒体库快速访问区域
 */
@Composable
private fun MediaLibrariesSection(
    libraries: List<com.google.chuangke.data.model.MediaLibrary>,
    onLibraryClick: (com.google.chuangke.data.model.MediaLibrary) -> Unit
) {
    Column {
        Text(
            text = "我的媒体库",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onBackground,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 8.dp)
        )

        LazyRow(
            contentPadding = PaddingValues(horizontal = 24.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(libraries) { library ->
                MediaLibraryCard(
                    library = library,
                    onClick = { onLibraryClick(library) }
                )
            }
        }
    }
}

/**
 * 媒体库卡片
 */
@Composable
private fun MediaLibraryCard(
    library: com.google.chuangke.data.model.MediaLibrary,
    onClick: () -> Unit
) {
    TVCard(
        onClick = onClick,
        modifier = Modifier.size(200.dp, 120.dp)
    ) { isFocused ->
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = library.type.icon,
                contentDescription = null,
                modifier = Modifier.size(32.dp),
                tint = if (isFocused) {
                    MaterialTheme.colorScheme.onPrimaryContainer
                } else {
                    MaterialTheme.colorScheme.primary
                }
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column {
                Text(
                    text = library.name,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = if (isFocused) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    }
                )
                Text(
                    text = "${library.itemCount} 项目",
                    fontSize = 14.sp,
                    color = if (isFocused) {
                        MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f)
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
        }
    }
}
