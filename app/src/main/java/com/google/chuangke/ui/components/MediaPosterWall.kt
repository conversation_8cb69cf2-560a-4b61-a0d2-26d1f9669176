package com.google.chuangke.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.chuangke.data.model.MediaItem
import com.google.chuangke.data.model.MediaType

/**
 * 海报尺寸枚举
 */
enum class PosterSize(val width: androidx.compose.ui.unit.Dp, val height: androidx.compose.ui.unit.Dp) {
    SMALL(120.dp, 180.dp),
    MEDIUM(160.dp, 240.dp),
    LARGE(200.dp, 300.dp),
    EXTRA_LARGE(240.dp, 360.dp)
}

/**
 * 媒体海报卡片
 */
@Composable
fun MediaPosterCard(
    mediaItem: MediaItem,
    size: PosterSize = PosterSize.MEDIUM,
    onClick: (MediaItem) -> Unit,
    modifier: Modifier = Modifier,
    showTitle: Boolean = true,
    showProgress: Boolean = true,
    showWatchedIndicator: Boolean = true
) {
    TVCard(
        onClick = { onClick(mediaItem) },
        modifier = modifier.size(size.width, size.height + if (showTitle) 40.dp else 0.dp)
    ) { isFocused ->
        Column {
            // 海报图片区域
            Box(
                modifier = Modifier
                    .size(size.width, size.height)
                    .clip(RoundedCornerShape(8.dp))
                    .background(MaterialTheme.colorScheme.surfaceVariant)
            ) {
                // 模拟海报图片 - 实际应用中这里会是AsyncImage
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.primary.copy(alpha = 0.3f),
                                    MaterialTheme.colorScheme.secondary.copy(alpha = 0.5f)
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = when (mediaItem.type) {
                            MediaType.MOVIE -> Icons.Default.Movie
                            MediaType.TV_SHOW -> Icons.Default.Tv
                            MediaType.LIVE_TV -> Icons.Default.LiveTv
                            else -> Icons.Default.PlayArrow
                        },
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = Color.White.copy(alpha = 0.7f)
                    )
                }
                
                // 观看进度条
                if (showProgress && mediaItem.watchProgress > 0f) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                            .height(4.dp)
                            .background(Color.Black.copy(alpha = 0.3f))
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth(mediaItem.watchProgress)
                                .fillMaxHeight()
                                .background(MaterialTheme.colorScheme.primary)
                        )
                    }
                }
                
                // 已观看标识
                if (showWatchedIndicator && mediaItem.isWatched) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(8.dp)
                            .size(24.dp)
                            .background(
                                MaterialTheme.colorScheme.primary,
                                RoundedCornerShape(12.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = "已观看",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                }
                
                // 评分标识
                mediaItem.rating?.let { rating ->
                    Box(
                        modifier = Modifier
                            .align(Alignment.TopStart)
                            .padding(8.dp)
                            .background(
                                Color.Black.copy(alpha = 0.7f),
                                RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 6.dp, vertical = 2.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Star,
                                contentDescription = null,
                                modifier = Modifier.size(12.dp),
                                tint = Color.Yellow
                            )
                            Spacer(modifier = Modifier.width(2.dp))
                            Text(
                                text = String.format("%.1f", rating),
                                fontSize = 10.sp,
                                color = Color.White,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
                
                // 焦点时的播放按钮
                if (isFocused) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(Color.Black.copy(alpha = 0.3f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "播放",
                            modifier = Modifier.size(64.dp),
                            tint = Color.White
                        )
                    }
                }
            }
            
            // 标题
            if (showTitle) {
                Text(
                    text = mediaItem.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = if (isFocused) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    },
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .padding(top = 8.dp)
                        .width(size.width)
                )
            }
        }
    }
}

/**
 * 水平滚动的媒体海报行
 */
@Composable
fun MediaPosterRow(
    title: String,
    items: List<MediaItem>,
    onItemClick: (MediaItem) -> Unit,
    onSeeAllClick: (() -> Unit)? = null,
    size: PosterSize = PosterSize.MEDIUM,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 标题行
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            onSeeAllClick?.let {
                TextButton(onClick = it) {
                    Text(
                        text = "查看全部",
                        fontSize = 16.sp,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Icon(
                        imageVector = Icons.Default.ArrowForward,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
        
        // 海报行
        LazyRow(
            contentPadding = PaddingValues(horizontal = 24.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(items) { item ->
                MediaPosterCard(
                    mediaItem = item,
                    size = size,
                    onClick = onItemClick
                )
            }
        }
    }
}

/**
 * 网格布局的媒体海报墙
 */
@Composable
fun MediaPosterGrid(
    items: List<MediaItem>,
    onItemClick: (MediaItem) -> Unit,
    size: PosterSize = PosterSize.MEDIUM,
    modifier: Modifier = Modifier
) {
    LazyVerticalGrid(
        columns = GridCells.Adaptive(minSize = size.width + 16.dp),
        contentPadding = PaddingValues(24.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        modifier = modifier
    ) {
        items(items) { item ->
            MediaPosterCard(
                mediaItem = item,
                size = size,
                onClick = onItemClick
            )
        }
    }
}
