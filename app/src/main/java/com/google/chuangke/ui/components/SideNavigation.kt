package com.google.chuangke.ui.components

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.key.*
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.google.chuangke.navigation.NavigationItem
import com.google.chuangke.navigation.Screen
import com.google.chuangke.navigation.navigationItems

@Composable
fun SideNavigation(
    isExpanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    selectedScreen: Screen,
    onScreenSelected: (Screen) -> Unit,
    onNavigationFocused: () -> Unit,
    modifier: Modifier = Modifier
) {
    val navigationWidth by animateDpAsState(
        targetValue = if (isExpanded) 240.dp else 80.dp,
        animationSpec = tween(durationMillis = 300),
        label = "navigation_width"
    )

    Surface(
        modifier = modifier
            .width(navigationWidth)
            .fillMaxHeight()
            .zIndex(10f) // 确保在最上层
            .shadow(
                elevation = 16.dp,
                shape = RoundedCornerShape(topEnd = 0.dp, bottomEnd = 0.dp)
            )
            .onFocusChanged { focusState ->
                if (focusState.hasFocus || focusState.isFocused) {
                    onNavigationFocused()
                }
            },
        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.98f), // 更高的透明度
        tonalElevation = 0.dp // 使用shadow代替elevation
    ) {
        // 添加渐变背景效果
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Brush.horizontalGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.surface.copy(alpha = 0.95f),
                            MaterialTheme.colorScheme.surface.copy(alpha = 0.85f)
                        ),
                        startX = 0f,
                        endX = Float.POSITIVE_INFINITY
                    )
                )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(8.dp)
            ) {
                // App logo/title area
                NavigationHeader(
                    isExpanded = isExpanded,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // Navigation items
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    items(navigationItems) { item ->
                        NavigationItemRow(
                            item = item,
                            isSelected = selectedScreen == item.screen,
                            isExpanded = isExpanded,
                            onClick = {
                                onScreenSelected(item.screen)
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun NavigationHeader(
    isExpanded: Boolean,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(56.dp),
        contentAlignment = if (isExpanded) Alignment.CenterStart else Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = if (isExpanded) Arrangement.Start else Arrangement.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = if (isExpanded) 16.dp else 0.dp)
        ) {
            // App icon
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(MaterialTheme.colorScheme.primary),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Tv,
                    contentDescription = "荔枝TV",
                    tint = MaterialTheme.colorScheme.onPrimary,
                    modifier = Modifier.size(20.dp)
                )
            }

            // App title (only when expanded)
            if (isExpanded) {
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "荔枝TV",
                    color = MaterialTheme.colorScheme.onSurface,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
private fun NavigationItemRow(
    item: NavigationItem,
    isSelected: Boolean,
    isExpanded: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isFocused by remember { mutableStateOf(false) }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(
                when {
                    isSelected -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.8f)
                    isFocused -> MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.6f)
                    else -> Color.Transparent
                }
            )
            .clickable { onClick() }
            .focusable()
            .onFocusChanged { isFocused = it.isFocused }
            .onKeyEvent { keyEvent ->
                if (keyEvent.type == KeyEventType.KeyUp) {
                    when (keyEvent.key) {
                        Key.DirectionCenter, Key.Enter -> {
                            onClick()
                            true
                        }

                        else -> false
                    }
                } else false
            },
        contentAlignment = if (isExpanded) Alignment.CenterStart else Alignment.Center
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = if (isExpanded) 16.dp else 0.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = if (isExpanded) Arrangement.Start else Arrangement.Center
        ) {
            // Icon with enhanced visual feedback
            Box(
                modifier = Modifier
                    .size(if (isFocused) 28.dp else 24.dp)
                    .clip(RoundedCornerShape(6.dp))
                    .background(
                        if (isSelected && !isExpanded) {
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
                        } else Color.Transparent
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = item.icon,
                    contentDescription = item.title,
                    tint = when {
                        isSelected -> MaterialTheme.colorScheme.primary
                        isFocused -> MaterialTheme.colorScheme.onSurface
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    },
                    modifier = Modifier.size(if (isFocused) 22.dp else 20.dp)
                )
            }

            // Text (only when expanded)
            if (isExpanded) {
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = item.title,
                    color = when {
                        isSelected -> MaterialTheme.colorScheme.onPrimaryContainer
                        isFocused -> MaterialTheme.colorScheme.onSurface
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    },
                    fontSize = 16.sp,
                    fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
                )
            }
        }

        // Selected indicator for collapsed state
        if (isSelected && !isExpanded) {
            Box(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .width(3.dp)
                    .height(24.dp)
                    .background(
                        MaterialTheme.colorScheme.primary,
                        RoundedCornerShape(topStart = 2.dp, bottomStart = 2.dp)
                    )
            )
        }
    }
}
