package com.google.chuangke.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.google.chuangke.navigation.Screen
import com.google.chuangke.navigation.navigationItems
import com.google.chuangke.ui.components.SideNavigation

@Composable
fun MainScreen() {
    val navController = rememberNavController()
    var isNavigationExpanded by remember { mutableStateOf(true) }
    var selectedScreen by remember { mutableStateOf(Screen.HOME) }

    Row(
        modifier = Modifier.fillMaxSize()
    ) {
        // Left side navigation
        SideNavigation(
            isExpanded = isNavigationExpanded,
            onExpandedChange = { isNavigationExpanded = it },
            selectedScreen = selectedScreen,
            onScreenSelected = { screen ->
                selectedScreen = screen
                val route = navigationItems.find { it.screen == screen }?.route ?: "home"
                navController.navigate(route) {
                    // Clear back stack to avoid building up a large stack
                    popUpTo(navController.graph.startDestinationId) {
                        saveState = true
                    }
                    launchSingleTop = true
                    restoreState = true
                }
            }
        )

        // Right side content area
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = MaterialTheme.colorScheme.background
        ) {
            ContentArea(
                navController = navController,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

@Composable
private fun ContentArea(
    navController: NavHostController,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = "home",
        modifier = modifier
    ) {
        composable("search") {
            SearchScreen()
        }
        composable("home") {
            HomeScreen()
        }
        composable("live") {
            LiveScreen()
        }
        composable("movies") {
            MoviesScreen()
        }
        composable("tv_shows") {
            TVShowsScreen()
        }
        composable("favorites") {
            FavoritesScreen()
        }
        composable("settings") {
            SettingsScreen()
        }
    }
}
