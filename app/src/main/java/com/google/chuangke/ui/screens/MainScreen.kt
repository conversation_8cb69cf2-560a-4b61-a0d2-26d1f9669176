package com.google.chuangke.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.google.chuangke.navigation.Screen
import com.google.chuangke.navigation.navigationItems
import com.google.chuangke.ui.components.SideNavigation

@Composable
fun MainScreen() {
    val navController = rememberNavController()
    var selectedScreen by remember { mutableStateOf(Screen.HOME) }
    var isContentFocused by remember { mutableStateOf(false) }
    val contentFocusRequester = remember { FocusRequester() }

    // 导航栏状态：当内容区域有焦点时自动收缩
    val isNavigationExpanded by remember {
        derivedStateOf { !isContentFocused }
    }

    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        // Right side content area (full screen)
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .focusRequester(contentFocusRequester)
                .onFocusChanged { focusState ->
                    isContentFocused = focusState.hasFocus || focusState.isFocused
                },
            color = MaterialTheme.colorScheme.background
        ) {
            ContentArea(
                navController = navController,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(
                        start = if (isNavigationExpanded) 240.dp else 80.dp
                    ),
                onContentFocused = {
                    isContentFocused = true
                }
            )
        }

        // Left side navigation (overlay)
        SideNavigation(
            isExpanded = isNavigationExpanded,
            onExpandedChange = { /* 不允许手动切换，由焦点自动控制 */ },
            selectedScreen = selectedScreen,
            onScreenSelected = { screen ->
                selectedScreen = screen
                val route = navigationItems.find { it.screen == screen }?.route ?: "home"
                navController.navigate(route) {
                    // Clear back stack to avoid building up a large stack
                    popUpTo(navController.graph.startDestinationId) {
                        saveState = true
                    }
                    launchSingleTop = true
                    restoreState = true
                }
                // 导航后将焦点移到内容区域
                contentFocusRequester.requestFocus()
            },
            onNavigationFocused = {
                isContentFocused = false
            },
            modifier = Modifier.fillMaxHeight()
        )
    }
}

@Composable
private fun ContentArea(
    navController: NavHostController,
    onContentFocused: () -> Unit,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = "home",
        modifier = modifier
            .onFocusChanged { focusState ->
                if (focusState.hasFocus || focusState.isFocused) {
                    onContentFocused()
                }
            }
    ) {
        composable("search") {
            SearchScreen()
        }
        composable("home") {
            HomeScreen()
        }
        composable("live") {
            LiveScreen()
        }
        composable("movies") {
            MoviesScreen()
        }
        composable("tv_shows") {
            TVShowsScreen()
        }
        composable("favorites") {
            FavoritesScreen()
        }
        composable("settings") {
            SettingsScreen()
        }
    }
}
