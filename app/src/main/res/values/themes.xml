<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
<!--    <style name="Theme.LitchiTV" parent="Theme.Material3.DayNight.NoActionBar">-->
<!--        &lt;!&ndash; Customize your theme here. &ndash;&gt;-->
<!--        <item name="colorPrimary">@color/md_theme_primary</item>-->
<!--        <item name="colorOnPrimary">@color/md_theme_onPrimary</item>-->
<!--        <item name="colorPrimaryContainer">@color/md_theme_primaryContainer</item>-->
<!--        <item name="colorOnPrimaryContainer">@color/md_theme_onPrimaryContainer</item>-->
<!--        <item name="colorSecondary">@color/md_theme_secondary</item>-->
<!--        <item name="colorOnSecondary">@color/md_theme_onSecondary</item>-->
<!--        <item name="colorSecondaryContainer">@color/md_theme_secondaryContainer</item>-->
<!--        <item name="colorOnSecondaryContainer">@color/md_theme_onSecondaryContainer</item>-->
<!--        <item name="colorTertiary">@color/md_theme_tertiary</item>-->
<!--        <item name="colorOnTertiary">@color/md_theme_onTertiary</item>-->
<!--        <item name="colorTertiaryContainer">@color/md_theme_tertiaryContainer</item>-->
<!--        <item name="colorOnTertiaryContainer">@color/md_theme_onTertiaryContainer</item>-->
<!--        <item name="colorError">@color/md_theme_error</item>-->
<!--        <item name="colorErrorContainer">@color/md_theme_errorContainer</item>-->
<!--        <item name="colorOnError">@color/md_theme_onError</item>-->
<!--        <item name="colorOnErrorContainer">@color/md_theme_onErrorContainer</item>-->
<!--        <item name="android:colorBackground">@color/md_theme_background</item>-->
<!--        <item name="colorOnBackground">@color/md_theme_onBackground</item>-->
<!--        <item name="colorSurface">@color/md_theme_surface</item>-->
<!--        <item name="colorOnSurface">@color/md_theme_onSurface</item>-->
<!--        <item name="colorSurfaceVariant">@color/md_theme_surfaceVariant</item>-->
<!--        <item name="colorOnSurfaceVariant">@color/md_theme_onSurfaceVariant</item>-->
<!--        <item name="colorOutline">@color/md_theme_outline</item>-->
<!--        <item name="colorOutlineVariant">@color/md_theme_outlineVariant</item>-->
<!--        <item name="colorSurfaceInverse">@color/md_theme_inverseSurface</item>-->
<!--        <item name="colorOnSurfaceInverse">@color/md_theme_inverseOnSurface</item>-->
<!--        <item name="colorPrimaryInverse">@color/md_theme_inversePrimary</item>-->
<!--    </style>-->
</resources>
