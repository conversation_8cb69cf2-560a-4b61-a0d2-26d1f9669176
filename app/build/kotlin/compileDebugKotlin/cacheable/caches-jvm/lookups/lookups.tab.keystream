  Bundle android.app.Activity  
LitchiTVTheme android.app.Activity  
MainScreen android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Surface android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  Bundle android.content.Context  
LitchiTVTheme android.content.Context  
MainScreen android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  fillMaxSize android.content.Context  onCreate android.content.Context  
setContent android.content.Context  Bundle android.content.ContextWrapper  
LitchiTVTheme android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  onCreate android.content.ContextWrapper  
setContent android.content.ContextWrapper  Bundle 
android.os  Bundle  android.view.ContextThemeWrapper  
LitchiTVTheme  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  
LitchiTVTheme #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
LitchiTVTheme -androidx.activity.ComponentActivity.Companion  
MainScreen -androidx.activity.ComponentActivity.Companion  
MaterialTheme -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  Surface -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  getFILLMaxSize -androidx.activity.ComponentActivity.Companion  getFillMaxSize -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  FavoritesScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  
LiveScreen /androidx.compose.animation.AnimatedContentScope  MoviesScreen /androidx.compose.animation.AnimatedContentScope  SearchScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  
TVShowsScreen /androidx.compose.animation.AnimatedContentScope  	TweenSpec androidx.compose.animation.core  animateDpAsState androidx.compose.animation.core  tween androidx.compose.animation.core  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  	focusable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentArea "androidx.compose.foundation.layout  ContentCard "androidx.compose.foundation.layout  ContentSection "androidx.compose.foundation.layout  DarkMode "androidx.compose.foundation.layout  Divider "androidx.compose.foundation.layout  FavoriteItemCard "androidx.compose.foundation.layout  FavoritesScreen "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  	GridCells "androidx.compose.foundation.layout  Help "androidx.compose.foundation.layout  HighQuality "androidx.compose.foundation.layout  
HomeScreen "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  Key "androidx.compose.foundation.layout  KeyEventType "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  LazyVerticalGrid "androidx.compose.foundation.layout  LiveChannelCard "androidx.compose.foundation.layout  
LiveScreen "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  	MovieCard "androidx.compose.foundation.layout  MoviesScreen "androidx.compose.foundation.layout  NavigationItemRow "androidx.compose.foundation.layout  NavigationToggleButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Screen "androidx.compose.foundation.layout  SearchResultCard "androidx.compose.foundation.layout  SearchScreen "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  SettingsItem "androidx.compose.foundation.layout  SettingsScreen "androidx.compose.foundation.layout  SettingsSection "androidx.compose.foundation.layout  SideNavigation "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  
TVShowCard "androidx.compose.foundation.layout  
TVShowsScreen "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  Update "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
composable "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  find "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  key "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  navigationItems "androidx.compose.foundation.layout  
onKeyEvent "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  type "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Start .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  FavoriteBorder +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Menu +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Search +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  content +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  
getCONTENT +androidx.compose.foundation.layout.BoxScope  
getContent +androidx.compose.foundation.layout.BoxScope  getFILLMaxSize +androidx.compose.foundation.layout.BoxScope  getFILLMaxWidth +androidx.compose.foundation.layout.BoxScope  getFillMaxSize +androidx.compose.foundation.layout.BoxScope  getFillMaxWidth +androidx.compose.foundation.layout.BoxScope  	getHEIGHT +androidx.compose.foundation.layout.BoxScope  	getHeight +androidx.compose.foundation.layout.BoxScope  
getPADDING +androidx.compose.foundation.layout.BoxScope  
getPadding +androidx.compose.foundation.layout.BoxScope  getSIZE +androidx.compose.foundation.layout.BoxScope  getSize +androidx.compose.foundation.layout.BoxScope  getWIDTH +androidx.compose.foundation.layout.BoxScope  getWidth +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  invoke +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ContentCard .androidx.compose.foundation.layout.ColumnScope  DarkMode .androidx.compose.foundation.layout.ColumnScope  Divider .androidx.compose.foundation.layout.ColumnScope  Favorite .androidx.compose.foundation.layout.ColumnScope  FavoriteBorder .androidx.compose.foundation.layout.ColumnScope  FavoriteItemCard .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  	GridCells .androidx.compose.foundation.layout.ColumnScope  Help .androidx.compose.foundation.layout.ColumnScope  HighQuality .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  LazyVerticalGrid .androidx.compose.foundation.layout.ColumnScope  LiveChannelCard .androidx.compose.foundation.layout.ColumnScope  LiveTv .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Movie .androidx.compose.foundation.layout.ColumnScope  	MovieCard .androidx.compose.foundation.layout.ColumnScope  NavigationItemRow .androidx.compose.foundation.layout.ColumnScope  NavigationToggleButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Search .androidx.compose.foundation.layout.ColumnScope  SearchResultCard .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  SettingsItem .androidx.compose.foundation.layout.ColumnScope  SettingsSection .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Star .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  
TVShowCard .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  Tv .androidx.compose.foundation.layout.ColumnScope  Update .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  	emptyList .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getEMPTYList .androidx.compose.foundation.layout.ColumnScope  getEmptyList .androidx.compose.foundation.layout.ColumnScope  getFILLMaxSize .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxSize .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getISEmpty .androidx.compose.foundation.layout.ColumnScope  
getISNotEmpty .androidx.compose.foundation.layout.ColumnScope  
getIsEmpty .androidx.compose.foundation.layout.ColumnScope  
getIsNotEmpty .androidx.compose.foundation.layout.ColumnScope  	getLISTOf .androidx.compose.foundation.layout.ColumnScope  	getListOf .androidx.compose.foundation.layout.ColumnScope  getNAVIGATIONItems .androidx.compose.foundation.layout.ColumnScope  getNavigationItems .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getWIDTH .androidx.compose.foundation.layout.ColumnScope  getWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  isEmpty .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  navigationItems .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  ContentArea +androidx.compose.foundation.layout.RowScope  Favorite +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  LiveTv +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Menu +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Movie +androidx.compose.foundation.layout.RowScope  
PaddingValues +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  SideNavigation +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Star +androidx.compose.foundation.layout.RowScope  Surface +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  Tv +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  find +androidx.compose.foundation.layout.RowScope  getFILLMaxSize +androidx.compose.foundation.layout.RowScope  getFIND +androidx.compose.foundation.layout.RowScope  getFillMaxSize +androidx.compose.foundation.layout.RowScope  getFind +androidx.compose.foundation.layout.RowScope  	getHEIGHT +androidx.compose.foundation.layout.RowScope  	getHeight +androidx.compose.foundation.layout.RowScope  getNAVIGATIONItems +androidx.compose.foundation.layout.RowScope  getNavigationItems +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  navigationItems +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Column .androidx.compose.foundation.lazy.LazyItemScope  ContentCard .androidx.compose.foundation.lazy.LazyItemScope  ContentSection .androidx.compose.foundation.lazy.LazyItemScope  DarkMode .androidx.compose.foundation.lazy.LazyItemScope  Divider .androidx.compose.foundation.lazy.LazyItemScope  FavoriteItemCard .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  Help .androidx.compose.foundation.lazy.LazyItemScope  HighQuality .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  Info .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  NavigationItemRow .androidx.compose.foundation.lazy.LazyItemScope  	PlayArrow .androidx.compose.foundation.lazy.LazyItemScope  SettingsItem .androidx.compose.foundation.lazy.LazyItemScope  SettingsSection .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  Switch .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  Update .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyItemScope  	getHeight .androidx.compose.foundation.lazy.LazyItemScope  
getPADDING .androidx.compose.foundation.lazy.LazyItemScope  
getPadding .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyListScope  ContentCard .androidx.compose.foundation.lazy.LazyListScope  ContentSection .androidx.compose.foundation.lazy.LazyListScope  DarkMode .androidx.compose.foundation.lazy.LazyListScope  Divider .androidx.compose.foundation.lazy.LazyListScope  FavoriteItemCard .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  Help .androidx.compose.foundation.lazy.LazyListScope  HighQuality .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  Info .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  NavigationItemRow .androidx.compose.foundation.lazy.LazyListScope  	PlayArrow .androidx.compose.foundation.lazy.LazyListScope  SettingsItem .androidx.compose.foundation.lazy.LazyListScope  SettingsSection .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  Switch .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  Update .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyListScope  	getHeight .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  getNAVIGATIONItems .androidx.compose.foundation.lazy.LazyListScope  getNavigationItems .androidx.compose.foundation.lazy.LazyListScope  
getPADDING .androidx.compose.foundation.lazy.LazyListScope  
getPadding .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  invoke .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  navigationItems .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Adaptive /androidx.compose.foundation.lazy.grid.GridCells  LiveChannelCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	MovieCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  SearchResultCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  
TVShowCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  LiveChannelCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  	MovieCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  SearchResultCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  
TVShowCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  getITEMS 3androidx.compose.foundation.lazy.grid.LazyGridScope  getItems 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  DarkMode ,androidx.compose.material.icons.Icons.Filled  Favorite ,androidx.compose.material.icons.Icons.Filled  FavoriteBorder ,androidx.compose.material.icons.Icons.Filled  Help ,androidx.compose.material.icons.Icons.Filled  HighQuality ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  LiveTv ,androidx.compose.material.icons.Icons.Filled  Menu ,androidx.compose.material.icons.Icons.Filled  Movie ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Star ,androidx.compose.material.icons.Icons.Filled  Tv ,androidx.compose.material.icons.Icons.Filled  Update ,androidx.compose.material.icons.Icons.Filled  	Alignment &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  DarkMode &androidx.compose.material.icons.filled  Divider &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  FavoriteBorder &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Help &androidx.compose.material.icons.filled  HighQuality &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  
LazyColumn &androidx.compose.material.icons.filled  LiveTv &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Menu &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  Movie &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Screen &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  SettingsItem &androidx.compose.material.icons.filled  SettingsSection &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  Star &androidx.compose.material.icons.filled  Switch &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  Tv &androidx.compose.material.icons.filled  Update &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  ContentArea androidx.compose.material3  ContentCard androidx.compose.material3  ContentSection androidx.compose.material3  DarkMode androidx.compose.material3  Divider androidx.compose.material3  FavoriteItemCard androidx.compose.material3  FavoritesScreen androidx.compose.material3  
FontWeight androidx.compose.material3  	GridCells androidx.compose.material3  Help androidx.compose.material3  HighQuality androidx.compose.material3  
HomeScreen androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Info androidx.compose.material3  Key androidx.compose.material3  KeyEventType androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyRow androidx.compose.material3  LazyVerticalGrid androidx.compose.material3  LiveChannelCard androidx.compose.material3  
LiveScreen androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  	MovieCard androidx.compose.material3  MoviesScreen androidx.compose.material3  NavigationItemRow androidx.compose.material3  NavigationToggleButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  
PaddingValues androidx.compose.material3  	PlayArrow androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Screen androidx.compose.material3  SearchResultCard androidx.compose.material3  SearchScreen androidx.compose.material3  Settings androidx.compose.material3  SettingsItem androidx.compose.material3  SettingsScreen androidx.compose.material3  SettingsSection androidx.compose.material3  SideNavigation androidx.compose.material3  Spacer androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  
TVShowCard androidx.compose.material3  
TVShowsScreen androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  Update androidx.compose.material3  androidx androidx.compose.material3  
composable androidx.compose.material3  darkColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  
fillMaxHeight androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  find androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  isEmpty androidx.compose.material3  
isNotEmpty androidx.compose.material3  items androidx.compose.material3  key androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  navigationItems androidx.compose.material3  
onKeyEvent androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  type androidx.compose.material3  width androidx.compose.material3  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  onBackground &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Box androidx.compose.runtime  BoxScope androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  ContentArea androidx.compose.runtime  ContentCard androidx.compose.runtime  ContentSection androidx.compose.runtime  DarkMode androidx.compose.runtime  Divider androidx.compose.runtime  FavoriteItemCard androidx.compose.runtime  FavoritesScreen androidx.compose.runtime  
FontWeight androidx.compose.runtime  	GridCells androidx.compose.runtime  Help androidx.compose.runtime  HighQuality androidx.compose.runtime  
HomeScreen androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Info androidx.compose.runtime  Key androidx.compose.runtime  KeyEventType androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyRow androidx.compose.runtime  LazyVerticalGrid androidx.compose.runtime  LiveChannelCard androidx.compose.runtime  
LiveScreen androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  	MovieCard androidx.compose.runtime  MoviesScreen androidx.compose.runtime  MutableState androidx.compose.runtime  NavigationItemRow androidx.compose.runtime  NavigationToggleButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  
PaddingValues androidx.compose.runtime  	PlayArrow androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Screen androidx.compose.runtime  SearchResultCard androidx.compose.runtime  SearchScreen androidx.compose.runtime  Settings androidx.compose.runtime  SettingsItem androidx.compose.runtime  SettingsScreen androidx.compose.runtime  SettingsSection androidx.compose.runtime  SideNavigation androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  Surface androidx.compose.runtime  Switch androidx.compose.runtime  
TVShowCard androidx.compose.runtime  
TVShowsScreen androidx.compose.runtime  Text androidx.compose.runtime  Update androidx.compose.runtime  androidx androidx.compose.runtime  
composable androidx.compose.runtime  	emptyList androidx.compose.runtime  
fillMaxHeight androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  find androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  isEmpty androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  items androidx.compose.runtime  key androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  navigationItems androidx.compose.runtime  
onKeyEvent androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  type androidx.compose.runtime  width androidx.compose.runtime  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterStart androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterStart 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  
fillMaxHeight androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  	focusable androidx.compose.ui.Modifier  
getBACKGROUND androidx.compose.ui.Modifier  
getBackground androidx.compose.ui.Modifier  getCLICKABLE androidx.compose.ui.Modifier  getCLIP androidx.compose.ui.Modifier  getClickable androidx.compose.ui.Modifier  getClip androidx.compose.ui.Modifier  getFILLMaxHeight androidx.compose.ui.Modifier  getFILLMaxWidth androidx.compose.ui.Modifier  getFOCUSABLE androidx.compose.ui.Modifier  getFillMaxHeight androidx.compose.ui.Modifier  getFillMaxWidth androidx.compose.ui.Modifier  getFocusable androidx.compose.ui.Modifier  	getHEIGHT androidx.compose.ui.Modifier  	getHeight androidx.compose.ui.Modifier  getONFocusChanged androidx.compose.ui.Modifier  
getONKeyEvent androidx.compose.ui.Modifier  getOnFocusChanged androidx.compose.ui.Modifier  
getOnKeyEvent androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  getWIDTH androidx.compose.ui.Modifier  getWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  onFocusChanged androidx.compose.ui.Modifier  
onKeyEvent androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  then androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  border &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  	getBORDER &androidx.compose.ui.Modifier.Companion  	getBorder &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  FocusRequester androidx.compose.ui.focus  
FocusState androidx.compose.ui.focus  focusRequester androidx.compose.ui.focus  onFocusChanged androidx.compose.ui.focus  	isFocused $androidx.compose.ui.focus.FocusState  Color androidx.compose.ui.graphics  Transparent "androidx.compose.ui.graphics.Color  Transparent ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  	Alignment androidx.compose.ui.input.key  Arrangement androidx.compose.ui.input.key  Box androidx.compose.ui.input.key  BoxScope androidx.compose.ui.input.key  Column androidx.compose.ui.input.key  
Composable androidx.compose.ui.input.key  
FontWeight androidx.compose.ui.input.key  Icon androidx.compose.ui.input.key  Icons androidx.compose.ui.input.key  Key androidx.compose.ui.input.key  KeyEvent androidx.compose.ui.input.key  KeyEventType androidx.compose.ui.input.key  
LazyColumn androidx.compose.ui.input.key  
MaterialTheme androidx.compose.ui.input.key  Modifier androidx.compose.ui.input.key  NavigationItemRow androidx.compose.ui.input.key  NavigationToggleButton androidx.compose.ui.input.key  Row androidx.compose.ui.input.key  Spacer androidx.compose.ui.input.key  Surface androidx.compose.ui.input.key  Text androidx.compose.ui.input.key  androidx androidx.compose.ui.input.key  
fillMaxHeight androidx.compose.ui.input.key  fillMaxSize androidx.compose.ui.input.key  fillMaxWidth androidx.compose.ui.input.key  getValue androidx.compose.ui.input.key  height androidx.compose.ui.input.key  items androidx.compose.ui.input.key  key androidx.compose.ui.input.key  mutableStateOf androidx.compose.ui.input.key  navigationItems androidx.compose.ui.input.key  
onKeyEvent androidx.compose.ui.input.key  padding androidx.compose.ui.input.key  provideDelegate androidx.compose.ui.input.key  remember androidx.compose.ui.input.key  setValue androidx.compose.ui.input.key  size androidx.compose.ui.input.key  type androidx.compose.ui.input.key  width androidx.compose.ui.input.key  DirectionCenter !androidx.compose.ui.input.key.Key  Enter !androidx.compose.ui.input.key.Key  equals !androidx.compose.ui.input.key.Key  DirectionCenter +androidx.compose.ui.input.key.Key.Companion  Enter +androidx.compose.ui.input.key.Key.Companion  getKEY &androidx.compose.ui.input.key.KeyEvent  getKey &androidx.compose.ui.input.key.KeyEvent  getTYPE &androidx.compose.ui.input.key.KeyEvent  getType &androidx.compose.ui.input.key.KeyEvent  key &androidx.compose.ui.input.key.KeyEvent  type &androidx.compose.ui.input.key.KeyEvent  KeyUp *androidx.compose.ui.input.key.KeyEventType  equals *androidx.compose.ui.input.key.KeyEventType  KeyUp 4androidx.compose.ui.input.key.KeyEventType.Companion  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
LitchiTVTheme #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  navigate !androidx.navigation.NavController  startDestinationId androidx.navigation.NavGraph  FavoritesScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  
LiveScreen #androidx.navigation.NavGraphBuilder  MoviesScreen #androidx.navigation.NavGraphBuilder  SearchScreen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  
TVShowsScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  
getCOMPOSABLE #androidx.navigation.NavGraphBuilder  
getComposable #androidx.navigation.NavGraphBuilder  graph %androidx.navigation.NavHostController  navigate %androidx.navigation.NavHostController  invoke %androidx.navigation.NavOptionsBuilder  launchSingleTop %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  restoreState %androidx.navigation.NavOptionsBuilder  	saveState "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  
LitchiTVTheme com.google.chuangke  MainActivity com.google.chuangke  
MainScreen com.google.chuangke  
MaterialTheme com.google.chuangke  Modifier com.google.chuangke  Surface com.google.chuangke  fillMaxSize com.google.chuangke  
setContent com.google.chuangke  Bundle  com.google.chuangke.MainActivity  
LitchiTVTheme  com.google.chuangke.MainActivity  
MainScreen  com.google.chuangke.MainActivity  
MaterialTheme  com.google.chuangke.MainActivity  Modifier  com.google.chuangke.MainActivity  Surface  com.google.chuangke.MainActivity  fillMaxSize  com.google.chuangke.MainActivity  getFILLMaxSize  com.google.chuangke.MainActivity  getFillMaxSize  com.google.chuangke.MainActivity  
getSETContent  com.google.chuangke.MainActivity  
getSetContent  com.google.chuangke.MainActivity  
setContent  com.google.chuangke.MainActivity  Favorite com.google.chuangke.navigation  Home com.google.chuangke.navigation  LiveTv com.google.chuangke.navigation  Movie com.google.chuangke.navigation  NavigationItem com.google.chuangke.navigation  Screen com.google.chuangke.navigation  Search com.google.chuangke.navigation  Settings com.google.chuangke.navigation  String com.google.chuangke.navigation  Tv com.google.chuangke.navigation  listOf com.google.chuangke.navigation  navigationItems com.google.chuangke.navigation  ImageVector -com.google.chuangke.navigation.NavigationItem  Screen -com.google.chuangke.navigation.NavigationItem  String -com.google.chuangke.navigation.NavigationItem  icon -com.google.chuangke.navigation.NavigationItem  route -com.google.chuangke.navigation.NavigationItem  screen -com.google.chuangke.navigation.NavigationItem  title -com.google.chuangke.navigation.NavigationItem  	FAVORITES %com.google.chuangke.navigation.Screen  HOME %com.google.chuangke.navigation.Screen  LIVE %com.google.chuangke.navigation.Screen  MOVIES %com.google.chuangke.navigation.Screen  SEARCH %com.google.chuangke.navigation.Screen  SETTINGS %com.google.chuangke.navigation.Screen  TV_SHOWS %com.google.chuangke.navigation.Screen  equals %com.google.chuangke.navigation.Screen  	Alignment !com.google.chuangke.ui.components  Arrangement !com.google.chuangke.ui.components  Boolean !com.google.chuangke.ui.components  Box !com.google.chuangke.ui.components  BoxScope !com.google.chuangke.ui.components  Column !com.google.chuangke.ui.components  
Composable !com.google.chuangke.ui.components  
FontWeight !com.google.chuangke.ui.components  Icon !com.google.chuangke.ui.components  Icons !com.google.chuangke.ui.components  Key !com.google.chuangke.ui.components  KeyEventType !com.google.chuangke.ui.components  
LazyColumn !com.google.chuangke.ui.components  
MaterialTheme !com.google.chuangke.ui.components  Modifier !com.google.chuangke.ui.components  NavigationItemRow !com.google.chuangke.ui.components  NavigationToggleButton !com.google.chuangke.ui.components  Row !com.google.chuangke.ui.components  SideNavigation !com.google.chuangke.ui.components  Spacer !com.google.chuangke.ui.components  Surface !com.google.chuangke.ui.components  TVButton !com.google.chuangke.ui.components  TVCard !com.google.chuangke.ui.components  TVFocusable !com.google.chuangke.ui.components  Text !com.google.chuangke.ui.components  Unit !com.google.chuangke.ui.components  androidx !com.google.chuangke.ui.components  
fillMaxHeight !com.google.chuangke.ui.components  fillMaxSize !com.google.chuangke.ui.components  fillMaxWidth !com.google.chuangke.ui.components  getValue !com.google.chuangke.ui.components  height !com.google.chuangke.ui.components  items !com.google.chuangke.ui.components  key !com.google.chuangke.ui.components  mutableStateOf !com.google.chuangke.ui.components  navigationItems !com.google.chuangke.ui.components  
onKeyEvent !com.google.chuangke.ui.components  padding !com.google.chuangke.ui.components  provideDelegate !com.google.chuangke.ui.components  remember !com.google.chuangke.ui.components  setValue !com.google.chuangke.ui.components  size !com.google.chuangke.ui.components  type !com.google.chuangke.ui.components  width !com.google.chuangke.ui.components  	Alignment com.google.chuangke.ui.screens  Arrangement com.google.chuangke.ui.screens  Box com.google.chuangke.ui.screens  Button com.google.chuangke.ui.screens  Card com.google.chuangke.ui.screens  CardDefaults com.google.chuangke.ui.screens  Column com.google.chuangke.ui.screens  
Composable com.google.chuangke.ui.screens  ContentArea com.google.chuangke.ui.screens  ContentCard com.google.chuangke.ui.screens  ContentSection com.google.chuangke.ui.screens  DarkMode com.google.chuangke.ui.screens  Divider com.google.chuangke.ui.screens  FavoriteItem com.google.chuangke.ui.screens  FavoriteItemCard com.google.chuangke.ui.screens  FavoritesScreen com.google.chuangke.ui.screens  
FontWeight com.google.chuangke.ui.screens  	GridCells com.google.chuangke.ui.screens  Help com.google.chuangke.ui.screens  HighQuality com.google.chuangke.ui.screens  
HomeScreen com.google.chuangke.ui.screens  Icon com.google.chuangke.ui.screens  
IconButton com.google.chuangke.ui.screens  Icons com.google.chuangke.ui.screens  Info com.google.chuangke.ui.screens  
LazyColumn com.google.chuangke.ui.screens  LazyRow com.google.chuangke.ui.screens  LazyVerticalGrid com.google.chuangke.ui.screens  List com.google.chuangke.ui.screens  LiveChannelCard com.google.chuangke.ui.screens  
LiveScreen com.google.chuangke.ui.screens  
MainScreen com.google.chuangke.ui.screens  
MaterialTheme com.google.chuangke.ui.screens  Modifier com.google.chuangke.ui.screens  Movie com.google.chuangke.ui.screens  	MovieCard com.google.chuangke.ui.screens  MoviesScreen com.google.chuangke.ui.screens  OutlinedTextField com.google.chuangke.ui.screens  
PaddingValues com.google.chuangke.ui.screens  	PlayArrow com.google.chuangke.ui.screens  RoundedCornerShape com.google.chuangke.ui.screens  Row com.google.chuangke.ui.screens  Screen com.google.chuangke.ui.screens  SearchResultCard com.google.chuangke.ui.screens  SearchScreen com.google.chuangke.ui.screens  Settings com.google.chuangke.ui.screens  SettingsItem com.google.chuangke.ui.screens  SettingsScreen com.google.chuangke.ui.screens  SettingsSection com.google.chuangke.ui.screens  SideNavigation com.google.chuangke.ui.screens  Spacer com.google.chuangke.ui.screens  String com.google.chuangke.ui.screens  Surface com.google.chuangke.ui.screens  Switch com.google.chuangke.ui.screens  TVShow com.google.chuangke.ui.screens  
TVShowCard com.google.chuangke.ui.screens  
TVShowsScreen com.google.chuangke.ui.screens  Text com.google.chuangke.ui.screens  Unit com.google.chuangke.ui.screens  Update com.google.chuangke.ui.screens  androidx com.google.chuangke.ui.screens  
composable com.google.chuangke.ui.screens  	emptyList com.google.chuangke.ui.screens  fillMaxSize com.google.chuangke.ui.screens  fillMaxWidth com.google.chuangke.ui.screens  find com.google.chuangke.ui.screens  getValue com.google.chuangke.ui.screens  height com.google.chuangke.ui.screens  isEmpty com.google.chuangke.ui.screens  
isNotEmpty com.google.chuangke.ui.screens  items com.google.chuangke.ui.screens  listOf com.google.chuangke.ui.screens  mutableStateOf com.google.chuangke.ui.screens  navigationItems com.google.chuangke.ui.screens  padding com.google.chuangke.ui.screens  provideDelegate com.google.chuangke.ui.screens  remember com.google.chuangke.ui.screens  setValue com.google.chuangke.ui.screens  size com.google.chuangke.ui.screens  width com.google.chuangke.ui.screens  String +com.google.chuangke.ui.screens.FavoriteItem  rating +com.google.chuangke.ui.screens.FavoriteItem  title +com.google.chuangke.ui.screens.FavoriteItem  type +com.google.chuangke.ui.screens.FavoriteItem  year +com.google.chuangke.ui.screens.FavoriteItem  String $com.google.chuangke.ui.screens.Movie  genre $com.google.chuangke.ui.screens.Movie  rating $com.google.chuangke.ui.screens.Movie  title $com.google.chuangke.ui.screens.Movie  year $com.google.chuangke.ui.screens.Movie  String %com.google.chuangke.ui.screens.TVShow  episodes %com.google.chuangke.ui.screens.TVShow  genre %com.google.chuangke.ui.screens.TVShow  rating %com.google.chuangke.ui.screens.TVShow  title %com.google.chuangke.ui.screens.TVShow  year %com.google.chuangke.ui.screens.TVShow  Boolean com.google.chuangke.ui.theme  DarkBackground com.google.chuangke.ui.theme  DarkColorScheme com.google.chuangke.ui.theme  DarkOutline com.google.chuangke.ui.theme  DarkOutlineVariant com.google.chuangke.ui.theme  DarkSurface com.google.chuangke.ui.theme  DarkSurfaceVariant com.google.chuangke.ui.theme  LightColorScheme com.google.chuangke.ui.theme  
LitchiTVTheme com.google.chuangke.ui.theme  OnDarkBackground com.google.chuangke.ui.theme  
OnDarkPrimary com.google.chuangke.ui.theme  OnDarkSecondary com.google.chuangke.ui.theme  
OnDarkSurface com.google.chuangke.ui.theme  OnDarkSurfaceVariant com.google.chuangke.ui.theme  Purple20 com.google.chuangke.ui.theme  Purple40 com.google.chuangke.ui.theme  Purple80 com.google.chuangke.ui.theme  Red40 com.google.chuangke.ui.theme  Red80 com.google.chuangke.ui.theme  Teal40 com.google.chuangke.ui.theme  Teal80 com.google.chuangke.ui.theme  
Typography com.google.chuangke.ui.theme  Unit com.google.chuangke.ui.theme  	Alignment 	java.lang  Arrangement 	java.lang  Box 	java.lang  Button 	java.lang  Card 	java.lang  CardDefaults 	java.lang  Column 	java.lang  ContentArea 	java.lang  ContentCard 	java.lang  ContentSection 	java.lang  Divider 	java.lang  FavoriteItemCard 	java.lang  FavoritesScreen 	java.lang  
FontWeight 	java.lang  	GridCells 	java.lang  
HomeScreen 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  
LazyColumn 	java.lang  LazyRow 	java.lang  LazyVerticalGrid 	java.lang  
LitchiTVTheme 	java.lang  LiveChannelCard 	java.lang  
LiveScreen 	java.lang  
MainScreen 	java.lang  
MaterialTheme 	java.lang  Modifier 	java.lang  	MovieCard 	java.lang  MoviesScreen 	java.lang  NavigationItemRow 	java.lang  NavigationToggleButton 	java.lang  OutlinedTextField 	java.lang  
PaddingValues 	java.lang  RoundedCornerShape 	java.lang  Row 	java.lang  Screen 	java.lang  SearchResultCard 	java.lang  SearchScreen 	java.lang  SettingsItem 	java.lang  SettingsScreen 	java.lang  SettingsSection 	java.lang  SideNavigation 	java.lang  Spacer 	java.lang  Surface 	java.lang  Switch 	java.lang  
TVShowCard 	java.lang  
TVShowsScreen 	java.lang  Text 	java.lang  androidx 	java.lang  	emptyList 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  find 	java.lang  height 	java.lang  isEmpty 	java.lang  
isNotEmpty 	java.lang  listOf 	java.lang  navigationItems 	java.lang  padding 	java.lang  provideDelegate 	java.lang  size 	java.lang  width 	java.lang  	Alignment kotlin  Arrangement kotlin  Boolean kotlin  Box kotlin  Button kotlin  Card kotlin  CardDefaults kotlin  Column kotlin  ContentArea kotlin  ContentCard kotlin  ContentSection kotlin  Divider kotlin  Double kotlin  FavoriteItemCard kotlin  FavoritesScreen kotlin  Float kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	GridCells kotlin  
HomeScreen kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  Int kotlin  
LazyColumn kotlin  LazyRow kotlin  LazyVerticalGrid kotlin  
LitchiTVTheme kotlin  LiveChannelCard kotlin  
LiveScreen kotlin  
MainScreen kotlin  
MaterialTheme kotlin  Modifier kotlin  	MovieCard kotlin  MoviesScreen kotlin  NavigationItemRow kotlin  NavigationToggleButton kotlin  Nothing kotlin  OutlinedTextField kotlin  
PaddingValues kotlin  RoundedCornerShape kotlin  Row kotlin  Screen kotlin  SearchResultCard kotlin  SearchScreen kotlin  SettingsItem kotlin  SettingsScreen kotlin  SettingsSection kotlin  SideNavigation kotlin  Spacer kotlin  String kotlin  Surface kotlin  Switch kotlin  
TVShowCard kotlin  
TVShowsScreen kotlin  Text kotlin  Unit kotlin  androidx kotlin  	emptyList kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  find kotlin  height kotlin  isEmpty kotlin  
isNotEmpty kotlin  listOf kotlin  navigationItems kotlin  padding kotlin  provideDelegate kotlin  size kotlin  width kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  
getISEmpty 
kotlin.String  
getISNotEmpty 
kotlin.String  
getIsEmpty 
kotlin.String  
getIsNotEmpty 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  	Alignment kotlin.annotation  Arrangement kotlin.annotation  Box kotlin.annotation  Button kotlin.annotation  Card kotlin.annotation  CardDefaults kotlin.annotation  Column kotlin.annotation  ContentArea kotlin.annotation  ContentCard kotlin.annotation  ContentSection kotlin.annotation  Divider kotlin.annotation  FavoriteItemCard kotlin.annotation  FavoritesScreen kotlin.annotation  
FontWeight kotlin.annotation  	GridCells kotlin.annotation  
HomeScreen kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  
LazyColumn kotlin.annotation  LazyRow kotlin.annotation  LazyVerticalGrid kotlin.annotation  
LitchiTVTheme kotlin.annotation  LiveChannelCard kotlin.annotation  
LiveScreen kotlin.annotation  
MainScreen kotlin.annotation  
MaterialTheme kotlin.annotation  Modifier kotlin.annotation  	MovieCard kotlin.annotation  MoviesScreen kotlin.annotation  NavigationItemRow kotlin.annotation  NavigationToggleButton kotlin.annotation  OutlinedTextField kotlin.annotation  
PaddingValues kotlin.annotation  RoundedCornerShape kotlin.annotation  Row kotlin.annotation  Screen kotlin.annotation  SearchResultCard kotlin.annotation  SearchScreen kotlin.annotation  SettingsItem kotlin.annotation  SettingsScreen kotlin.annotation  SettingsSection kotlin.annotation  SideNavigation kotlin.annotation  Spacer kotlin.annotation  Surface kotlin.annotation  Switch kotlin.annotation  
TVShowCard kotlin.annotation  
TVShowsScreen kotlin.annotation  Text kotlin.annotation  androidx kotlin.annotation  	emptyList kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  find kotlin.annotation  height kotlin.annotation  isEmpty kotlin.annotation  
isNotEmpty kotlin.annotation  listOf kotlin.annotation  navigationItems kotlin.annotation  padding kotlin.annotation  provideDelegate kotlin.annotation  size kotlin.annotation  width kotlin.annotation  	Alignment kotlin.collections  Arrangement kotlin.collections  Box kotlin.collections  Button kotlin.collections  Card kotlin.collections  CardDefaults kotlin.collections  Column kotlin.collections  ContentArea kotlin.collections  ContentCard kotlin.collections  ContentSection kotlin.collections  Divider kotlin.collections  FavoriteItemCard kotlin.collections  FavoritesScreen kotlin.collections  
FontWeight kotlin.collections  	GridCells kotlin.collections  
HomeScreen kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  
LazyColumn kotlin.collections  LazyRow kotlin.collections  LazyVerticalGrid kotlin.collections  List kotlin.collections  
LitchiTVTheme kotlin.collections  LiveChannelCard kotlin.collections  
LiveScreen kotlin.collections  
MainScreen kotlin.collections  
MaterialTheme kotlin.collections  Modifier kotlin.collections  	MovieCard kotlin.collections  MoviesScreen kotlin.collections  NavigationItemRow kotlin.collections  NavigationToggleButton kotlin.collections  OutlinedTextField kotlin.collections  
PaddingValues kotlin.collections  RoundedCornerShape kotlin.collections  Row kotlin.collections  Screen kotlin.collections  SearchResultCard kotlin.collections  SearchScreen kotlin.collections  SettingsItem kotlin.collections  SettingsScreen kotlin.collections  SettingsSection kotlin.collections  SideNavigation kotlin.collections  Spacer kotlin.collections  Surface kotlin.collections  Switch kotlin.collections  
TVShowCard kotlin.collections  
TVShowsScreen kotlin.collections  Text kotlin.collections  androidx kotlin.collections  	emptyList kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  find kotlin.collections  height kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  navigationItems kotlin.collections  padding kotlin.collections  provideDelegate kotlin.collections  size kotlin.collections  width kotlin.collections  getFIND kotlin.collections.List  getFind kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  	Alignment kotlin.comparisons  Arrangement kotlin.comparisons  Box kotlin.comparisons  Button kotlin.comparisons  Card kotlin.comparisons  CardDefaults kotlin.comparisons  Column kotlin.comparisons  ContentArea kotlin.comparisons  ContentCard kotlin.comparisons  ContentSection kotlin.comparisons  Divider kotlin.comparisons  FavoriteItemCard kotlin.comparisons  FavoritesScreen kotlin.comparisons  
FontWeight kotlin.comparisons  	GridCells kotlin.comparisons  
HomeScreen kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  
LazyColumn kotlin.comparisons  LazyRow kotlin.comparisons  LazyVerticalGrid kotlin.comparisons  
LitchiTVTheme kotlin.comparisons  LiveChannelCard kotlin.comparisons  
LiveScreen kotlin.comparisons  
MainScreen kotlin.comparisons  
MaterialTheme kotlin.comparisons  Modifier kotlin.comparisons  	MovieCard kotlin.comparisons  MoviesScreen kotlin.comparisons  NavigationItemRow kotlin.comparisons  NavigationToggleButton kotlin.comparisons  OutlinedTextField kotlin.comparisons  
PaddingValues kotlin.comparisons  RoundedCornerShape kotlin.comparisons  Row kotlin.comparisons  Screen kotlin.comparisons  SearchResultCard kotlin.comparisons  SearchScreen kotlin.comparisons  SettingsItem kotlin.comparisons  SettingsScreen kotlin.comparisons  SettingsSection kotlin.comparisons  SideNavigation kotlin.comparisons  Spacer kotlin.comparisons  Surface kotlin.comparisons  Switch kotlin.comparisons  
TVShowCard kotlin.comparisons  
TVShowsScreen kotlin.comparisons  Text kotlin.comparisons  androidx kotlin.comparisons  	emptyList kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  find kotlin.comparisons  height kotlin.comparisons  isEmpty kotlin.comparisons  
isNotEmpty kotlin.comparisons  listOf kotlin.comparisons  navigationItems kotlin.comparisons  padding kotlin.comparisons  provideDelegate kotlin.comparisons  size kotlin.comparisons  width kotlin.comparisons  	Alignment 	kotlin.io  Arrangement 	kotlin.io  Box 	kotlin.io  Button 	kotlin.io  Card 	kotlin.io  CardDefaults 	kotlin.io  Column 	kotlin.io  ContentArea 	kotlin.io  ContentCard 	kotlin.io  ContentSection 	kotlin.io  Divider 	kotlin.io  FavoriteItemCard 	kotlin.io  FavoritesScreen 	kotlin.io  
FontWeight 	kotlin.io  	GridCells 	kotlin.io  
HomeScreen 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  
LazyColumn 	kotlin.io  LazyRow 	kotlin.io  LazyVerticalGrid 	kotlin.io  
LitchiTVTheme 	kotlin.io  LiveChannelCard 	kotlin.io  
LiveScreen 	kotlin.io  
MainScreen 	kotlin.io  
MaterialTheme 	kotlin.io  Modifier 	kotlin.io  	MovieCard 	kotlin.io  MoviesScreen 	kotlin.io  NavigationItemRow 	kotlin.io  NavigationToggleButton 	kotlin.io  OutlinedTextField 	kotlin.io  
PaddingValues 	kotlin.io  RoundedCornerShape 	kotlin.io  Row 	kotlin.io  Screen 	kotlin.io  SearchResultCard 	kotlin.io  SearchScreen 	kotlin.io  SettingsItem 	kotlin.io  SettingsScreen 	kotlin.io  SettingsSection 	kotlin.io  SideNavigation 	kotlin.io  Spacer 	kotlin.io  Surface 	kotlin.io  Switch 	kotlin.io  
TVShowCard 	kotlin.io  
TVShowsScreen 	kotlin.io  Text 	kotlin.io  androidx 	kotlin.io  	emptyList 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  find 	kotlin.io  height 	kotlin.io  isEmpty 	kotlin.io  
isNotEmpty 	kotlin.io  listOf 	kotlin.io  navigationItems 	kotlin.io  padding 	kotlin.io  provideDelegate 	kotlin.io  size 	kotlin.io  width 	kotlin.io  	Alignment 
kotlin.jvm  Arrangement 
kotlin.jvm  Box 
kotlin.jvm  Button 
kotlin.jvm  Card 
kotlin.jvm  CardDefaults 
kotlin.jvm  Column 
kotlin.jvm  ContentArea 
kotlin.jvm  ContentCard 
kotlin.jvm  ContentSection 
kotlin.jvm  Divider 
kotlin.jvm  FavoriteItemCard 
kotlin.jvm  FavoritesScreen 
kotlin.jvm  
FontWeight 
kotlin.jvm  	GridCells 
kotlin.jvm  
HomeScreen 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  
LazyColumn 
kotlin.jvm  LazyRow 
kotlin.jvm  LazyVerticalGrid 
kotlin.jvm  
LitchiTVTheme 
kotlin.jvm  LiveChannelCard 
kotlin.jvm  
LiveScreen 
kotlin.jvm  
MainScreen 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Modifier 
kotlin.jvm  	MovieCard 
kotlin.jvm  MoviesScreen 
kotlin.jvm  NavigationItemRow 
kotlin.jvm  NavigationToggleButton 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  
PaddingValues 
kotlin.jvm  RoundedCornerShape 
kotlin.jvm  Row 
kotlin.jvm  Screen 
kotlin.jvm  SearchResultCard 
kotlin.jvm  SearchScreen 
kotlin.jvm  SettingsItem 
kotlin.jvm  SettingsScreen 
kotlin.jvm  SettingsSection 
kotlin.jvm  SideNavigation 
kotlin.jvm  Spacer 
kotlin.jvm  Surface 
kotlin.jvm  Switch 
kotlin.jvm  
TVShowCard 
kotlin.jvm  
TVShowsScreen 
kotlin.jvm  Text 
kotlin.jvm  androidx 
kotlin.jvm  	emptyList 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  find 
kotlin.jvm  height 
kotlin.jvm  isEmpty 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  listOf 
kotlin.jvm  navigationItems 
kotlin.jvm  padding 
kotlin.jvm  provideDelegate 
kotlin.jvm  size 
kotlin.jvm  width 
kotlin.jvm  	Alignment 
kotlin.ranges  Arrangement 
kotlin.ranges  Box 
kotlin.ranges  Button 
kotlin.ranges  Card 
kotlin.ranges  CardDefaults 
kotlin.ranges  Column 
kotlin.ranges  ContentArea 
kotlin.ranges  ContentCard 
kotlin.ranges  ContentSection 
kotlin.ranges  Divider 
kotlin.ranges  FavoriteItemCard 
kotlin.ranges  FavoritesScreen 
kotlin.ranges  
FontWeight 
kotlin.ranges  	GridCells 
kotlin.ranges  
HomeScreen 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  
LazyColumn 
kotlin.ranges  LazyRow 
kotlin.ranges  LazyVerticalGrid 
kotlin.ranges  
LitchiTVTheme 
kotlin.ranges  LiveChannelCard 
kotlin.ranges  
LiveScreen 
kotlin.ranges  
MainScreen 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Modifier 
kotlin.ranges  	MovieCard 
kotlin.ranges  MoviesScreen 
kotlin.ranges  NavigationItemRow 
kotlin.ranges  NavigationToggleButton 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  
PaddingValues 
kotlin.ranges  RoundedCornerShape 
kotlin.ranges  Row 
kotlin.ranges  Screen 
kotlin.ranges  SearchResultCard 
kotlin.ranges  SearchScreen 
kotlin.ranges  SettingsItem 
kotlin.ranges  SettingsScreen 
kotlin.ranges  SettingsSection 
kotlin.ranges  SideNavigation 
kotlin.ranges  Spacer 
kotlin.ranges  Surface 
kotlin.ranges  Switch 
kotlin.ranges  
TVShowCard 
kotlin.ranges  
TVShowsScreen 
kotlin.ranges  Text 
kotlin.ranges  androidx 
kotlin.ranges  	emptyList 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  find 
kotlin.ranges  height 
kotlin.ranges  isEmpty 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  listOf 
kotlin.ranges  navigationItems 
kotlin.ranges  padding 
kotlin.ranges  provideDelegate 
kotlin.ranges  size 
kotlin.ranges  width 
kotlin.ranges  	Alignment kotlin.sequences  Arrangement kotlin.sequences  Box kotlin.sequences  Button kotlin.sequences  Card kotlin.sequences  CardDefaults kotlin.sequences  Column kotlin.sequences  ContentArea kotlin.sequences  ContentCard kotlin.sequences  ContentSection kotlin.sequences  Divider kotlin.sequences  FavoriteItemCard kotlin.sequences  FavoritesScreen kotlin.sequences  
FontWeight kotlin.sequences  	GridCells kotlin.sequences  
HomeScreen kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  
LazyColumn kotlin.sequences  LazyRow kotlin.sequences  LazyVerticalGrid kotlin.sequences  
LitchiTVTheme kotlin.sequences  LiveChannelCard kotlin.sequences  
LiveScreen kotlin.sequences  
MainScreen kotlin.sequences  
MaterialTheme kotlin.sequences  Modifier kotlin.sequences  	MovieCard kotlin.sequences  MoviesScreen kotlin.sequences  NavigationItemRow kotlin.sequences  NavigationToggleButton kotlin.sequences  OutlinedTextField kotlin.sequences  
PaddingValues kotlin.sequences  RoundedCornerShape kotlin.sequences  Row kotlin.sequences  Screen kotlin.sequences  SearchResultCard kotlin.sequences  SearchScreen kotlin.sequences  SettingsItem kotlin.sequences  SettingsScreen kotlin.sequences  SettingsSection kotlin.sequences  SideNavigation kotlin.sequences  Spacer kotlin.sequences  Surface kotlin.sequences  Switch kotlin.sequences  
TVShowCard kotlin.sequences  
TVShowsScreen kotlin.sequences  Text kotlin.sequences  androidx kotlin.sequences  	emptyList kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  find kotlin.sequences  height kotlin.sequences  isEmpty kotlin.sequences  
isNotEmpty kotlin.sequences  listOf kotlin.sequences  navigationItems kotlin.sequences  padding kotlin.sequences  provideDelegate kotlin.sequences  size kotlin.sequences  width kotlin.sequences  	Alignment kotlin.text  Arrangement kotlin.text  Box kotlin.text  Button kotlin.text  Card kotlin.text  CardDefaults kotlin.text  Column kotlin.text  ContentArea kotlin.text  ContentCard kotlin.text  ContentSection kotlin.text  Divider kotlin.text  FavoriteItemCard kotlin.text  FavoritesScreen kotlin.text  
FontWeight kotlin.text  	GridCells kotlin.text  
HomeScreen kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  
LazyColumn kotlin.text  LazyRow kotlin.text  LazyVerticalGrid kotlin.text  
LitchiTVTheme kotlin.text  LiveChannelCard kotlin.text  
LiveScreen kotlin.text  
MainScreen kotlin.text  
MaterialTheme kotlin.text  Modifier kotlin.text  	MovieCard kotlin.text  MoviesScreen kotlin.text  NavigationItemRow kotlin.text  NavigationToggleButton kotlin.text  OutlinedTextField kotlin.text  
PaddingValues kotlin.text  RoundedCornerShape kotlin.text  Row kotlin.text  Screen kotlin.text  SearchResultCard kotlin.text  SearchScreen kotlin.text  SettingsItem kotlin.text  SettingsScreen kotlin.text  SettingsSection kotlin.text  SideNavigation kotlin.text  Spacer kotlin.text  Surface kotlin.text  Switch kotlin.text  
TVShowCard kotlin.text  
TVShowsScreen kotlin.text  Text kotlin.text  androidx kotlin.text  	emptyList kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  find kotlin.text  height kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  listOf kotlin.text  navigationItems kotlin.text  padding kotlin.text  provideDelegate kotlin.text  size kotlin.text  width kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   