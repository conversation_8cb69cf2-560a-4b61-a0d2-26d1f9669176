  Bundle android.app.Activity  
LitchiTVTheme android.app.Activity  
MainScreen android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Surface android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  Bundle android.content.Context  
LitchiTVTheme android.content.Context  
MainScreen android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  fillMaxSize android.content.Context  onCreate android.content.Context  
setContent android.content.Context  Bundle android.content.ContextWrapper  
LitchiTVTheme android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  onCreate android.content.ContextWrapper  
setContent android.content.ContextWrapper  Bundle 
android.os  Bundle  android.view.ContextThemeWrapper  
LitchiTVTheme  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  
LitchiTVTheme #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
LitchiTVTheme -androidx.activity.ComponentActivity.Companion  
MainScreen -androidx.activity.ComponentActivity.Companion  
MaterialTheme -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  Surface -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  getFILLMaxSize -androidx.activity.ComponentActivity.Companion  getFillMaxSize -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  FavoritesScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  
LiveScreen /androidx.compose.animation.AnimatedContentScope  MoviesScreen /androidx.compose.animation.AnimatedContentScope  SearchScreen /androidx.compose.animation.AnimatedContentScope  SettingsScreen /androidx.compose.animation.AnimatedContentScope  
TVShowsScreen /androidx.compose.animation.AnimatedContentScope  	TweenSpec androidx.compose.animation.core  animateDpAsState androidx.compose.animation.core  tween androidx.compose.animation.core  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  	focusable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentArea "androidx.compose.foundation.layout  ContentCard "androidx.compose.foundation.layout  ContentSection "androidx.compose.foundation.layout  DarkMode "androidx.compose.foundation.layout  Divider "androidx.compose.foundation.layout  FavoriteItemCard "androidx.compose.foundation.layout  FavoritesScreen "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  	GridCells "androidx.compose.foundation.layout  Help "androidx.compose.foundation.layout  HighQuality "androidx.compose.foundation.layout  
HomeScreen "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  Key "androidx.compose.foundation.layout  KeyEventType "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  LazyVerticalGrid "androidx.compose.foundation.layout  LiveChannelCard "androidx.compose.foundation.layout  
LiveScreen "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  	MovieCard "androidx.compose.foundation.layout  MoviesScreen "androidx.compose.foundation.layout  NavigationItemRow "androidx.compose.foundation.layout  NavigationToggleButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Screen "androidx.compose.foundation.layout  SearchResultCard "androidx.compose.foundation.layout  SearchScreen "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  SettingsItem "androidx.compose.foundation.layout  SettingsScreen "androidx.compose.foundation.layout  SettingsSection "androidx.compose.foundation.layout  SideNavigation "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  
TVShowCard "androidx.compose.foundation.layout  
TVShowsScreen "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  Update "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
composable "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  find "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  key "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  navigationItems "androidx.compose.foundation.layout  
onKeyEvent "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  type "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Start .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  FavoriteBorder +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Menu +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Search +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  content +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  
getCONTENT +androidx.compose.foundation.layout.BoxScope  
getContent +androidx.compose.foundation.layout.BoxScope  getFILLMaxSize +androidx.compose.foundation.layout.BoxScope  getFILLMaxWidth +androidx.compose.foundation.layout.BoxScope  getFillMaxSize +androidx.compose.foundation.layout.BoxScope  getFillMaxWidth +androidx.compose.foundation.layout.BoxScope  	getHEIGHT +androidx.compose.foundation.layout.BoxScope  	getHeight +androidx.compose.foundation.layout.BoxScope  
getPADDING +androidx.compose.foundation.layout.BoxScope  
getPadding +androidx.compose.foundation.layout.BoxScope  getSIZE +androidx.compose.foundation.layout.BoxScope  getSize +androidx.compose.foundation.layout.BoxScope  getWIDTH +androidx.compose.foundation.layout.BoxScope  getWidth +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  invoke +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  ContentCard .androidx.compose.foundation.layout.ColumnScope  DarkMode .androidx.compose.foundation.layout.ColumnScope  Divider .androidx.compose.foundation.layout.ColumnScope  Favorite .androidx.compose.foundation.layout.ColumnScope  FavoriteBorder .androidx.compose.foundation.layout.ColumnScope  FavoriteItemCard .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  	GridCells .androidx.compose.foundation.layout.ColumnScope  Help .androidx.compose.foundation.layout.ColumnScope  HighQuality .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Info .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  LazyVerticalGrid .androidx.compose.foundation.layout.ColumnScope  LiveChannelCard .androidx.compose.foundation.layout.ColumnScope  LiveTv .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Movie .androidx.compose.foundation.layout.ColumnScope  	MovieCard .androidx.compose.foundation.layout.ColumnScope  NavigationItemRow .androidx.compose.foundation.layout.ColumnScope  NavigationToggleButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Search .androidx.compose.foundation.layout.ColumnScope  SearchResultCard .androidx.compose.foundation.layout.ColumnScope  Settings .androidx.compose.foundation.layout.ColumnScope  SettingsItem .androidx.compose.foundation.layout.ColumnScope  SettingsSection .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Star .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  
TVShowCard .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  Tv .androidx.compose.foundation.layout.ColumnScope  Update .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  	emptyList .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getEMPTYList .androidx.compose.foundation.layout.ColumnScope  getEmptyList .androidx.compose.foundation.layout.ColumnScope  getFILLMaxSize .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxSize .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getISEmpty .androidx.compose.foundation.layout.ColumnScope  
getISNotEmpty .androidx.compose.foundation.layout.ColumnScope  
getIsEmpty .androidx.compose.foundation.layout.ColumnScope  
getIsNotEmpty .androidx.compose.foundation.layout.ColumnScope  	getLISTOf .androidx.compose.foundation.layout.ColumnScope  	getListOf .androidx.compose.foundation.layout.ColumnScope  getNAVIGATIONItems .androidx.compose.foundation.layout.ColumnScope  getNavigationItems .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getWIDTH .androidx.compose.foundation.layout.ColumnScope  getWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  isEmpty .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  navigationItems .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  ContentArea +androidx.compose.foundation.layout.RowScope  Favorite +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  LiveTv +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Menu +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Movie +androidx.compose.foundation.layout.RowScope  
PaddingValues +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Settings +androidx.compose.foundation.layout.RowScope  SideNavigation +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Star +androidx.compose.foundation.layout.RowScope  Surface +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  Tv +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  find +androidx.compose.foundation.layout.RowScope  getFILLMaxSize +androidx.compose.foundation.layout.RowScope  getFIND +androidx.compose.foundation.layout.RowScope  getFillMaxSize +androidx.compose.foundation.layout.RowScope  getFind +androidx.compose.foundation.layout.RowScope  	getHEIGHT +androidx.compose.foundation.layout.RowScope  	getHeight +androidx.compose.foundation.layout.RowScope  getNAVIGATIONItems +androidx.compose.foundation.layout.RowScope  getNavigationItems +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  navigationItems +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Column .androidx.compose.foundation.lazy.LazyItemScope  ContentCard .androidx.compose.foundation.lazy.LazyItemScope  ContentSection .androidx.compose.foundation.lazy.LazyItemScope  DarkMode .androidx.compose.foundation.lazy.LazyItemScope  Divider .androidx.compose.foundation.lazy.LazyItemScope  FavoriteItemCard .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  Help .androidx.compose.foundation.lazy.LazyItemScope  HighQuality .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  Info .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  NavigationItemRow .androidx.compose.foundation.lazy.LazyItemScope  	PlayArrow .androidx.compose.foundation.lazy.LazyItemScope  SettingsItem .androidx.compose.foundation.lazy.LazyItemScope  SettingsSection .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  Switch .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  Update .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyItemScope  	getHeight .androidx.compose.foundation.lazy.LazyItemScope  
getPADDING .androidx.compose.foundation.lazy.LazyItemScope  
getPadding .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyListScope  ContentCard .androidx.compose.foundation.lazy.LazyListScope  ContentSection .androidx.compose.foundation.lazy.LazyListScope  DarkMode .androidx.compose.foundation.lazy.LazyListScope  Divider .androidx.compose.foundation.lazy.LazyListScope  FavoriteItemCard .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  Help .androidx.compose.foundation.lazy.LazyListScope  HighQuality .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  Info .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  NavigationItemRow .androidx.compose.foundation.lazy.LazyListScope  	PlayArrow .androidx.compose.foundation.lazy.LazyListScope  SettingsItem .androidx.compose.foundation.lazy.LazyListScope  SettingsSection .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  Switch .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  Update .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyListScope  	getHeight .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  getNAVIGATIONItems .androidx.compose.foundation.lazy.LazyListScope  getNavigationItems .androidx.compose.foundation.lazy.LazyListScope  
getPADDING .androidx.compose.foundation.lazy.LazyListScope  
getPadding .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  invoke .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  navigationItems .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  	GridCells %androidx.compose.foundation.lazy.grid  LazyGridItemScope %androidx.compose.foundation.lazy.grid  
LazyGridScope %androidx.compose.foundation.lazy.grid  LazyVerticalGrid %androidx.compose.foundation.lazy.grid  items %androidx.compose.foundation.lazy.grid  Adaptive /androidx.compose.foundation.lazy.grid.GridCells  LiveChannelCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  	MovieCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  SearchResultCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  
TVShowCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  LiveChannelCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  	MovieCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  SearchResultCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  
TVShowCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  getITEMS 3androidx.compose.foundation.lazy.grid.LazyGridScope  getItems 3androidx.compose.foundation.lazy.grid.LazyGridScope  items 3androidx.compose.foundation.lazy.grid.LazyGridScope  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  DarkMode ,androidx.compose.material.icons.Icons.Filled  Favorite ,androidx.compose.material.icons.Icons.Filled  FavoriteBorder ,androidx.compose.material.icons.Icons.Filled  Help ,androidx.compose.material.icons.Icons.Filled  HighQuality ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  LiveTv ,androidx.compose.material.icons.Icons.Filled  Menu ,androidx.compose.material.icons.Icons.Filled  Movie ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  Star ,androidx.compose.material.icons.Icons.Filled  Tv ,androidx.compose.material.icons.Icons.Filled  Update ,androidx.compose.material.icons.Icons.Filled  	Alignment &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  DarkMode &androidx.compose.material.icons.filled  Divider &androidx.compose.material.icons.filled  Favorite &androidx.compose.material.icons.filled  FavoriteBorder &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Help &androidx.compose.material.icons.filled  HighQuality &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  
LazyColumn &androidx.compose.material.icons.filled  LiveTv &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Menu &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  Movie &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Screen &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  SettingsItem &androidx.compose.material.icons.filled  SettingsSection &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  Star &androidx.compose.material.icons.filled  Switch &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  Tv &androidx.compose.material.icons.filled  Update &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  ContentArea androidx.compose.material3  ContentCard androidx.compose.material3  ContentSection androidx.compose.material3  DarkMode androidx.compose.material3  Divider androidx.compose.material3  FavoriteItemCard androidx.compose.material3  FavoritesScreen androidx.compose.material3  
FontWeight androidx.compose.material3  	GridCells androidx.compose.material3  Help androidx.compose.material3  HighQuality androidx.compose.material3  
HomeScreen androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Info androidx.compose.material3  Key androidx.compose.material3  KeyEventType androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyRow androidx.compose.material3  LazyVerticalGrid androidx.compose.material3  LiveChannelCard androidx.compose.material3  
LiveScreen androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  	MovieCard androidx.compose.material3  MoviesScreen androidx.compose.material3  NavigationItemRow androidx.compose.material3  NavigationToggleButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  
PaddingValues androidx.compose.material3  	PlayArrow androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Screen androidx.compose.material3  SearchResultCard androidx.compose.material3  SearchScreen androidx.compose.material3  Settings androidx.compose.material3  SettingsItem androidx.compose.material3  SettingsScreen androidx.compose.material3  SettingsSection androidx.compose.material3  SideNavigation androidx.compose.material3  Spacer androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  
TVShowCard androidx.compose.material3  
TVShowsScreen androidx.compose.material3  Text androidx.compose.material3  
Typography androidx.compose.material3  Update androidx.compose.material3  androidx androidx.compose.material3  
composable androidx.compose.material3  darkColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  
fillMaxHeight androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  find androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  isEmpty androidx.compose.material3  
isNotEmpty androidx.compose.material3  items androidx.compose.material3  key androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  navigationItems androidx.compose.material3  
onKeyEvent androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  type androidx.compose.material3  width androidx.compose.material3  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  onBackground &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Box androidx.compose.runtime  BoxScope androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  ContentArea androidx.compose.runtime  ContentCard androidx.compose.runtime  ContentSection androidx.compose.runtime  DarkMode androidx.compose.runtime  Divider androidx.compose.runtime  FavoriteItemCard androidx.compose.runtime  FavoritesScreen androidx.compose.runtime  
FontWeight androidx.compose.runtime  	GridCells androidx.compose.runtime  Help androidx.compose.runtime  HighQuality androidx.compose.runtime  
HomeScreen androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Info androidx.compose.runtime  Key androidx.compose.runtime  KeyEventType androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyRow androidx.compose.runtime  LazyVerticalGrid androidx.compose.runtime  LiveChannelCard androidx.compose.runtime  
LiveScreen androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  	MovieCard androidx.compose.runtime  MoviesScreen androidx.compose.runtime  MutableState androidx.compose.runtime  NavigationItemRow androidx.compose.runtime  NavigationToggleButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  
PaddingValues androidx.compose.runtime  	PlayArrow androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  Screen androidx.compose.runtime  SearchResultCard androidx.compose.runtime  SearchScreen androidx.compose.runtime  Settings androidx.compose.runtime  SettingsItem androidx.compose.runtime  SettingsScreen androidx.compose.runtime  SettingsSection androidx.compose.runtime  SideNavigation androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  Surface androidx.compose.runtime  Switch androidx.compose.runtime  
TVShowCard androidx.compose.runtime  
TVShowsScreen androidx.compose.runtime  Text androidx.compose.runtime  Update androidx.compose.runtime  androidx androidx.compose.runtime  
composable androidx.compose.runtime  	emptyList androidx.compose.runtime  
fillMaxHeight androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  find androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  isEmpty androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  items androidx.compose.runtime  key androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  navigationItems androidx.compose.runtime  
onKeyEvent androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  type androidx.compose.runtime  width androidx.compose.runtime  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterStart androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterStart 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  
fillMaxHeight androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  	focusable androidx.compose.ui.Modifier  
getBACKGROUND androidx.compose.ui.Modifier  
getBackground androidx.compose.ui.Modifier  getCLICKABLE androidx.compose.ui.Modifier  getCLIP androidx.compose.ui.Modifier  getClickable androidx.compose.ui.Modifier  getClip androidx.compose.ui.Modifier  getFILLMaxHeight androidx.compose.ui.Modifier  getFILLMaxWidth androidx.compose.ui.Modifier  getFOCUSABLE androidx.compose.ui.Modifier  getFillMaxHeight androidx.compose.ui.Modifier  getFillMaxWidth androidx.compose.ui.Modifier  getFocusable androidx.compose.ui.Modifier  	getHEIGHT androidx.compose.ui.Modifier  	getHeight androidx.compose.ui.Modifier  getONFocusChanged androidx.compose.ui.Modifier  
getONKeyEvent androidx.compose.ui.Modifier  getOnFocusChanged androidx.compose.ui.Modifier  
getOnKeyEvent androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  getWIDTH androidx.compose.ui.Modifier  getWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  onFocusChanged androidx.compose.ui.Modifier  
onKeyEvent androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  then androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  border &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  	getBORDER &androidx.compose.ui.Modifier.Companion  	getBorder &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  FocusRequester androidx.compose.ui.focus  
FocusState androidx.compose.ui.focus  focusRequester androidx.compose.ui.focus  onFocusChanged androidx.compose.ui.focus  	isFocused $androidx.compose.ui.focus.FocusState  Color androidx.compose.ui.graphics  Transparent "androidx.compose.ui.graphics.Color  Transparent ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  	Alignment androidx.compose.ui.input.key  Arrangement androidx.compose.ui.input.key  Box androidx.compose.ui.input.key  BoxScope androidx.compose.ui.input.key  Column androidx.compose.ui.input.key  
Composable androidx.compose.ui.input.key  
FontWeight androidx.compose.ui.input.key  Icon androidx.compose.ui.input.key  Icons androidx.compose.ui.input.key  Key androidx.compose.ui.input.key  KeyEvent androidx.compose.ui.input.key  KeyEventType androidx.compose.ui.input.key  
LazyColumn androidx.compose.ui.input.key  
MaterialTheme androidx.compose.ui.input.key  Modifier androidx.compose.ui.input.key  NavigationItemRow androidx.compose.ui.input.key  NavigationToggleButton androidx.compose.ui.input.key  Row androidx.compose.ui.input.key  Spacer androidx.compose.ui.input.key  Surface androidx.compose.ui.input.key  Text androidx.compose.ui.input.key  androidx androidx.compose.ui.input.key  
fillMaxHeight androidx.compose.ui.input.key  fillMaxSize androidx.compose.ui.input.key  fillMaxWidth androidx.compose.ui.input.key  getValue androidx.compose.ui.input.key  height androidx.compose.ui.input.key  items androidx.compose.ui.input.key  key androidx.compose.ui.input.key  mutableStateOf androidx.compose.ui.input.key  navigationItems androidx.compose.ui.input.key  
onKeyEvent androidx.compose.ui.input.key  padding androidx.compose.ui.input.key  provideDelegate androidx.compose.ui.input.key  remember androidx.compose.ui.input.key  setValue androidx.compose.ui.input.key  size androidx.compose.ui.input.key  type androidx.compose.ui.input.key  width androidx.compose.ui.input.key  DirectionCenter !androidx.compose.ui.input.key.Key  Enter !androidx.compose.ui.input.key.Key  equals !androidx.compose.ui.input.key.Key  DirectionCenter +androidx.compose.ui.input.key.Key.Companion  Enter +androidx.compose.ui.input.key.Key.Companion  getKEY &androidx.compose.ui.input.key.KeyEvent  getKey &androidx.compose.ui.input.key.KeyEvent  getTYPE &androidx.compose.ui.input.key.KeyEvent  getType &androidx.compose.ui.input.key.KeyEvent  key &androidx.compose.ui.input.key.KeyEvent  type &androidx.compose.ui.input.key.KeyEvent  KeyUp *androidx.compose.ui.input.key.KeyEventType  equals *androidx.compose.ui.input.key.KeyEventType  KeyUp 4androidx.compose.ui.input.key.KeyEventType.Companion  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  
LitchiTVTheme #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  navigate !androidx.navigation.NavController  startDestinationId androidx.navigation.NavGraph  FavoritesScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  
LiveScreen #androidx.navigation.NavGraphBuilder  MoviesScreen #androidx.navigation.NavGraphBuilder  SearchScreen #androidx.navigation.NavGraphBuilder  SettingsScreen #androidx.navigation.NavGraphBuilder  
TVShowsScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  
getCOMPOSABLE #androidx.navigation.NavGraphBuilder  
getComposable #androidx.navigation.NavGraphBuilder  graph %androidx.navigation.NavHostController  navigate %androidx.navigation.NavHostController  invoke %androidx.navigation.NavOptionsBuilder  launchSingleTop %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  restoreState %androidx.navigation.NavOptionsBuilder  	saveState "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  
LitchiTVTheme com.google.chuangke  MainActivity com.google.chuangke  
MainScreen com.google.chuangke  
MaterialTheme com.google.chuangke  Modifier com.google.chuangke  Surface com.google.chuangke  fillMaxSize com.google.chuangke  
setContent com.google.chuangke  Bundle  com.google.chuangke.MainActivity  
LitchiTVTheme  com.google.chuangke.MainActivity  
MainScreen  com.google.chuangke.MainActivity  
MaterialTheme  com.google.chuangke.MainActivity  Modifier  com.google.chuangke.MainActivity  Surface  com.google.chuangke.MainActivity  fillMaxSize  com.google.chuangke.MainActivity  getFILLMaxSize  com.google.chuangke.MainActivity  getFillMaxSize  com.google.chuangke.MainActivity  
getSETContent  com.google.chuangke.MainActivity  
getSetContent  com.google.chuangke.MainActivity  
setContent  com.google.chuangke.MainActivity  Favorite com.google.chuangke.navigation  Home com.google.chuangke.navigation  LiveTv com.google.chuangke.navigation  Movie com.google.chuangke.navigation  NavigationItem com.google.chuangke.navigation  Screen com.google.chuangke.navigation  Search com.google.chuangke.navigation  Settings com.google.chuangke.navigation  String com.google.chuangke.navigation  Tv com.google.chuangke.navigation  listOf com.google.chuangke.navigation  navigationItems com.google.chuangke.navigation  ImageVector -com.google.chuangke.navigation.NavigationItem  Screen -com.google.chuangke.navigation.NavigationItem  String -com.google.chuangke.navigation.NavigationItem  icon -com.google.chuangke.navigation.NavigationItem  route -com.google.chuangke.navigation.NavigationItem  screen -com.google.chuangke.navigation.NavigationItem  title -com.google.chuangke.navigation.NavigationItem  	FAVORITES %com.google.chuangke.navigation.Screen  HOME %com.google.chuangke.navigation.Screen  LIVE %com.google.chuangke.navigation.Screen  MOVIES %com.google.chuangke.navigation.Screen  SEARCH %com.google.chuangke.navigation.Screen  SETTINGS %com.google.chuangke.navigation.Screen  TV_SHOWS %com.google.chuangke.navigation.Screen  equals %com.google.chuangke.navigation.Screen  	Alignment !com.google.chuangke.ui.components  Arrangement !com.google.chuangke.ui.components  Boolean !com.google.chuangke.ui.components  Box !com.google.chuangke.ui.components  BoxScope !com.google.chuangke.ui.components  Column !com.google.chuangke.ui.components  
Composable !com.google.chuangke.ui.components  
FontWeight !com.google.chuangke.ui.components  Icon !com.google.chuangke.ui.components  Icons !com.google.chuangke.ui.components  Key !com.google.chuangke.ui.components  KeyEventType !com.google.chuangke.ui.components  
LazyColumn !com.google.chuangke.ui.components  
MaterialTheme !com.google.chuangke.ui.components  Modifier !com.google.chuangke.ui.components  NavigationItemRow !com.google.chuangke.ui.components  NavigationToggleButton !com.google.chuangke.ui.components  Row !com.google.chuangke.ui.components  SideNavigation !com.google.chuangke.ui.components  Spacer !com.google.chuangke.ui.components  Surface !com.google.chuangke.ui.components  TVButton !com.google.chuangke.ui.components  TVCard !com.google.chuangke.ui.components  TVFocusable !com.google.chuangke.ui.components  Text !com.google.chuangke.ui.components  Unit !com.google.chuangke.ui.components  androidx !com.google.chuangke.ui.components  
fillMaxHeight !com.google.chuangke.ui.components  fillMaxSize !com.google.chuangke.ui.components  fillMaxWidth !com.google.chuangke.ui.components  getValue !com.google.chuangke.ui.components  height !com.google.chuangke.ui.components  items !com.google.chuangke.ui.components  key !com.google.chuangke.ui.components  mutableStateOf !com.google.chuangke.ui.components  navigationItems !com.google.chuangke.ui.components  
onKeyEvent !com.google.chuangke.ui.components  padding !com.google.chuangke.ui.components  provideDelegate !com.google.chuangke.ui.components  remember !com.google.chuangke.ui.components  setValue !com.google.chuangke.ui.components  size !com.google.chuangke.ui.components  type !com.google.chuangke.ui.components  width !com.google.chuangke.ui.components  	Alignment com.google.chuangke.ui.screens  Arrangement com.google.chuangke.ui.screens  Box com.google.chuangke.ui.screens  Button com.google.chuangke.ui.screens  Card com.google.chuangke.ui.screens  CardDefaults com.google.chuangke.ui.screens  Column com.google.chuangke.ui.screens  
Composable com.google.chuangke.ui.screens  ContentArea com.google.chuangke.ui.screens  ContentCard com.google.chuangke.ui.screens  ContentSection com.google.chuangke.ui.screens  DarkMode com.google.chuangke.ui.screens  Divider com.google.chuangke.ui.screens  FavoriteItem com.google.chuangke.ui.screens  FavoriteItemCard com.google.chuangke.ui.screens  FavoritesScreen com.google.chuangke.ui.screens  
FontWeight com.google.chuangke.ui.screens  	GridCells com.google.chuangke.ui.screens  Help com.google.chuangke.ui.screens  HighQuality com.google.chuangke.ui.screens  
HomeScreen com.google.chuangke.ui.screens  Icon com.google.chuangke.ui.screens  
IconButton com.google.chuangke.ui.screens  Icons com.google.chuangke.ui.screens  Info com.google.chuangke.ui.screens  
LazyColumn com.google.chuangke.ui.screens  LazyRow com.google.chuangke.ui.screens  LazyVerticalGrid com.google.chuangke.ui.screens  List com.google.chuangke.ui.screens  LiveChannelCard com.google.chuangke.ui.screens  
LiveScreen com.google.chuangke.ui.screens  
MainScreen com.google.chuangke.ui.screens  
MaterialTheme com.google.chuangke.ui.screens  Modifier com.google.chuangke.ui.screens  Movie com.google.chuangke.ui.screens  	MovieCard com.google.chuangke.ui.screens  MoviesScreen com.google.chuangke.ui.screens  OutlinedTextField com.google.chuangke.ui.screens  
PaddingValues com.google.chuangke.ui.screens  	PlayArrow com.google.chuangke.ui.screens  RoundedCornerShape com.google.chuangke.ui.screens  Row com.google.chuangke.ui.screens  Screen com.google.chuangke.ui.screens  SearchResultCard com.google.chuangke.ui.screens  SearchScreen com.google.chuangke.ui.screens  Settings com.google.chuangke.ui.screens  SettingsItem com.google.chuangke.ui.screens  SettingsScreen com.google.chuangke.ui.screens  SettingsSection com.google.chuangke.ui.screens  SideNavigation com.google.chuangke.ui.screens  Spacer com.google.chuangke.ui.screens  String com.google.chuangke.ui.screens  Surface com.google.chuangke.ui.screens  Switch com.google.chuangke.ui.screens  TVShow com.google.chuangke.ui.screens  
TVShowCard com.google.chuangke.ui.screens  
TVShowsScreen com.google.chuangke.ui.screens  Text com.google.chuangke.ui.screens  Unit com.google.chuangke.ui.screens  Update com.google.chuangke.ui.screens  androidx com.google.chuangke.ui.screens  
composable com.google.chuangke.ui.screens  	emptyList com.google.chuangke.ui.screens  fillMaxSize com.google.chuangke.ui.screens  fillMaxWidth com.google.chuangke.ui.screens  find com.google.chuangke.ui.screens  getValue com.google.chuangke.ui.screens  height com.google.chuangke.ui.screens  isEmpty com.google.chuangke.ui.screens  
isNotEmpty com.google.chuangke.ui.screens  items com.google.chuangke.ui.screens  listOf com.google.chuangke.ui.screens  mutableStateOf com.google.chuangke.ui.screens  navigationItems com.google.chuangke.ui.screens  padding com.google.chuangke.ui.screens  provideDelegate com.google.chuangke.ui.screens  remember com.google.chuangke.ui.screens  setValue com.google.chuangke.ui.screens  size com.google.chuangke.ui.screens  width com.google.chuangke.ui.screens  String +com.google.chuangke.ui.screens.FavoriteItem  rating +com.google.chuangke.ui.screens.FavoriteItem  title +com.google.chuangke.ui.screens.FavoriteItem  type +com.google.chuangke.ui.screens.FavoriteItem  year +com.google.chuangke.ui.screens.FavoriteItem  String $com.google.chuangke.ui.screens.Movie  genre $com.google.chuangke.ui.screens.Movie  rating $com.google.chuangke.ui.screens.Movie  title $com.google.chuangke.ui.screens.Movie  year $com.google.chuangke.ui.screens.Movie  String %com.google.chuangke.ui.screens.TVShow  episodes %com.google.chuangke.ui.screens.TVShow  genre %com.google.chuangke.ui.screens.TVShow  rating %com.google.chuangke.ui.screens.TVShow  title %com.google.chuangke.ui.screens.TVShow  year %com.google.chuangke.ui.screens.TVShow  Boolean com.google.chuangke.ui.theme  DarkBackground com.google.chuangke.ui.theme  DarkColorScheme com.google.chuangke.ui.theme  DarkOutline com.google.chuangke.ui.theme  DarkOutlineVariant com.google.chuangke.ui.theme  DarkSurface com.google.chuangke.ui.theme  DarkSurfaceVariant com.google.chuangke.ui.theme  LightColorScheme com.google.chuangke.ui.theme  
LitchiTVTheme com.google.chuangke.ui.theme  OnDarkBackground com.google.chuangke.ui.theme  
OnDarkPrimary com.google.chuangke.ui.theme  OnDarkSecondary com.google.chuangke.ui.theme  
OnDarkSurface com.google.chuangke.ui.theme  OnDarkSurfaceVariant com.google.chuangke.ui.theme  Purple20 com.google.chuangke.ui.theme  Purple40 com.google.chuangke.ui.theme  Purple80 com.google.chuangke.ui.theme  Red40 com.google.chuangke.ui.theme  Red80 com.google.chuangke.ui.theme  Teal40 com.google.chuangke.ui.theme  Teal80 com.google.chuangke.ui.theme  
Typography com.google.chuangke.ui.theme  Unit com.google.chuangke.ui.theme  	Alignment 	java.lang  Arrangement 	java.lang  Box 	java.lang  Button 	java.lang  Card 	java.lang  CardDefaults 	java.lang  Column 	java.lang  ContentArea 	java.lang  ContentCard 	java.lang  ContentSection 	java.lang  Divider 	java.lang  FavoriteItemCard 	java.lang  FavoritesScreen 	java.lang  
FontWeight 	java.lang  	GridCells 	java.lang  
HomeScreen 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  
LazyColumn 	java.lang  LazyRow 	java.lang  LazyVerticalGrid 	java.lang  
LitchiTVTheme 	java.lang  LiveChannelCard 	java.lang  
LiveScreen 	java.lang  
MainScreen 	java.lang  
MaterialTheme 	java.lang  Modifier 	java.lang  	MovieCard 	java.lang  MoviesScreen 	java.lang  NavigationItemRow 	java.lang  NavigationToggleButton 	java.lang  OutlinedTextField 	java.lang  
PaddingValues 	java.lang  RoundedCornerShape 	java.lang  Row 	java.lang  Screen 	java.lang  SearchResultCard 	java.lang  SearchScreen 	java.lang  SettingsItem 	java.lang  SettingsScreen 	java.lang  SettingsSection 	java.lang  SideNavigation 	java.lang  Spacer 	java.lang  Surface 	java.lang  Switch 	java.lang  
TVShowCard 	java.lang  
TVShowsScreen 	java.lang  Text 	java.lang  androidx 	java.lang  	emptyList 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  find 	java.lang  height 	java.lang  isEmpty 	java.lang  
isNotEmpty 	java.lang  listOf 	java.lang  navigationItems 	java.lang  padding 	java.lang  provideDelegate 	java.lang  size 	java.lang  width 	java.lang  	Alignment kotlin  Arrangement kotlin  Boolean kotlin  Box kotlin  Button kotlin  Card kotlin  CardDefaults kotlin  Column kotlin  ContentArea kotlin  ContentCard kotlin  ContentSection kotlin  Divider kotlin  Double kotlin  FavoriteItemCard kotlin  FavoritesScreen kotlin  Float kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	GridCells kotlin  
HomeScreen kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  Int kotlin  
LazyColumn kotlin  LazyRow kotlin  LazyVerticalGrid kotlin  
LitchiTVTheme kotlin  LiveChannelCard kotlin  
LiveScreen kotlin  
MainScreen kotlin  
MaterialTheme kotlin  Modifier kotlin  	MovieCard kotlin  MoviesScreen kotlin  NavigationItemRow kotlin  NavigationToggleButton kotlin  Nothing kotlin  OutlinedTextField kotlin  
PaddingValues kotlin  RoundedCornerShape kotlin  Row kotlin  Screen kotlin  SearchResultCard kotlin  SearchScreen kotlin  SettingsItem kotlin  SettingsScreen kotlin  SettingsSection kotlin  SideNavigation kotlin  Spacer kotlin  String kotlin  Surface kotlin  Switch kotlin  
TVShowCard kotlin  
TVShowsScreen kotlin  Text kotlin  Unit kotlin  androidx kotlin  	emptyList kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  find kotlin  height kotlin  isEmpty kotlin  
isNotEmpty kotlin  listOf kotlin  navigationItems kotlin  padding kotlin  provideDelegate kotlin  size kotlin  width kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  
getISEmpty 
kotlin.String  
getISNotEmpty 
kotlin.String  
getIsEmpty 
kotlin.String  
getIsNotEmpty 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  	Alignment kotlin.annotation  Arrangement kotlin.annotation  Box kotlin.annotation  Button kotlin.annotation  Card kotlin.annotation  CardDefaults kotlin.annotation  Column kotlin.annotation  ContentArea kotlin.annotation  ContentCard kotlin.annotation  ContentSection kotlin.annotation  Divider kotlin.annotation  FavoriteItemCard kotlin.annotation  FavoritesScreen kotlin.annotation  
FontWeight kotlin.annotation  	GridCells kotlin.annotation  
HomeScreen kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  
LazyColumn kotlin.annotation  LazyRow kotlin.annotation  LazyVerticalGrid kotlin.annotation  
LitchiTVTheme kotlin.annotation  LiveChannelCard kotlin.annotation  
LiveScreen kotlin.annotation  
MainScreen kotlin.annotation  
MaterialTheme kotlin.annotation  Modifier kotlin.annotation  	MovieCard kotlin.annotation  MoviesScreen kotlin.annotation  NavigationItemRow kotlin.annotation  NavigationToggleButton kotlin.annotation  OutlinedTextField kotlin.annotation  
PaddingValues kotlin.annotation  RoundedCornerShape kotlin.annotation  Row kotlin.annotation  Screen kotlin.annotation  SearchResultCard kotlin.annotation  SearchScreen kotlin.annotation  SettingsItem kotlin.annotation  SettingsScreen kotlin.annotation  SettingsSection kotlin.annotation  SideNavigation kotlin.annotation  Spacer kotlin.annotation  Surface kotlin.annotation  Switch kotlin.annotation  
TVShowCard kotlin.annotation  
TVShowsScreen kotlin.annotation  Text kotlin.annotation  androidx kotlin.annotation  	emptyList kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  find kotlin.annotation  height kotlin.annotation  isEmpty kotlin.annotation  
isNotEmpty kotlin.annotation  listOf kotlin.annotation  navigationItems kotlin.annotation  padding kotlin.annotation  provideDelegate kotlin.annotation  size kotlin.annotation  width kotlin.annotation  	Alignment kotlin.collections  Arrangement kotlin.collections  Box kotlin.collections  Button kotlin.collections  Card kotlin.collections  CardDefaults kotlin.collections  Column kotlin.collections  ContentArea kotlin.collections  ContentCard kotlin.collections  ContentSection kotlin.collections  Divider kotlin.collections  FavoriteItemCard kotlin.collections  FavoritesScreen kotlin.collections  
FontWeight kotlin.collections  	GridCells kotlin.collections  
HomeScreen kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  
LazyColumn kotlin.collections  LazyRow kotlin.collections  LazyVerticalGrid kotlin.collections  List kotlin.collections  
LitchiTVTheme kotlin.collections  LiveChannelCard kotlin.collections  
LiveScreen kotlin.collections  
MainScreen kotlin.collections  
MaterialTheme kotlin.collections  Modifier kotlin.collections  	MovieCard kotlin.collections  MoviesScreen kotlin.collections  NavigationItemRow kotlin.collections  NavigationToggleButton kotlin.collections  OutlinedTextField kotlin.collections  
PaddingValues kotlin.collections  RoundedCornerShape kotlin.collections  Row kotlin.collections  Screen kotlin.collections  SearchResultCard kotlin.collections  SearchScreen kotlin.collections  SettingsItem kotlin.collections  SettingsScreen kotlin.collections  SettingsSection kotlin.collections  SideNavigation kotlin.collections  Spacer kotlin.collections  Surface kotlin.collections  Switch kotlin.collections  
TVShowCard kotlin.collections  
TVShowsScreen kotlin.collections  Text kotlin.collections  androidx kotlin.collections  	emptyList kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  find kotlin.collections  height kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  navigationItems kotlin.collections  padding kotlin.collections  provideDelegate kotlin.collections  size kotlin.collections  width kotlin.collections  getFIND kotlin.collections.List  getFind kotlin.collections.List  
getISNotEmpty kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  	Alignment kotlin.comparisons  Arrangement kotlin.comparisons  Box kotlin.comparisons  Button kotlin.comparisons  Card kotlin.comparisons  CardDefaults kotlin.comparisons  Column kotlin.comparisons  ContentArea kotlin.comparisons  ContentCard kotlin.comparisons  ContentSection kotlin.comparisons  Divider kotlin.comparisons  FavoriteItemCard kotlin.comparisons  FavoritesScreen kotlin.comparisons  
FontWeight kotlin.comparisons  	GridCells kotlin.comparisons  
HomeScreen kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  
LazyColumn kotlin.comparisons  LazyRow kotlin.comparisons  LazyVerticalGrid kotlin.comparisons  
LitchiTVTheme kotlin.comparisons  LiveChannelCard kotlin.comparisons  
LiveScreen kotlin.comparisons  
MainScreen kotlin.comparisons  
MaterialTheme kotlin.comparisons  Modifier kotlin.comparisons  	MovieCard kotlin.comparisons  MoviesScreen kotlin.comparisons  NavigationItemRow kotlin.comparisons  NavigationToggleButton kotlin.comparisons  OutlinedTextField kotlin.comparisons  
PaddingValues kotlin.comparisons  RoundedCornerShape kotlin.comparisons  Row kotlin.comparisons  Screen kotlin.comparisons  SearchResultCard kotlin.comparisons  SearchScreen kotlin.comparisons  SettingsItem kotlin.comparisons  SettingsScreen kotlin.comparisons  SettingsSection kotlin.comparisons  SideNavigation kotlin.comparisons  Spacer kotlin.comparisons  Surface kotlin.comparisons  Switch kotlin.comparisons  
TVShowCard kotlin.comparisons  
TVShowsScreen kotlin.comparisons  Text kotlin.comparisons  androidx kotlin.comparisons  	emptyList kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  find kotlin.comparisons  height kotlin.comparisons  isEmpty kotlin.comparisons  
isNotEmpty kotlin.comparisons  listOf kotlin.comparisons  navigationItems kotlin.comparisons  padding kotlin.comparisons  provideDelegate kotlin.comparisons  size kotlin.comparisons  width kotlin.comparisons  	Alignment 	kotlin.io  Arrangement 	kotlin.io  Box 	kotlin.io  Button 	kotlin.io  Card 	kotlin.io  CardDefaults 	kotlin.io  Column 	kotlin.io  ContentArea 	kotlin.io  ContentCard 	kotlin.io  ContentSection 	kotlin.io  Divider 	kotlin.io  FavoriteItemCard 	kotlin.io  FavoritesScreen 	kotlin.io  
FontWeight 	kotlin.io  	GridCells 	kotlin.io  
HomeScreen 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  
LazyColumn 	kotlin.io  LazyRow 	kotlin.io  LazyVerticalGrid 	kotlin.io  
LitchiTVTheme 	kotlin.io  LiveChannelCard 	kotlin.io  
LiveScreen 	kotlin.io  
MainScreen 	kotlin.io  
MaterialTheme 	kotlin.io  Modifier 	kotlin.io  	MovieCard 	kotlin.io  MoviesScreen 	kotlin.io  NavigationItemRow 	kotlin.io  NavigationToggleButton 	kotlin.io  OutlinedTextField 	kotlin.io  
PaddingValues 	kotlin.io  RoundedCornerShape 	kotlin.io  Row 	kotlin.io  Screen 	kotlin.io  SearchResultCard 	kotlin.io  SearchScreen 	kotlin.io  SettingsItem 	kotlin.io  SettingsScreen 	kotlin.io  SettingsSection 	kotlin.io  SideNavigation 	kotlin.io  Spacer 	kotlin.io  Surface 	kotlin.io  Switch 	kotlin.io  
TVShowCard 	kotlin.io  
TVShowsScreen 	kotlin.io  Text 	kotlin.io  androidx 	kotlin.io  	emptyList 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  find 	kotlin.io  height 	kotlin.io  isEmpty 	kotlin.io  
isNotEmpty 	kotlin.io  listOf 	kotlin.io  navigationItems 	kotlin.io  padding 	kotlin.io  provideDelegate 	kotlin.io  size 	kotlin.io  width 	kotlin.io  	Alignment 
kotlin.jvm  Arrangement 
kotlin.jvm  Box 
kotlin.jvm  Button 
kotlin.jvm  Card 
kotlin.jvm  CardDefaults 
kotlin.jvm  Column 
kotlin.jvm  ContentArea 
kotlin.jvm  ContentCard 
kotlin.jvm  ContentSection 
kotlin.jvm  Divider 
kotlin.jvm  FavoriteItemCard 
kotlin.jvm  FavoritesScreen 
kotlin.jvm  
FontWeight 
kotlin.jvm  	GridCells 
kotlin.jvm  
HomeScreen 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  
LazyColumn 
kotlin.jvm  LazyRow 
kotlin.jvm  LazyVerticalGrid 
kotlin.jvm  
LitchiTVTheme 
kotlin.jvm  LiveChannelCard 
kotlin.jvm  
LiveScreen 
kotlin.jvm  
MainScreen 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Modifier 
kotlin.jvm  	MovieCard 
kotlin.jvm  MoviesScreen 
kotlin.jvm  NavigationItemRow 
kotlin.jvm  NavigationToggleButton 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  
PaddingValues 
kotlin.jvm  RoundedCornerShape 
kotlin.jvm  Row 
kotlin.jvm  Screen 
kotlin.jvm  SearchResultCard 
kotlin.jvm  SearchScreen 
kotlin.jvm  SettingsItem 
kotlin.jvm  SettingsScreen 
kotlin.jvm  SettingsSection 
kotlin.jvm  SideNavigation 
kotlin.jvm  Spacer 
kotlin.jvm  Surface 
kotlin.jvm  Switch 
kotlin.jvm  
TVShowCard 
kotlin.jvm  
TVShowsScreen 
kotlin.jvm  Text 
kotlin.jvm  androidx 
kotlin.jvm  	emptyList 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  find 
kotlin.jvm  height 
kotlin.jvm  isEmpty 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  listOf 
kotlin.jvm  navigationItems 
kotlin.jvm  padding 
kotlin.jvm  provideDelegate 
kotlin.jvm  size 
kotlin.jvm  width 
kotlin.jvm  	Alignment 
kotlin.ranges  Arrangement 
kotlin.ranges  Box 
kotlin.ranges  Button 
kotlin.ranges  Card 
kotlin.ranges  CardDefaults 
kotlin.ranges  Column 
kotlin.ranges  ContentArea 
kotlin.ranges  ContentCard 
kotlin.ranges  ContentSection 
kotlin.ranges  Divider 
kotlin.ranges  FavoriteItemCard 
kotlin.ranges  FavoritesScreen 
kotlin.ranges  
FontWeight 
kotlin.ranges  	GridCells 
kotlin.ranges  
HomeScreen 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  
LazyColumn 
kotlin.ranges  LazyRow 
kotlin.ranges  LazyVerticalGrid 
kotlin.ranges  
LitchiTVTheme 
kotlin.ranges  LiveChannelCard 
kotlin.ranges  
LiveScreen 
kotlin.ranges  
MainScreen 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Modifier 
kotlin.ranges  	MovieCard 
kotlin.ranges  MoviesScreen 
kotlin.ranges  NavigationItemRow 
kotlin.ranges  NavigationToggleButton 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  
PaddingValues 
kotlin.ranges  RoundedCornerShape 
kotlin.ranges  Row 
kotlin.ranges  Screen 
kotlin.ranges  SearchResultCard 
kotlin.ranges  SearchScreen 
kotlin.ranges  SettingsItem 
kotlin.ranges  SettingsScreen 
kotlin.ranges  SettingsSection 
kotlin.ranges  SideNavigation 
kotlin.ranges  Spacer 
kotlin.ranges  Surface 
kotlin.ranges  Switch 
kotlin.ranges  
TVShowCard 
kotlin.ranges  
TVShowsScreen 
kotlin.ranges  Text 
kotlin.ranges  androidx 
kotlin.ranges  	emptyList 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  find 
kotlin.ranges  height 
kotlin.ranges  isEmpty 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  listOf 
kotlin.ranges  navigationItems 
kotlin.ranges  padding 
kotlin.ranges  provideDelegate 
kotlin.ranges  size 
kotlin.ranges  width 
kotlin.ranges  	Alignment kotlin.sequences  Arrangement kotlin.sequences  Box kotlin.sequences  Button kotlin.sequences  Card kotlin.sequences  CardDefaults kotlin.sequences  Column kotlin.sequences  ContentArea kotlin.sequences  ContentCard kotlin.sequences  ContentSection kotlin.sequences  Divider kotlin.sequences  FavoriteItemCard kotlin.sequences  FavoritesScreen kotlin.sequences  
FontWeight kotlin.sequences  	GridCells kotlin.sequences  
HomeScreen kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  
LazyColumn kotlin.sequences  LazyRow kotlin.sequences  LazyVerticalGrid kotlin.sequences  
LitchiTVTheme kotlin.sequences  LiveChannelCard kotlin.sequences  
LiveScreen kotlin.sequences  
MainScreen kotlin.sequences  
MaterialTheme kotlin.sequences  Modifier kotlin.sequences  	MovieCard kotlin.sequences  MoviesScreen kotlin.sequences  NavigationItemRow kotlin.sequences  NavigationToggleButton kotlin.sequences  OutlinedTextField kotlin.sequences  
PaddingValues kotlin.sequences  RoundedCornerShape kotlin.sequences  Row kotlin.sequences  Screen kotlin.sequences  SearchResultCard kotlin.sequences  SearchScreen kotlin.sequences  SettingsItem kotlin.sequences  SettingsScreen kotlin.sequences  SettingsSection kotlin.sequences  SideNavigation kotlin.sequences  Spacer kotlin.sequences  Surface kotlin.sequences  Switch kotlin.sequences  
TVShowCard kotlin.sequences  
TVShowsScreen kotlin.sequences  Text kotlin.sequences  androidx kotlin.sequences  	emptyList kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  find kotlin.sequences  height kotlin.sequences  isEmpty kotlin.sequences  
isNotEmpty kotlin.sequences  listOf kotlin.sequences  navigationItems kotlin.sequences  padding kotlin.sequences  provideDelegate kotlin.sequences  size kotlin.sequences  width kotlin.sequences  	Alignment kotlin.text  Arrangement kotlin.text  Box kotlin.text  Button kotlin.text  Card kotlin.text  CardDefaults kotlin.text  Column kotlin.text  ContentArea kotlin.text  ContentCard kotlin.text  ContentSection kotlin.text  Divider kotlin.text  FavoriteItemCard kotlin.text  FavoritesScreen kotlin.text  
FontWeight kotlin.text  	GridCells kotlin.text  
HomeScreen kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  
LazyColumn kotlin.text  LazyRow kotlin.text  LazyVerticalGrid kotlin.text  
LitchiTVTheme kotlin.text  LiveChannelCard kotlin.text  
LiveScreen kotlin.text  
MainScreen kotlin.text  
MaterialTheme kotlin.text  Modifier kotlin.text  	MovieCard kotlin.text  MoviesScreen kotlin.text  NavigationItemRow kotlin.text  NavigationToggleButton kotlin.text  OutlinedTextField kotlin.text  
PaddingValues kotlin.text  RoundedCornerShape kotlin.text  Row kotlin.text  Screen kotlin.text  SearchResultCard kotlin.text  SearchScreen kotlin.text  SettingsItem kotlin.text  SettingsScreen kotlin.text  SettingsSection kotlin.text  SideNavigation kotlin.text  Spacer kotlin.text  Surface kotlin.text  Switch kotlin.text  
TVShowCard kotlin.text  
TVShowsScreen kotlin.text  Text kotlin.text  androidx kotlin.text  	emptyList kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  find kotlin.text  height kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  listOf kotlin.text  navigationItems kotlin.text  padding kotlin.text  provideDelegate kotlin.text  size kotlin.text  width kotlin.text  MediaPosterCard !com.google.chuangke.ui.components  	MediaType com.google.chuangke.data.model  
SampleData #com.google.chuangke.data.repository  	Companion :com.google.chuangke.data.repository.WatchHistoryRepository  
PosterSize !com.google.chuangke.ui.components  
WatchStats #com.google.chuangke.data.repository  WatchHistory com.google.chuangke.data.model  MediaPosterGrid !com.google.chuangke.ui.components  TVShow com.google.chuangke.data.model  Person com.google.chuangke.data.model  MediaPosterRow !com.google.chuangke.ui.components  LibraryType com.google.chuangke.data.model  MediaDetailScreen com.google.chuangke.ui.screens  WatchHistoryRepository #com.google.chuangke.data.repository  ContinueWatchingItem com.google.chuangke.data.model  Recommendation com.google.chuangke.data.model  SearchResult com.google.chuangke.data.model  Season com.google.chuangke.data.model  MediaLibrary com.google.chuangke.data.model  	MediaItem com.google.chuangke.data.model  Episode com.google.chuangke.data.model  	ArrowBack "androidx.compose.foundation.layout  ArrowForward "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  CastCard "androidx.compose.foundation.layout  CastSection "androidx.compose.foundation.layout  Check "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  ContinueWatchingCard "androidx.compose.foundation.layout  ContinueWatchingSection "androidx.compose.foundation.layout  Favorite "androidx.compose.foundation.layout  FavoriteBorder "androidx.compose.foundation.layout  
FilterChip "androidx.compose.foundation.layout  
FilterSection "androidx.compose.foundation.layout  HeroSection "androidx.compose.foundation.layout  InfoRow "androidx.compose.foundation.layout  LiveTv "androidx.compose.foundation.layout  MediaDetailHero "androidx.compose.foundation.layout  MediaDetailInfo "androidx.compose.foundation.layout  MediaLibrariesSection "androidx.compose.foundation.layout  MediaLibraryCard "androidx.compose.foundation.layout  MediaPosterCard "androidx.compose.foundation.layout  MediaPosterGrid "androidx.compose.foundation.layout  MediaPosterRow "androidx.compose.foundation.layout  	MediaType "androidx.compose.foundation.layout  Movie "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  Person "androidx.compose.foundation.layout  
PosterSize "androidx.compose.foundation.layout  
SampleData "androidx.compose.foundation.layout  Star "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TextOverflow "androidx.compose.foundation.layout  Tv "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  filter "androidx.compose.foundation.layout  first "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  invoke "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  sortedBy "androidx.compose.foundation.layout  sortedByDescending "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  	ArrowBack +androidx.compose.foundation.layout.BoxScope  Brush +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  Check +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Favorite +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Info +androidx.compose.foundation.layout.BoxScope  LazyRow +androidx.compose.foundation.layout.BoxScope  LiveTv +androidx.compose.foundation.layout.BoxScope  	MediaType +androidx.compose.foundation.layout.BoxScope  Movie +androidx.compose.foundation.layout.BoxScope  OutlinedButton +androidx.compose.foundation.layout.BoxScope  Person +androidx.compose.foundation.layout.BoxScope  	PlayArrow +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Star +androidx.compose.foundation.layout.BoxScope  String +androidx.compose.foundation.layout.BoxScope  Surface +androidx.compose.foundation.layout.BoxScope  TextOverflow +androidx.compose.foundation.layout.BoxScope  Tv +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  
fillMaxHeight +androidx.compose.foundation.layout.BoxScope  format +androidx.compose.foundation.layout.BoxScope  
getBACKGROUND +androidx.compose.foundation.layout.BoxScope  
getBackground +androidx.compose.foundation.layout.BoxScope  getCLIP +androidx.compose.foundation.layout.BoxScope  getClip +androidx.compose.foundation.layout.BoxScope  getFILLMaxHeight +androidx.compose.foundation.layout.BoxScope  	getFORMAT +androidx.compose.foundation.layout.BoxScope  getFillMaxHeight +androidx.compose.foundation.layout.BoxScope  	getFormat +androidx.compose.foundation.layout.BoxScope  
getISNotEmpty +androidx.compose.foundation.layout.BoxScope  
getIsNotEmpty +androidx.compose.foundation.layout.BoxScope  getLET +androidx.compose.foundation.layout.BoxScope  	getLISTOf +androidx.compose.foundation.layout.BoxScope  getLet +androidx.compose.foundation.layout.BoxScope  	getListOf +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  items +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  ArrowForward .androidx.compose.foundation.layout.ColumnScope  Brush .androidx.compose.foundation.layout.ColumnScope  CastCard .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  ContinueWatchingCard .androidx.compose.foundation.layout.ColumnScope  
FilterChip .androidx.compose.foundation.layout.ColumnScope  
FilterSection .androidx.compose.foundation.layout.ColumnScope  InfoRow .androidx.compose.foundation.layout.ColumnScope  MediaLibraryCard .androidx.compose.foundation.layout.ColumnScope  MediaPosterCard .androidx.compose.foundation.layout.ColumnScope  	MediaType .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  Person .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  Surface .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  TextOverflow .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  
fillMaxHeight .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  
getBACKGROUND .androidx.compose.foundation.layout.ColumnScope  
getBackground .androidx.compose.foundation.layout.ColumnScope  getCLIP .androidx.compose.foundation.layout.ColumnScope  getClip .androidx.compose.foundation.layout.ColumnScope  getFILLMaxHeight .androidx.compose.foundation.layout.ColumnScope  	getFORMAT .androidx.compose.foundation.layout.ColumnScope  getFillMaxHeight .androidx.compose.foundation.layout.ColumnScope  	getFormat .androidx.compose.foundation.layout.ColumnScope  getLET .androidx.compose.foundation.layout.ColumnScope  getLet .androidx.compose.foundation.layout.ColumnScope  getTAKE .androidx.compose.foundation.layout.ColumnScope  getTake .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  take .androidx.compose.foundation.layout.ColumnScope  Arrangement +androidx.compose.foundation.layout.RowScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  ArrowForward +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Brush +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  FavoriteBorder +androidx.compose.foundation.layout.RowScope  Info +androidx.compose.foundation.layout.RowScope  LazyRow +androidx.compose.foundation.layout.RowScope  	MediaType +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  String +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  
fillMaxHeight +androidx.compose.foundation.layout.RowScope  fillMaxWidth +androidx.compose.foundation.layout.RowScope  format +androidx.compose.foundation.layout.RowScope  
getBACKGROUND +androidx.compose.foundation.layout.RowScope  
getBackground +androidx.compose.foundation.layout.RowScope  getCLIP +androidx.compose.foundation.layout.RowScope  getClip +androidx.compose.foundation.layout.RowScope  getFILLMaxHeight +androidx.compose.foundation.layout.RowScope  getFILLMaxWidth +androidx.compose.foundation.layout.RowScope  	getFORMAT +androidx.compose.foundation.layout.RowScope  getFillMaxHeight +androidx.compose.foundation.layout.RowScope  getFillMaxWidth +androidx.compose.foundation.layout.RowScope  	getFormat +androidx.compose.foundation.layout.RowScope  
getISNotEmpty +androidx.compose.foundation.layout.RowScope  
getIsNotEmpty +androidx.compose.foundation.layout.RowScope  getLET +androidx.compose.foundation.layout.RowScope  	getLISTOf +androidx.compose.foundation.layout.RowScope  getLet +androidx.compose.foundation.layout.RowScope  	getListOf +androidx.compose.foundation.layout.RowScope  
getPADDING +androidx.compose.foundation.layout.RowScope  
getPadding +androidx.compose.foundation.layout.RowScope  
isNotEmpty +androidx.compose.foundation.layout.RowScope  items +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  CastCard .androidx.compose.foundation.lazy.LazyItemScope  CastSection .androidx.compose.foundation.lazy.LazyItemScope  ContinueWatchingCard .androidx.compose.foundation.lazy.LazyItemScope  ContinueWatchingSection .androidx.compose.foundation.lazy.LazyItemScope  
FilterChip .androidx.compose.foundation.lazy.LazyItemScope  
FilterSection .androidx.compose.foundation.lazy.LazyItemScope  HeroSection .androidx.compose.foundation.lazy.LazyItemScope  Icon .androidx.compose.foundation.lazy.LazyItemScope  MediaDetailHero .androidx.compose.foundation.lazy.LazyItemScope  MediaDetailInfo .androidx.compose.foundation.lazy.LazyItemScope  MediaLibrariesSection .androidx.compose.foundation.lazy.LazyItemScope  MediaLibraryCard .androidx.compose.foundation.lazy.LazyItemScope  MediaPosterCard .androidx.compose.foundation.lazy.LazyItemScope  MediaPosterGrid .androidx.compose.foundation.lazy.LazyItemScope  MediaPosterRow .androidx.compose.foundation.lazy.LazyItemScope  Movie .androidx.compose.foundation.lazy.LazyItemScope  
PosterSize .androidx.compose.foundation.lazy.LazyItemScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  
SampleData .androidx.compose.foundation.lazy.LazyItemScope  Surface .androidx.compose.foundation.lazy.LazyItemScope  first .androidx.compose.foundation.lazy.LazyItemScope  getFIRST .androidx.compose.foundation.lazy.LazyItemScope  getFirst .androidx.compose.foundation.lazy.LazyItemScope  getSIZE .androidx.compose.foundation.lazy.LazyItemScope  getSize .androidx.compose.foundation.lazy.LazyItemScope  getWIDTH .androidx.compose.foundation.lazy.LazyItemScope  getWidth .androidx.compose.foundation.lazy.LazyItemScope  invoke .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  CastCard .androidx.compose.foundation.lazy.LazyListScope  CastSection .androidx.compose.foundation.lazy.LazyListScope  ContinueWatchingCard .androidx.compose.foundation.lazy.LazyListScope  ContinueWatchingSection .androidx.compose.foundation.lazy.LazyListScope  
FilterChip .androidx.compose.foundation.lazy.LazyListScope  
FilterSection .androidx.compose.foundation.lazy.LazyListScope  HeroSection .androidx.compose.foundation.lazy.LazyListScope  Icon .androidx.compose.foundation.lazy.LazyListScope  MediaDetailHero .androidx.compose.foundation.lazy.LazyListScope  MediaDetailInfo .androidx.compose.foundation.lazy.LazyListScope  MediaLibrariesSection .androidx.compose.foundation.lazy.LazyListScope  MediaLibraryCard .androidx.compose.foundation.lazy.LazyListScope  MediaPosterCard .androidx.compose.foundation.lazy.LazyListScope  MediaPosterGrid .androidx.compose.foundation.lazy.LazyListScope  MediaPosterRow .androidx.compose.foundation.lazy.LazyListScope  Movie .androidx.compose.foundation.lazy.LazyListScope  
PosterSize .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  
SampleData .androidx.compose.foundation.lazy.LazyListScope  Surface .androidx.compose.foundation.lazy.LazyListScope  first .androidx.compose.foundation.lazy.LazyListScope  getFIRST .androidx.compose.foundation.lazy.LazyListScope  getFirst .androidx.compose.foundation.lazy.LazyListScope  
getISNotEmpty .androidx.compose.foundation.lazy.LazyListScope  
getIsNotEmpty .androidx.compose.foundation.lazy.LazyListScope  getSIZE .androidx.compose.foundation.lazy.LazyListScope  getSize .androidx.compose.foundation.lazy.LazyListScope  getTAKE .androidx.compose.foundation.lazy.LazyListScope  getTake .androidx.compose.foundation.lazy.LazyListScope  getWIDTH .androidx.compose.foundation.lazy.LazyListScope  getWidth .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  take .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  MediaPosterCard 7androidx.compose.foundation.lazy.grid.LazyGridItemScope  MediaPosterCard 3androidx.compose.foundation.lazy.grid.LazyGridScope  invoke 3androidx.compose.foundation.lazy.grid.LazyGridScope  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  ArrowForward ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  	MusicNote ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  Photo ,androidx.compose.material.icons.Icons.Filled  	ArrowBack &androidx.compose.material.icons.filled  ArrowForward &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Brush &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  CastCard &androidx.compose.material.icons.filled  CastSection &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  ContinueWatchingCard &androidx.compose.material.icons.filled  ContinueWatchingSection &androidx.compose.material.icons.filled  
FilterChip &androidx.compose.material.icons.filled  
FilterSection &androidx.compose.material.icons.filled  	GridCells &androidx.compose.material.icons.filled  HeroSection &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  InfoRow &androidx.compose.material.icons.filled  LazyRow &androidx.compose.material.icons.filled  MediaDetailHero &androidx.compose.material.icons.filled  MediaDetailInfo &androidx.compose.material.icons.filled  MediaLibrariesSection &androidx.compose.material.icons.filled  MediaLibraryCard &androidx.compose.material.icons.filled  MediaPosterCard &androidx.compose.material.icons.filled  MediaPosterGrid &androidx.compose.material.icons.filled  MediaPosterRow &androidx.compose.material.icons.filled  	MediaType &androidx.compose.material.icons.filled  	MusicNote &androidx.compose.material.icons.filled  OutlinedButton &androidx.compose.material.icons.filled  
PaddingValues &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  Photo &androidx.compose.material.icons.filled  
PosterSize &androidx.compose.material.icons.filled  
SampleData &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Surface &androidx.compose.material.icons.filled  System &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  TextOverflow &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  com &androidx.compose.material.icons.filled  	emptyList &androidx.compose.material.icons.filled  
fillMaxHeight &androidx.compose.material.icons.filled  filter &androidx.compose.material.icons.filled  first &androidx.compose.material.icons.filled  format &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  invoke &androidx.compose.material.icons.filled  
isNotEmpty &androidx.compose.material.icons.filled  items &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  sortedBy &androidx.compose.material.icons.filled  sortedByDescending &androidx.compose.material.icons.filled  take &androidx.compose.material.icons.filled  	ArrowBack androidx.compose.material3  ArrowForward androidx.compose.material3  Brush androidx.compose.material3  CastCard androidx.compose.material3  CastSection androidx.compose.material3  Check androidx.compose.material3  Color androidx.compose.material3  ContinueWatchingCard androidx.compose.material3  ContinueWatchingSection androidx.compose.material3  Favorite androidx.compose.material3  FavoriteBorder androidx.compose.material3  
FilterChip androidx.compose.material3  
FilterSection androidx.compose.material3  HeroSection androidx.compose.material3  InfoRow androidx.compose.material3  LiveTv androidx.compose.material3  MediaDetailHero androidx.compose.material3  MediaDetailInfo androidx.compose.material3  MediaLibrariesSection androidx.compose.material3  MediaLibraryCard androidx.compose.material3  MediaPosterCard androidx.compose.material3  MediaPosterGrid androidx.compose.material3  MediaPosterRow androidx.compose.material3  	MediaType androidx.compose.material3  Movie androidx.compose.material3  OutlinedButton androidx.compose.material3  Person androidx.compose.material3  
PosterSize androidx.compose.material3  
SampleData androidx.compose.material3  Star androidx.compose.material3  String androidx.compose.material3  
TextButton androidx.compose.material3  TextOverflow androidx.compose.material3  Tv androidx.compose.material3  
background androidx.compose.material3  clip androidx.compose.material3  com androidx.compose.material3  filter androidx.compose.material3  first androidx.compose.material3  format androidx.compose.material3  invoke androidx.compose.material3  let androidx.compose.material3  sortedBy androidx.compose.material3  sortedByDescending androidx.compose.material3  take androidx.compose.material3  	secondary &androidx.compose.material3.ColorScheme  tertiary &androidx.compose.material3.ColorScheme  	ArrowBack androidx.compose.runtime  ArrowForward androidx.compose.runtime  Brush androidx.compose.runtime  CastCard androidx.compose.runtime  CastSection androidx.compose.runtime  Check androidx.compose.runtime  Color androidx.compose.runtime  ContinueWatchingCard androidx.compose.runtime  ContinueWatchingSection androidx.compose.runtime  Favorite androidx.compose.runtime  FavoriteBorder androidx.compose.runtime  
FilterChip androidx.compose.runtime  
FilterSection androidx.compose.runtime  HeroSection androidx.compose.runtime  InfoRow androidx.compose.runtime  LiveTv androidx.compose.runtime  MediaDetailHero androidx.compose.runtime  MediaDetailInfo androidx.compose.runtime  MediaLibrariesSection androidx.compose.runtime  MediaLibraryCard androidx.compose.runtime  MediaPosterCard androidx.compose.runtime  MediaPosterGrid androidx.compose.runtime  MediaPosterRow androidx.compose.runtime  	MediaType androidx.compose.runtime  Movie androidx.compose.runtime  OutlinedButton androidx.compose.runtime  Person androidx.compose.runtime  
PosterSize androidx.compose.runtime  
SampleData androidx.compose.runtime  Star androidx.compose.runtime  String androidx.compose.runtime  
TextButton androidx.compose.runtime  TextOverflow androidx.compose.runtime  Tv androidx.compose.runtime  
background androidx.compose.runtime  clip androidx.compose.runtime  com androidx.compose.runtime  filter androidx.compose.runtime  first androidx.compose.runtime  format androidx.compose.runtime  invoke androidx.compose.runtime  let androidx.compose.runtime  sortedBy androidx.compose.runtime  sortedByDescending androidx.compose.runtime  take androidx.compose.runtime  BottomCenter androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  TopStart androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  TopStart 'androidx.compose.ui.Alignment.Companion  align androidx.compose.ui.Modifier  getSIZE androidx.compose.ui.Modifier  getSize androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  getALIGN &androidx.compose.ui.Modifier.Companion  getAlign &androidx.compose.ui.Modifier.Companion  Brush androidx.compose.ui.graphics  horizontalGradient "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  horizontalGradient ,androidx.compose.ui.graphics.Brush.Companion  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Black "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  ContentScale androidx.compose.ui.layout  TextOverflow androidx.compose.ui.text.style  Ellipsis +androidx.compose.ui.text.style.TextOverflow  Ellipsis 5androidx.compose.ui.text.style.TextOverflow.Companion  plus androidx.compose.ui.unit.Dp  Boolean com.google.chuangke.data.model  Double com.google.chuangke.data.model  Float com.google.chuangke.data.model  Icons com.google.chuangke.data.model  Int com.google.chuangke.data.model  List com.google.chuangke.data.model  LiveTv com.google.chuangke.data.model  Long com.google.chuangke.data.model  Movie com.google.chuangke.data.model  	MusicNote com.google.chuangke.data.model  MutableStateFlow com.google.chuangke.data.model  Photo com.google.chuangke.data.model  
SampleData com.google.chuangke.data.model  String com.google.chuangke.data.model  System com.google.chuangke.data.model  Tv com.google.chuangke.data.model  Volatile com.google.chuangke.data.model  WatchHistoryRepository com.google.chuangke.data.model  
WatchStats com.google.chuangke.data.model  also com.google.chuangke.data.model  asStateFlow com.google.chuangke.data.model  count com.google.chuangke.data.model  	emptyList com.google.chuangke.data.model  emptyMap com.google.chuangke.data.model  find com.google.chuangke.data.model  groupBy com.google.chuangke.data.model  indexOfFirst com.google.chuangke.data.model  let com.google.chuangke.data.model  listOf com.google.chuangke.data.model  map com.google.chuangke.data.model  	mapValues com.google.chuangke.data.model  maxByOrNull com.google.chuangke.data.model  plus com.google.chuangke.data.model  random com.google.chuangke.data.model  	removeAll com.google.chuangke.data.model  set com.google.chuangke.data.model  sortByDescending com.google.chuangke.data.model  sumOf com.google.chuangke.data.model  synchronized com.google.chuangke.data.model  take com.google.chuangke.data.model  
toMutableList com.google.chuangke.data.model  toMutableMap com.google.chuangke.data.model  Episode 3com.google.chuangke.data.model.ContinueWatchingItem  Float 3com.google.chuangke.data.model.ContinueWatchingItem  Long 3com.google.chuangke.data.model.ContinueWatchingItem  	MediaItem 3com.google.chuangke.data.model.ContinueWatchingItem  copy 3com.google.chuangke.data.model.ContinueWatchingItem  lastWatchedDate 3com.google.chuangke.data.model.ContinueWatchingItem  	mediaItem 3com.google.chuangke.data.model.ContinueWatchingItem  progress 3com.google.chuangke.data.model.ContinueWatchingItem  Boolean &com.google.chuangke.data.model.Episode  Float &com.google.chuangke.data.model.Episode  Int &com.google.chuangke.data.model.Episode  Long &com.google.chuangke.data.model.Episode  String &com.google.chuangke.data.model.Episode  Icons *com.google.chuangke.data.model.LibraryType  ImageVector *com.google.chuangke.data.model.LibraryType  LibraryType *com.google.chuangke.data.model.LibraryType  LiveTv *com.google.chuangke.data.model.LibraryType  MOVIES *com.google.chuangke.data.model.LibraryType  Movie *com.google.chuangke.data.model.LibraryType  	MusicNote *com.google.chuangke.data.model.LibraryType  Photo *com.google.chuangke.data.model.LibraryType  String *com.google.chuangke.data.model.LibraryType  TV_SHOWS *com.google.chuangke.data.model.LibraryType  Tv *com.google.chuangke.data.model.LibraryType  icon *com.google.chuangke.data.model.LibraryType  Icons 2com.google.chuangke.data.model.LibraryType.LIVE_TV  LiveTv 2com.google.chuangke.data.model.LibraryType.LIVE_TV  Icons 1com.google.chuangke.data.model.LibraryType.MOVIES  Movie 1com.google.chuangke.data.model.LibraryType.MOVIES  Icons 0com.google.chuangke.data.model.LibraryType.MUSIC  	MusicNote 0com.google.chuangke.data.model.LibraryType.MUSIC  Icons 1com.google.chuangke.data.model.LibraryType.PHOTOS  Photo 1com.google.chuangke.data.model.LibraryType.PHOTOS  Icons 3com.google.chuangke.data.model.LibraryType.TV_SHOWS  Tv 3com.google.chuangke.data.model.LibraryType.TV_SHOWS  Boolean (com.google.chuangke.data.model.MediaItem  Double (com.google.chuangke.data.model.MediaItem  Float (com.google.chuangke.data.model.MediaItem  Int (com.google.chuangke.data.model.MediaItem  List (com.google.chuangke.data.model.MediaItem  Long (com.google.chuangke.data.model.MediaItem  	MediaType (com.google.chuangke.data.model.MediaItem  Person (com.google.chuangke.data.model.MediaItem  String (com.google.chuangke.data.model.MediaItem  System (com.google.chuangke.data.model.MediaItem  	addedDate (com.google.chuangke.data.model.MediaItem  cast (com.google.chuangke.data.model.MediaItem  director (com.google.chuangke.data.model.MediaItem  duration (com.google.chuangke.data.model.MediaItem  	emptyList (com.google.chuangke.data.model.MediaItem  genres (com.google.chuangke.data.model.MediaItem  getLET (com.google.chuangke.data.model.MediaItem  getLet (com.google.chuangke.data.model.MediaItem  id (com.google.chuangke.data.model.MediaItem  	isWatched (com.google.chuangke.data.model.MediaItem  lastWatchedDate (com.google.chuangke.data.model.MediaItem  let (com.google.chuangke.data.model.MediaItem  overview (com.google.chuangke.data.model.MediaItem  rating (com.google.chuangke.data.model.MediaItem  releaseDate (com.google.chuangke.data.model.MediaItem  studio (com.google.chuangke.data.model.MediaItem  title (com.google.chuangke.data.model.MediaItem  type (com.google.chuangke.data.model.MediaItem  
watchProgress (com.google.chuangke.data.model.MediaItem  year (com.google.chuangke.data.model.MediaItem  Int +com.google.chuangke.data.model.MediaLibrary  LibraryType +com.google.chuangke.data.model.MediaLibrary  List +com.google.chuangke.data.model.MediaLibrary  Long +com.google.chuangke.data.model.MediaLibrary  	MediaItem +com.google.chuangke.data.model.MediaLibrary  String +com.google.chuangke.data.model.MediaLibrary  	emptyList +com.google.chuangke.data.model.MediaLibrary  	itemCount +com.google.chuangke.data.model.MediaLibrary  name +com.google.chuangke.data.model.MediaLibrary  type +com.google.chuangke.data.model.MediaLibrary  LIVE_TV (com.google.chuangke.data.model.MediaType  MOVIE (com.google.chuangke.data.model.MediaType  TV_SHOW (com.google.chuangke.data.model.MediaType  String %com.google.chuangke.data.model.Person  	character %com.google.chuangke.data.model.Person  name %com.google.chuangke.data.model.Person  List -com.google.chuangke.data.model.Recommendation  	MediaItem -com.google.chuangke.data.model.Recommendation  String -com.google.chuangke.data.model.Recommendation  items -com.google.chuangke.data.model.Recommendation  title -com.google.chuangke.data.model.Recommendation  Int +com.google.chuangke.data.model.SearchResult  List +com.google.chuangke.data.model.SearchResult  	MediaItem +com.google.chuangke.data.model.SearchResult  Person +com.google.chuangke.data.model.SearchResult  String +com.google.chuangke.data.model.SearchResult  TVShow +com.google.chuangke.data.model.SearchResult  	emptyList +com.google.chuangke.data.model.SearchResult  Episode %com.google.chuangke.data.model.Season  Int %com.google.chuangke.data.model.Season  List %com.google.chuangke.data.model.Season  String %com.google.chuangke.data.model.Season  	emptyList %com.google.chuangke.data.model.Season  Int %com.google.chuangke.data.model.TVShow  List %com.google.chuangke.data.model.TVShow  	MediaItem %com.google.chuangke.data.model.TVShow  Season %com.google.chuangke.data.model.TVShow  String %com.google.chuangke.data.model.TVShow  	emptyList %com.google.chuangke.data.model.TVShow  	mediaItem %com.google.chuangke.data.model.TVShow  Boolean +com.google.chuangke.data.model.WatchHistory  Float +com.google.chuangke.data.model.WatchHistory  Int +com.google.chuangke.data.model.WatchHistory  Long +com.google.chuangke.data.model.WatchHistory  	MediaType +com.google.chuangke.data.model.WatchHistory  String +com.google.chuangke.data.model.WatchHistory  	completed +com.google.chuangke.data.model.WatchHistory  duration +com.google.chuangke.data.model.WatchHistory  mediaId +com.google.chuangke.data.model.WatchHistory  	mediaType +com.google.chuangke.data.model.WatchHistory  ContinueWatchingItem #com.google.chuangke.data.repository  Float #com.google.chuangke.data.repository  Int #com.google.chuangke.data.repository  LibraryType #com.google.chuangke.data.repository  List #com.google.chuangke.data.repository  Map #com.google.chuangke.data.repository  	MediaItem #com.google.chuangke.data.repository  MediaLibrary #com.google.chuangke.data.repository  	MediaType #com.google.chuangke.data.repository  MutableStateFlow #com.google.chuangke.data.repository  Recommendation #com.google.chuangke.data.repository  String #com.google.chuangke.data.repository  System #com.google.chuangke.data.repository  TVShow #com.google.chuangke.data.repository  Volatile #com.google.chuangke.data.repository  WatchHistory #com.google.chuangke.data.repository  also #com.google.chuangke.data.repository  asStateFlow #com.google.chuangke.data.repository  count #com.google.chuangke.data.repository  	emptyList #com.google.chuangke.data.repository  emptyMap #com.google.chuangke.data.repository  find #com.google.chuangke.data.repository  groupBy #com.google.chuangke.data.repository  indexOfFirst #com.google.chuangke.data.repository  invoke #com.google.chuangke.data.repository  let #com.google.chuangke.data.repository  listOf #com.google.chuangke.data.repository  map #com.google.chuangke.data.repository  	mapValues #com.google.chuangke.data.repository  maxByOrNull #com.google.chuangke.data.repository  plus #com.google.chuangke.data.repository  random #com.google.chuangke.data.repository  	removeAll #com.google.chuangke.data.repository  set #com.google.chuangke.data.repository  sortByDescending #com.google.chuangke.data.repository  sumOf #com.google.chuangke.data.repository  synchronized #com.google.chuangke.data.repository  take #com.google.chuangke.data.repository  
toMutableList #com.google.chuangke.data.repository  toMutableMap #com.google.chuangke.data.repository  ContinueWatchingItem .com.google.chuangke.data.repository.SampleData  LibraryType .com.google.chuangke.data.repository.SampleData  	MediaItem .com.google.chuangke.data.repository.SampleData  MediaLibrary .com.google.chuangke.data.repository.SampleData  	MediaType .com.google.chuangke.data.repository.SampleData  Recommendation .com.google.chuangke.data.repository.SampleData  System .com.google.chuangke.data.repository.SampleData  TVShow .com.google.chuangke.data.repository.SampleData  continueWatchingItems .com.google.chuangke.data.repository.SampleData  	getLISTOf .com.google.chuangke.data.repository.SampleData  	getListOf .com.google.chuangke.data.repository.SampleData  getMAP .com.google.chuangke.data.repository.SampleData  getMap .com.google.chuangke.data.repository.SampleData  getPLUS .com.google.chuangke.data.repository.SampleData  getPlus .com.google.chuangke.data.repository.SampleData  getTAKE .com.google.chuangke.data.repository.SampleData  getTake .com.google.chuangke.data.repository.SampleData  listOf .com.google.chuangke.data.repository.SampleData  map .com.google.chuangke.data.repository.SampleData  mediaLibraries .com.google.chuangke.data.repository.SampleData  plus .com.google.chuangke.data.repository.SampleData  
recentlyAdded .com.google.chuangke.data.repository.SampleData  recommendations .com.google.chuangke.data.repository.SampleData  sampleMovies .com.google.chuangke.data.repository.SampleData  
sampleTVShows .com.google.chuangke.data.repository.SampleData  take .com.google.chuangke.data.repository.SampleData  ContinueWatchingItem :com.google.chuangke.data.repository.WatchHistoryRepository  Float :com.google.chuangke.data.repository.WatchHistoryRepository  Int :com.google.chuangke.data.repository.WatchHistoryRepository  List :com.google.chuangke.data.repository.WatchHistoryRepository  Map :com.google.chuangke.data.repository.WatchHistoryRepository  	MediaType :com.google.chuangke.data.repository.WatchHistoryRepository  MutableStateFlow :com.google.chuangke.data.repository.WatchHistoryRepository  
SampleData :com.google.chuangke.data.repository.WatchHistoryRepository  	StateFlow :com.google.chuangke.data.repository.WatchHistoryRepository  String :com.google.chuangke.data.repository.WatchHistoryRepository  System :com.google.chuangke.data.repository.WatchHistoryRepository  Volatile :com.google.chuangke.data.repository.WatchHistoryRepository  WatchHistory :com.google.chuangke.data.repository.WatchHistoryRepository  WatchHistoryRepository :com.google.chuangke.data.repository.WatchHistoryRepository  
WatchStats :com.google.chuangke.data.repository.WatchHistoryRepository  _continueWatching :com.google.chuangke.data.repository.WatchHistoryRepository  _mediaProgress :com.google.chuangke.data.repository.WatchHistoryRepository  
_watchHistory :com.google.chuangke.data.repository.WatchHistoryRepository  also :com.google.chuangke.data.repository.WatchHistoryRepository  asStateFlow :com.google.chuangke.data.repository.WatchHistoryRepository  count :com.google.chuangke.data.repository.WatchHistoryRepository  	emptyList :com.google.chuangke.data.repository.WatchHistoryRepository  emptyMap :com.google.chuangke.data.repository.WatchHistoryRepository  find :com.google.chuangke.data.repository.WatchHistoryRepository  generateHistoryId :com.google.chuangke.data.repository.WatchHistoryRepository  getALSO :com.google.chuangke.data.repository.WatchHistoryRepository  getASStateFlow :com.google.chuangke.data.repository.WatchHistoryRepository  getAlso :com.google.chuangke.data.repository.WatchHistoryRepository  getAsStateFlow :com.google.chuangke.data.repository.WatchHistoryRepository  getCOUNT :com.google.chuangke.data.repository.WatchHistoryRepository  getCount :com.google.chuangke.data.repository.WatchHistoryRepository  getEMPTYList :com.google.chuangke.data.repository.WatchHistoryRepository  getEMPTYMap :com.google.chuangke.data.repository.WatchHistoryRepository  getEmptyList :com.google.chuangke.data.repository.WatchHistoryRepository  getEmptyMap :com.google.chuangke.data.repository.WatchHistoryRepository  getFIND :com.google.chuangke.data.repository.WatchHistoryRepository  getFind :com.google.chuangke.data.repository.WatchHistoryRepository  
getGROUPBy :com.google.chuangke.data.repository.WatchHistoryRepository  
getGroupBy :com.google.chuangke.data.repository.WatchHistoryRepository  getINDEXOfFirst :com.google.chuangke.data.repository.WatchHistoryRepository  getIndexOfFirst :com.google.chuangke.data.repository.WatchHistoryRepository  getLET :com.google.chuangke.data.repository.WatchHistoryRepository  getLet :com.google.chuangke.data.repository.WatchHistoryRepository  getMAPValues :com.google.chuangke.data.repository.WatchHistoryRepository  getMAXByOrNull :com.google.chuangke.data.repository.WatchHistoryRepository  getMapValues :com.google.chuangke.data.repository.WatchHistoryRepository  getMaxByOrNull :com.google.chuangke.data.repository.WatchHistoryRepository  	getRANDOM :com.google.chuangke.data.repository.WatchHistoryRepository  getREMOVEAll :com.google.chuangke.data.repository.WatchHistoryRepository  	getRandom :com.google.chuangke.data.repository.WatchHistoryRepository  getRemoveAll :com.google.chuangke.data.repository.WatchHistoryRepository  getSET :com.google.chuangke.data.repository.WatchHistoryRepository  getSORTByDescending :com.google.chuangke.data.repository.WatchHistoryRepository  getSUMOf :com.google.chuangke.data.repository.WatchHistoryRepository  getSet :com.google.chuangke.data.repository.WatchHistoryRepository  getSortByDescending :com.google.chuangke.data.repository.WatchHistoryRepository  getSumOf :com.google.chuangke.data.repository.WatchHistoryRepository  getTAKE :com.google.chuangke.data.repository.WatchHistoryRepository  getTOMutableList :com.google.chuangke.data.repository.WatchHistoryRepository  getTOMutableMap :com.google.chuangke.data.repository.WatchHistoryRepository  getTake :com.google.chuangke.data.repository.WatchHistoryRepository  getToMutableList :com.google.chuangke.data.repository.WatchHistoryRepository  getToMutableMap :com.google.chuangke.data.repository.WatchHistoryRepository  groupBy :com.google.chuangke.data.repository.WatchHistoryRepository  indexOfFirst :com.google.chuangke.data.repository.WatchHistoryRepository  invoke :com.google.chuangke.data.repository.WatchHistoryRepository  let :com.google.chuangke.data.repository.WatchHistoryRepository  	mapValues :com.google.chuangke.data.repository.WatchHistoryRepository  maxByOrNull :com.google.chuangke.data.repository.WatchHistoryRepository  random :com.google.chuangke.data.repository.WatchHistoryRepository  	removeAll :com.google.chuangke.data.repository.WatchHistoryRepository  removeContinueWatching :com.google.chuangke.data.repository.WatchHistoryRepository  set :com.google.chuangke.data.repository.WatchHistoryRepository  sortByDescending :com.google.chuangke.data.repository.WatchHistoryRepository  sumOf :com.google.chuangke.data.repository.WatchHistoryRepository  synchronized :com.google.chuangke.data.repository.WatchHistoryRepository  take :com.google.chuangke.data.repository.WatchHistoryRepository  
toMutableList :com.google.chuangke.data.repository.WatchHistoryRepository  toMutableMap :com.google.chuangke.data.repository.WatchHistoryRepository  updateContinueWatching :com.google.chuangke.data.repository.WatchHistoryRepository  updateMediaProgress :com.google.chuangke.data.repository.WatchHistoryRepository  ContinueWatchingItem Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  Float Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  INSTANCE Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  Int Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  List Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  Map Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  	MediaType Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  MutableStateFlow Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  
SampleData Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  	StateFlow Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  String Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  System Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  Volatile Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  WatchHistory Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  WatchHistoryRepository Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  
WatchStats Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  also Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  asStateFlow Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  count Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  	emptyList Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  emptyMap Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  find Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getALSO Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getASStateFlow Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getAlso Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getAsStateFlow Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getCOUNT Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getCount Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getEMPTYList Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getEMPTYMap Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getEmptyList Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getEmptyMap Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getFIND Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getFind Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  
getGROUPBy Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  
getGroupBy Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getINDEXOfFirst Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getIndexOfFirst Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getLET Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getLet Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getMAPValues Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getMAXByOrNull Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getMapValues Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getMaxByOrNull Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  	getRANDOM Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getREMOVEAll Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  	getRandom Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getRemoveAll Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getSET Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getSORTByDescending Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getSUMOf Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getSYNCHRONIZED Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getSet Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getSortByDescending Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getSumOf Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getSynchronized Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getTAKE Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getTOMutableList Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getTOMutableMap Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getTake Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getToMutableList Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  getToMutableMap Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  groupBy Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  indexOfFirst Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  invoke Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  let Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  	mapValues Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  maxByOrNull Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  random Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  	removeAll Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  set Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  sortByDescending Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  sumOf Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  synchronized Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  take Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  
toMutableList Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  toMutableMap Dcom.google.chuangke.data.repository.WatchHistoryRepository.Companion  Int .com.google.chuangke.data.repository.WatchStats  	MediaType .com.google.chuangke.data.repository.WatchStats  ArrowForward !com.google.chuangke.ui.components  Brush !com.google.chuangke.ui.components  Check !com.google.chuangke.ui.components  Color !com.google.chuangke.ui.components  	GridCells !com.google.chuangke.ui.components  LazyRow !com.google.chuangke.ui.components  List !com.google.chuangke.ui.components  LiveTv !com.google.chuangke.ui.components  	MediaType !com.google.chuangke.ui.components  Movie !com.google.chuangke.ui.components  
PaddingValues !com.google.chuangke.ui.components  	PlayArrow !com.google.chuangke.ui.components  RoundedCornerShape !com.google.chuangke.ui.components  Star !com.google.chuangke.ui.components  String !com.google.chuangke.ui.components  
TextButton !com.google.chuangke.ui.components  TextOverflow !com.google.chuangke.ui.components  Tv !com.google.chuangke.ui.components  
background !com.google.chuangke.ui.components  clip !com.google.chuangke.ui.components  format !com.google.chuangke.ui.components  let !com.google.chuangke.ui.components  listOf !com.google.chuangke.ui.components  MEDIUM ,com.google.chuangke.ui.components.PosterSize  
PosterSize ,com.google.chuangke.ui.components.PosterSize  androidx ,com.google.chuangke.ui.components.PosterSize  dp ,com.google.chuangke.ui.components.PosterSize  height ,com.google.chuangke.ui.components.PosterSize  width ,com.google.chuangke.ui.components.PosterSize  dp 8com.google.chuangke.ui.components.PosterSize.EXTRA_LARGE  dp 2com.google.chuangke.ui.components.PosterSize.LARGE  dp 3com.google.chuangke.ui.components.PosterSize.MEDIUM  dp 2com.google.chuangke.ui.components.PosterSize.SMALL  	ArrowBack com.google.chuangke.ui.screens  Brush com.google.chuangke.ui.screens  CastCard com.google.chuangke.ui.screens  CastSection com.google.chuangke.ui.screens  Color com.google.chuangke.ui.screens  ContinueWatchingCard com.google.chuangke.ui.screens  ContinueWatchingSection com.google.chuangke.ui.screens  Favorite com.google.chuangke.ui.screens  FavoriteBorder com.google.chuangke.ui.screens  
FilterChip com.google.chuangke.ui.screens  
FilterSection com.google.chuangke.ui.screens  HeroSection com.google.chuangke.ui.screens  InfoRow com.google.chuangke.ui.screens  MediaDetailHero com.google.chuangke.ui.screens  MediaDetailInfo com.google.chuangke.ui.screens  MediaLibrariesSection com.google.chuangke.ui.screens  MediaLibraryCard com.google.chuangke.ui.screens  MediaPosterGrid com.google.chuangke.ui.screens  MediaPosterRow com.google.chuangke.ui.screens  	MediaType com.google.chuangke.ui.screens  OutlinedButton com.google.chuangke.ui.screens  Person com.google.chuangke.ui.screens  
PosterSize com.google.chuangke.ui.screens  
SampleData com.google.chuangke.ui.screens  Star com.google.chuangke.ui.screens  Tv com.google.chuangke.ui.screens  
background com.google.chuangke.ui.screens  clip com.google.chuangke.ui.screens  com com.google.chuangke.ui.screens  
fillMaxHeight com.google.chuangke.ui.screens  filter com.google.chuangke.ui.screens  first com.google.chuangke.ui.screens  format com.google.chuangke.ui.screens  invoke com.google.chuangke.ui.screens  let com.google.chuangke.ui.screens  sortedBy com.google.chuangke.ui.screens  sortedByDescending com.google.chuangke.ui.screens  take com.google.chuangke.ui.screens  Brush 	java.lang  CastCard 	java.lang  CastSection 	java.lang  Color 	java.lang  ContinueWatchingCard 	java.lang  ContinueWatchingItem 	java.lang  ContinueWatchingSection 	java.lang  
FilterChip 	java.lang  
FilterSection 	java.lang  HeroSection 	java.lang  InfoRow 	java.lang  LibraryType 	java.lang  MediaDetailHero 	java.lang  MediaDetailInfo 	java.lang  	MediaItem 	java.lang  MediaLibrariesSection 	java.lang  MediaLibrary 	java.lang  MediaLibraryCard 	java.lang  MediaPosterCard 	java.lang  MediaPosterGrid 	java.lang  MediaPosterRow 	java.lang  	MediaType 	java.lang  MutableStateFlow 	java.lang  OutlinedButton 	java.lang  
PosterSize 	java.lang  Recommendation 	java.lang  
SampleData 	java.lang  String 	java.lang  System 	java.lang  TVShow 	java.lang  
TextButton 	java.lang  TextOverflow 	java.lang  WatchHistory 	java.lang  WatchHistoryRepository 	java.lang  
WatchStats 	java.lang  also 	java.lang  asStateFlow 	java.lang  
background 	java.lang  clip 	java.lang  com 	java.lang  count 	java.lang  emptyMap 	java.lang  
fillMaxHeight 	java.lang  filter 	java.lang  first 	java.lang  format 	java.lang  groupBy 	java.lang  indexOfFirst 	java.lang  invoke 	java.lang  let 	java.lang  map 	java.lang  	mapValues 	java.lang  maxByOrNull 	java.lang  plus 	java.lang  random 	java.lang  	removeAll 	java.lang  set 	java.lang  sortByDescending 	java.lang  sortedBy 	java.lang  sortedByDescending 	java.lang  sumOf 	java.lang  synchronized 	java.lang  take 	java.lang  
toMutableList 	java.lang  toMutableMap 	java.lang  currentTimeMillis java.lang.System  Brush kotlin  CastCard kotlin  CastSection kotlin  Color kotlin  ContinueWatchingCard kotlin  ContinueWatchingItem kotlin  ContinueWatchingSection kotlin  
FilterChip kotlin  
FilterSection kotlin  HeroSection kotlin  InfoRow kotlin  LibraryType kotlin  Long kotlin  MediaDetailHero kotlin  MediaDetailInfo kotlin  	MediaItem kotlin  MediaLibrariesSection kotlin  MediaLibrary kotlin  MediaLibraryCard kotlin  MediaPosterCard kotlin  MediaPosterGrid kotlin  MediaPosterRow kotlin  	MediaType kotlin  MutableStateFlow kotlin  OutlinedButton kotlin  
PosterSize kotlin  Recommendation kotlin  
SampleData kotlin  System kotlin  TVShow kotlin  
TextButton kotlin  TextOverflow kotlin  Volatile kotlin  WatchHistory kotlin  WatchHistoryRepository kotlin  
WatchStats kotlin  also kotlin  asStateFlow kotlin  
background kotlin  clip kotlin  com kotlin  count kotlin  emptyMap kotlin  
fillMaxHeight kotlin  filter kotlin  first kotlin  format kotlin  groupBy kotlin  indexOfFirst kotlin  invoke kotlin  let kotlin  map kotlin  	mapValues kotlin  maxByOrNull kotlin  plus kotlin  random kotlin  	removeAll kotlin  set kotlin  sortByDescending kotlin  sortedBy kotlin  sortedByDescending kotlin  sumOf kotlin  synchronized kotlin  take kotlin  
toMutableList kotlin  toMutableMap kotlin  getLET 
kotlin.Double  getLet 
kotlin.Double  getLET kotlin.Function0  getLet kotlin.Function0  getLET 
kotlin.Int  getLet 
kotlin.Int  getLET 
kotlin.String  getLet 
kotlin.String  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  Brush kotlin.annotation  CastCard kotlin.annotation  CastSection kotlin.annotation  Color kotlin.annotation  ContinueWatchingCard kotlin.annotation  ContinueWatchingItem kotlin.annotation  ContinueWatchingSection kotlin.annotation  
FilterChip kotlin.annotation  
FilterSection kotlin.annotation  HeroSection kotlin.annotation  InfoRow kotlin.annotation  LibraryType kotlin.annotation  MediaDetailHero kotlin.annotation  MediaDetailInfo kotlin.annotation  	MediaItem kotlin.annotation  MediaLibrariesSection kotlin.annotation  MediaLibrary kotlin.annotation  MediaLibraryCard kotlin.annotation  MediaPosterCard kotlin.annotation  MediaPosterGrid kotlin.annotation  MediaPosterRow kotlin.annotation  	MediaType kotlin.annotation  MutableStateFlow kotlin.annotation  OutlinedButton kotlin.annotation  
PosterSize kotlin.annotation  Recommendation kotlin.annotation  
SampleData kotlin.annotation  String kotlin.annotation  System kotlin.annotation  TVShow kotlin.annotation  
TextButton kotlin.annotation  TextOverflow kotlin.annotation  Volatile kotlin.annotation  WatchHistory kotlin.annotation  WatchHistoryRepository kotlin.annotation  
WatchStats kotlin.annotation  also kotlin.annotation  asStateFlow kotlin.annotation  
background kotlin.annotation  clip kotlin.annotation  com kotlin.annotation  count kotlin.annotation  emptyMap kotlin.annotation  
fillMaxHeight kotlin.annotation  filter kotlin.annotation  first kotlin.annotation  format kotlin.annotation  groupBy kotlin.annotation  indexOfFirst kotlin.annotation  invoke kotlin.annotation  let kotlin.annotation  map kotlin.annotation  	mapValues kotlin.annotation  maxByOrNull kotlin.annotation  plus kotlin.annotation  random kotlin.annotation  	removeAll kotlin.annotation  set kotlin.annotation  sortByDescending kotlin.annotation  sortedBy kotlin.annotation  sortedByDescending kotlin.annotation  sumOf kotlin.annotation  synchronized kotlin.annotation  take kotlin.annotation  
toMutableList kotlin.annotation  toMutableMap kotlin.annotation  Brush kotlin.collections  CastCard kotlin.collections  CastSection kotlin.collections  Color kotlin.collections  ContinueWatchingCard kotlin.collections  ContinueWatchingItem kotlin.collections  ContinueWatchingSection kotlin.collections  
FilterChip kotlin.collections  
FilterSection kotlin.collections  HeroSection kotlin.collections  InfoRow kotlin.collections  LibraryType kotlin.collections  Map kotlin.collections  MediaDetailHero kotlin.collections  MediaDetailInfo kotlin.collections  	MediaItem kotlin.collections  MediaLibrariesSection kotlin.collections  MediaLibrary kotlin.collections  MediaLibraryCard kotlin.collections  MediaPosterCard kotlin.collections  MediaPosterGrid kotlin.collections  MediaPosterRow kotlin.collections  	MediaType kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  MutableStateFlow kotlin.collections  OutlinedButton kotlin.collections  
PosterSize kotlin.collections  Recommendation kotlin.collections  
SampleData kotlin.collections  String kotlin.collections  System kotlin.collections  TVShow kotlin.collections  
TextButton kotlin.collections  TextOverflow kotlin.collections  Volatile kotlin.collections  WatchHistory kotlin.collections  WatchHistoryRepository kotlin.collections  
WatchStats kotlin.collections  also kotlin.collections  asStateFlow kotlin.collections  
background kotlin.collections  clip kotlin.collections  com kotlin.collections  count kotlin.collections  emptyMap kotlin.collections  
fillMaxHeight kotlin.collections  filter kotlin.collections  first kotlin.collections  format kotlin.collections  groupBy kotlin.collections  indexOfFirst kotlin.collections  invoke kotlin.collections  let kotlin.collections  map kotlin.collections  	mapValues kotlin.collections  maxByOrNull kotlin.collections  plus kotlin.collections  random kotlin.collections  	removeAll kotlin.collections  set kotlin.collections  sortByDescending kotlin.collections  sortedBy kotlin.collections  sortedByDescending kotlin.collections  sumOf kotlin.collections  sumOfInt kotlin.collections  synchronized kotlin.collections  take kotlin.collections  
toMutableList kotlin.collections  toMutableMap kotlin.collections  getCOUNT kotlin.collections.List  getCount kotlin.collections.List  	getFILTER kotlin.collections.List  getFIRST kotlin.collections.List  	getFilter kotlin.collections.List  getFirst kotlin.collections.List  
getGROUPBy kotlin.collections.List  
getGroupBy kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  getPLUS kotlin.collections.List  getPlus kotlin.collections.List  getSORTEDBy kotlin.collections.List  getSORTEDByDescending kotlin.collections.List  getSUMOf kotlin.collections.List  getSortedBy kotlin.collections.List  getSortedByDescending kotlin.collections.List  getSumOf kotlin.collections.List  getTAKE kotlin.collections.List  getTOMutableList kotlin.collections.List  getTake kotlin.collections.List  getToMutableList kotlin.collections.List  setFirst kotlin.collections.List  Entry kotlin.collections.Map  getMAPValues kotlin.collections.Map  getMAXByOrNull kotlin.collections.Map  getMapValues kotlin.collections.Map  getMaxByOrNull kotlin.collections.Map  getTOMutableMap kotlin.collections.Map  getToMutableMap kotlin.collections.Map  getINDEXOfFirst kotlin.collections.MutableList  getIndexOfFirst kotlin.collections.MutableList  getREMOVEAll kotlin.collections.MutableList  getRemoveAll kotlin.collections.MutableList  getSORTByDescending kotlin.collections.MutableList  getSortByDescending kotlin.collections.MutableList  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  Brush kotlin.comparisons  CastCard kotlin.comparisons  CastSection kotlin.comparisons  Color kotlin.comparisons  ContinueWatchingCard kotlin.comparisons  ContinueWatchingItem kotlin.comparisons  ContinueWatchingSection kotlin.comparisons  
FilterChip kotlin.comparisons  
FilterSection kotlin.comparisons  HeroSection kotlin.comparisons  InfoRow kotlin.comparisons  LibraryType kotlin.comparisons  MediaDetailHero kotlin.comparisons  MediaDetailInfo kotlin.comparisons  	MediaItem kotlin.comparisons  MediaLibrariesSection kotlin.comparisons  MediaLibrary kotlin.comparisons  MediaLibraryCard kotlin.comparisons  MediaPosterCard kotlin.comparisons  MediaPosterGrid kotlin.comparisons  MediaPosterRow kotlin.comparisons  	MediaType kotlin.comparisons  MutableStateFlow kotlin.comparisons  OutlinedButton kotlin.comparisons  
PosterSize kotlin.comparisons  Recommendation kotlin.comparisons  
SampleData kotlin.comparisons  String kotlin.comparisons  System kotlin.comparisons  TVShow kotlin.comparisons  
TextButton kotlin.comparisons  TextOverflow kotlin.comparisons  Volatile kotlin.comparisons  WatchHistory kotlin.comparisons  WatchHistoryRepository kotlin.comparisons  
WatchStats kotlin.comparisons  also kotlin.comparisons  asStateFlow kotlin.comparisons  
background kotlin.comparisons  clip kotlin.comparisons  com kotlin.comparisons  count kotlin.comparisons  emptyMap kotlin.comparisons  
fillMaxHeight kotlin.comparisons  filter kotlin.comparisons  first kotlin.comparisons  format kotlin.comparisons  groupBy kotlin.comparisons  indexOfFirst kotlin.comparisons  invoke kotlin.comparisons  let kotlin.comparisons  map kotlin.comparisons  	mapValues kotlin.comparisons  maxByOrNull kotlin.comparisons  plus kotlin.comparisons  random kotlin.comparisons  	removeAll kotlin.comparisons  set kotlin.comparisons  sortByDescending kotlin.comparisons  sortedBy kotlin.comparisons  sortedByDescending kotlin.comparisons  sumOf kotlin.comparisons  synchronized kotlin.comparisons  take kotlin.comparisons  
toMutableList kotlin.comparisons  toMutableMap kotlin.comparisons  Brush 	kotlin.io  CastCard 	kotlin.io  CastSection 	kotlin.io  Color 	kotlin.io  ContinueWatchingCard 	kotlin.io  ContinueWatchingItem 	kotlin.io  ContinueWatchingSection 	kotlin.io  
FilterChip 	kotlin.io  
FilterSection 	kotlin.io  HeroSection 	kotlin.io  InfoRow 	kotlin.io  LibraryType 	kotlin.io  MediaDetailHero 	kotlin.io  MediaDetailInfo 	kotlin.io  	MediaItem 	kotlin.io  MediaLibrariesSection 	kotlin.io  MediaLibrary 	kotlin.io  MediaLibraryCard 	kotlin.io  MediaPosterCard 	kotlin.io  MediaPosterGrid 	kotlin.io  MediaPosterRow 	kotlin.io  	MediaType 	kotlin.io  MutableStateFlow 	kotlin.io  OutlinedButton 	kotlin.io  
PosterSize 	kotlin.io  Recommendation 	kotlin.io  
SampleData 	kotlin.io  String 	kotlin.io  System 	kotlin.io  TVShow 	kotlin.io  
TextButton 	kotlin.io  TextOverflow 	kotlin.io  Volatile 	kotlin.io  WatchHistory 	kotlin.io  WatchHistoryRepository 	kotlin.io  
WatchStats 	kotlin.io  also 	kotlin.io  asStateFlow 	kotlin.io  
background 	kotlin.io  clip 	kotlin.io  com 	kotlin.io  count 	kotlin.io  emptyMap 	kotlin.io  
fillMaxHeight 	kotlin.io  filter 	kotlin.io  first 	kotlin.io  format 	kotlin.io  groupBy 	kotlin.io  indexOfFirst 	kotlin.io  invoke 	kotlin.io  let 	kotlin.io  map 	kotlin.io  	mapValues 	kotlin.io  maxByOrNull 	kotlin.io  plus 	kotlin.io  random 	kotlin.io  	removeAll 	kotlin.io  set 	kotlin.io  sortByDescending 	kotlin.io  sortedBy 	kotlin.io  sortedByDescending 	kotlin.io  sumOf 	kotlin.io  synchronized 	kotlin.io  take 	kotlin.io  
toMutableList 	kotlin.io  toMutableMap 	kotlin.io  Brush 
kotlin.jvm  CastCard 
kotlin.jvm  CastSection 
kotlin.jvm  Color 
kotlin.jvm  ContinueWatchingCard 
kotlin.jvm  ContinueWatchingItem 
kotlin.jvm  ContinueWatchingSection 
kotlin.jvm  
FilterChip 
kotlin.jvm  
FilterSection 
kotlin.jvm  HeroSection 
kotlin.jvm  InfoRow 
kotlin.jvm  LibraryType 
kotlin.jvm  MediaDetailHero 
kotlin.jvm  MediaDetailInfo 
kotlin.jvm  	MediaItem 
kotlin.jvm  MediaLibrariesSection 
kotlin.jvm  MediaLibrary 
kotlin.jvm  MediaLibraryCard 
kotlin.jvm  MediaPosterCard 
kotlin.jvm  MediaPosterGrid 
kotlin.jvm  MediaPosterRow 
kotlin.jvm  	MediaType 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OutlinedButton 
kotlin.jvm  
PosterSize 
kotlin.jvm  Recommendation 
kotlin.jvm  
SampleData 
kotlin.jvm  String 
kotlin.jvm  System 
kotlin.jvm  TVShow 
kotlin.jvm  
TextButton 
kotlin.jvm  TextOverflow 
kotlin.jvm  Volatile 
kotlin.jvm  WatchHistory 
kotlin.jvm  WatchHistoryRepository 
kotlin.jvm  
WatchStats 
kotlin.jvm  also 
kotlin.jvm  asStateFlow 
kotlin.jvm  
background 
kotlin.jvm  clip 
kotlin.jvm  com 
kotlin.jvm  count 
kotlin.jvm  emptyMap 
kotlin.jvm  
fillMaxHeight 
kotlin.jvm  filter 
kotlin.jvm  first 
kotlin.jvm  format 
kotlin.jvm  groupBy 
kotlin.jvm  indexOfFirst 
kotlin.jvm  invoke 
kotlin.jvm  let 
kotlin.jvm  map 
kotlin.jvm  	mapValues 
kotlin.jvm  maxByOrNull 
kotlin.jvm  plus 
kotlin.jvm  random 
kotlin.jvm  	removeAll 
kotlin.jvm  set 
kotlin.jvm  sortByDescending 
kotlin.jvm  sortedBy 
kotlin.jvm  sortedByDescending 
kotlin.jvm  sumOf 
kotlin.jvm  synchronized 
kotlin.jvm  take 
kotlin.jvm  
toMutableList 
kotlin.jvm  toMutableMap 
kotlin.jvm  Brush 
kotlin.ranges  CastCard 
kotlin.ranges  CastSection 
kotlin.ranges  Color 
kotlin.ranges  ContinueWatchingCard 
kotlin.ranges  ContinueWatchingItem 
kotlin.ranges  ContinueWatchingSection 
kotlin.ranges  
FilterChip 
kotlin.ranges  
FilterSection 
kotlin.ranges  HeroSection 
kotlin.ranges  InfoRow 
kotlin.ranges  IntRange 
kotlin.ranges  LibraryType 
kotlin.ranges  MediaDetailHero 
kotlin.ranges  MediaDetailInfo 
kotlin.ranges  	MediaItem 
kotlin.ranges  MediaLibrariesSection 
kotlin.ranges  MediaLibrary 
kotlin.ranges  MediaLibraryCard 
kotlin.ranges  MediaPosterCard 
kotlin.ranges  MediaPosterGrid 
kotlin.ranges  MediaPosterRow 
kotlin.ranges  	MediaType 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OutlinedButton 
kotlin.ranges  
PosterSize 
kotlin.ranges  Recommendation 
kotlin.ranges  
SampleData 
kotlin.ranges  String 
kotlin.ranges  System 
kotlin.ranges  TVShow 
kotlin.ranges  
TextButton 
kotlin.ranges  TextOverflow 
kotlin.ranges  Volatile 
kotlin.ranges  WatchHistory 
kotlin.ranges  WatchHistoryRepository 
kotlin.ranges  
WatchStats 
kotlin.ranges  also 
kotlin.ranges  asStateFlow 
kotlin.ranges  
background 
kotlin.ranges  clip 
kotlin.ranges  com 
kotlin.ranges  count 
kotlin.ranges  emptyMap 
kotlin.ranges  
fillMaxHeight 
kotlin.ranges  filter 
kotlin.ranges  first 
kotlin.ranges  format 
kotlin.ranges  groupBy 
kotlin.ranges  indexOfFirst 
kotlin.ranges  invoke 
kotlin.ranges  let 
kotlin.ranges  map 
kotlin.ranges  	mapValues 
kotlin.ranges  maxByOrNull 
kotlin.ranges  plus 
kotlin.ranges  random 
kotlin.ranges  	removeAll 
kotlin.ranges  set 
kotlin.ranges  sortByDescending 
kotlin.ranges  sortedBy 
kotlin.ranges  sortedByDescending 
kotlin.ranges  sumOf 
kotlin.ranges  synchronized 
kotlin.ranges  take 
kotlin.ranges  
toMutableList 
kotlin.ranges  toMutableMap 
kotlin.ranges  random kotlin.ranges.IntProgression  	getRANDOM kotlin.ranges.IntRange  	getRandom kotlin.ranges.IntRange  random kotlin.ranges.IntRange  Brush kotlin.sequences  CastCard kotlin.sequences  CastSection kotlin.sequences  Color kotlin.sequences  ContinueWatchingCard kotlin.sequences  ContinueWatchingItem kotlin.sequences  ContinueWatchingSection kotlin.sequences  
FilterChip kotlin.sequences  
FilterSection kotlin.sequences  HeroSection kotlin.sequences  InfoRow kotlin.sequences  LibraryType kotlin.sequences  MediaDetailHero kotlin.sequences  MediaDetailInfo kotlin.sequences  	MediaItem kotlin.sequences  MediaLibrariesSection kotlin.sequences  MediaLibrary kotlin.sequences  MediaLibraryCard kotlin.sequences  MediaPosterCard kotlin.sequences  MediaPosterGrid kotlin.sequences  MediaPosterRow kotlin.sequences  	MediaType kotlin.sequences  MutableStateFlow kotlin.sequences  OutlinedButton kotlin.sequences  
PosterSize kotlin.sequences  Recommendation kotlin.sequences  
SampleData kotlin.sequences  String kotlin.sequences  System kotlin.sequences  TVShow kotlin.sequences  
TextButton kotlin.sequences  TextOverflow kotlin.sequences  Volatile kotlin.sequences  WatchHistory kotlin.sequences  WatchHistoryRepository kotlin.sequences  
WatchStats kotlin.sequences  also kotlin.sequences  asStateFlow kotlin.sequences  
background kotlin.sequences  clip kotlin.sequences  com kotlin.sequences  count kotlin.sequences  emptyMap kotlin.sequences  
fillMaxHeight kotlin.sequences  filter kotlin.sequences  first kotlin.sequences  format kotlin.sequences  groupBy kotlin.sequences  indexOfFirst kotlin.sequences  invoke kotlin.sequences  let kotlin.sequences  map kotlin.sequences  	mapValues kotlin.sequences  maxByOrNull kotlin.sequences  plus kotlin.sequences  random kotlin.sequences  	removeAll kotlin.sequences  set kotlin.sequences  sortByDescending kotlin.sequences  sortedBy kotlin.sequences  sortedByDescending kotlin.sequences  sumOf kotlin.sequences  synchronized kotlin.sequences  take kotlin.sequences  
toMutableList kotlin.sequences  toMutableMap kotlin.sequences  Brush kotlin.text  CastCard kotlin.text  CastSection kotlin.text  Color kotlin.text  ContinueWatchingCard kotlin.text  ContinueWatchingItem kotlin.text  ContinueWatchingSection kotlin.text  
FilterChip kotlin.text  
FilterSection kotlin.text  HeroSection kotlin.text  InfoRow kotlin.text  LibraryType kotlin.text  MediaDetailHero kotlin.text  MediaDetailInfo kotlin.text  	MediaItem kotlin.text  MediaLibrariesSection kotlin.text  MediaLibrary kotlin.text  MediaLibraryCard kotlin.text  MediaPosterCard kotlin.text  MediaPosterGrid kotlin.text  MediaPosterRow kotlin.text  	MediaType kotlin.text  MutableStateFlow kotlin.text  OutlinedButton kotlin.text  
PosterSize kotlin.text  Recommendation kotlin.text  
SampleData kotlin.text  String kotlin.text  System kotlin.text  TVShow kotlin.text  
TextButton kotlin.text  TextOverflow kotlin.text  Volatile kotlin.text  WatchHistory kotlin.text  WatchHistoryRepository kotlin.text  
WatchStats kotlin.text  also kotlin.text  asStateFlow kotlin.text  
background kotlin.text  clip kotlin.text  com kotlin.text  count kotlin.text  emptyMap kotlin.text  
fillMaxHeight kotlin.text  filter kotlin.text  first kotlin.text  format kotlin.text  groupBy kotlin.text  indexOfFirst kotlin.text  invoke kotlin.text  let kotlin.text  map kotlin.text  	mapValues kotlin.text  maxByOrNull kotlin.text  plus kotlin.text  random kotlin.text  	removeAll kotlin.text  set kotlin.text  sortByDescending kotlin.text  sortedBy kotlin.text  sortedByDescending kotlin.text  sumOf kotlin.text  synchronized kotlin.text  take kotlin.text  
toMutableList kotlin.text  toMutableMap kotlin.text  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  LaunchedEffect "androidx.compose.foundation.layout  NavigationHeader "androidx.compose.foundation.layout  derivedStateOf "androidx.compose.foundation.layout  focusRequester "androidx.compose.foundation.layout  onFocusChanged "androidx.compose.foundation.layout  ContentArea +androidx.compose.foundation.layout.BoxScope  
LazyColumn +androidx.compose.foundation.layout.BoxScope  NavigationHeader +androidx.compose.foundation.layout.BoxScope  NavigationItemRow +androidx.compose.foundation.layout.BoxScope  SideNavigation +androidx.compose.foundation.layout.BoxScope  find +androidx.compose.foundation.layout.BoxScope  focusRequester +androidx.compose.foundation.layout.BoxScope  getFIND +androidx.compose.foundation.layout.BoxScope  getFOCUSRequester +androidx.compose.foundation.layout.BoxScope  getFind +androidx.compose.foundation.layout.BoxScope  getFocusRequester +androidx.compose.foundation.layout.BoxScope  getNAVIGATIONItems +androidx.compose.foundation.layout.BoxScope  getNavigationItems +androidx.compose.foundation.layout.BoxScope  getONFocusChanged +androidx.compose.foundation.layout.BoxScope  getOnFocusChanged +androidx.compose.foundation.layout.BoxScope  navigationItems +androidx.compose.foundation.layout.BoxScope  onFocusChanged +androidx.compose.foundation.layout.BoxScope  NavigationHeader .androidx.compose.foundation.layout.ColumnScope  Key &androidx.compose.material.icons.filled  KeyEventType &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  NavigationHeader &androidx.compose.material.icons.filled  NavigationItemRow &androidx.compose.material.icons.filled  key &androidx.compose.material.icons.filled  navigationItems &androidx.compose.material.icons.filled  
onKeyEvent &androidx.compose.material.icons.filled  type &androidx.compose.material.icons.filled  LaunchedEffect androidx.compose.material3  NavigationHeader androidx.compose.material3  derivedStateOf androidx.compose.material3  focusRequester androidx.compose.material3  onFocusChanged androidx.compose.material3  LaunchedEffect androidx.compose.runtime  NavigationHeader androidx.compose.runtime  derivedStateOf androidx.compose.runtime  focusRequester androidx.compose.runtime  onFocusChanged androidx.compose.runtime  zIndex androidx.compose.ui  	CenterEnd androidx.compose.ui.Alignment  	CenterEnd 'androidx.compose.ui.Alignment.Companion  focusRequester androidx.compose.ui.Modifier  getFOCUSRequester androidx.compose.ui.Modifier  getFocusRequester androidx.compose.ui.Modifier  	getSHADOW androidx.compose.ui.Modifier  	getShadow androidx.compose.ui.Modifier  	getZIndex androidx.compose.ui.Modifier  shadow androidx.compose.ui.Modifier  zIndex androidx.compose.ui.Modifier  
fillMaxHeight &androidx.compose.ui.Modifier.Companion  getFILLMaxHeight &androidx.compose.ui.Modifier.Companion  getFillMaxHeight &androidx.compose.ui.Modifier.Companion  shadow androidx.compose.ui.draw  requestFocus (androidx.compose.ui.focus.FocusRequester  invoke 2androidx.compose.ui.focus.FocusRequester.Companion  hasFocus $androidx.compose.ui.focus.FocusState  Color androidx.compose.ui.input.key  NavigationHeader androidx.compose.ui.input.key  RoundedCornerShape androidx.compose.ui.input.key  Tv androidx.compose.ui.input.key  
background androidx.compose.ui.input.key  clip androidx.compose.ui.input.key  listOf androidx.compose.ui.input.key  Float !com.google.chuangke.ui.components  NavigationHeader !com.google.chuangke.ui.components  LaunchedEffect com.google.chuangke.ui.screens  derivedStateOf com.google.chuangke.ui.screens  focusRequester com.google.chuangke.ui.screens  onFocusChanged com.google.chuangke.ui.screens  NavigationHeader 	java.lang  focusRequester 	java.lang  onFocusChanged 	java.lang  NavigationHeader kotlin  focusRequester kotlin  onFocusChanged kotlin  NavigationHeader kotlin.annotation  focusRequester kotlin.annotation  onFocusChanged kotlin.annotation  NavigationHeader kotlin.collections  focusRequester kotlin.collections  onFocusChanged kotlin.collections  NavigationHeader kotlin.comparisons  focusRequester kotlin.comparisons  onFocusChanged kotlin.comparisons  SuspendFunction1 kotlin.coroutines  NavigationHeader 	kotlin.io  focusRequester 	kotlin.io  onFocusChanged 	kotlin.io  NavigationHeader 
kotlin.jvm  focusRequester 
kotlin.jvm  onFocusChanged 
kotlin.jvm  NavigationHeader 
kotlin.ranges  focusRequester 
kotlin.ranges  onFocusChanged 
kotlin.ranges  NavigationHeader kotlin.sequences  focusRequester kotlin.sequences  onFocusChanged kotlin.sequences  NavigationHeader kotlin.text  focusRequester kotlin.text  onFocusChanged kotlin.text  CoroutineScope kotlinx.coroutines                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         