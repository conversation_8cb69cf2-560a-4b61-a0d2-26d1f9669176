-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:2:1-39:12
INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:2:1-39:12
INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:2:1-39:12
INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:2:1-39:12
MERGED from [androidx.tv:tv-foundation:1.0.0-alpha12] /Users/<USER>/.gradle/caches/8.12/transforms/284fc467713766d644bed7ba48d557a8/transformed/tv-foundation-1.0.0-alpha12/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.tv:tv-material:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8d300037e97b4dc0472b872100f7b41c/transformed/tv-material-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] /Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] /Users/<USER>/.gradle/caches/8.12/transforms/4540ef49c6fb64d260f0552af9d94e0d/transformed/navigation-runtime-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] /Users/<USER>/.gradle/caches/8.12/transforms/15a0326c8ecad61b60baefc151055566/transformed/navigation-common-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c59cb03e33148179633814ab1a1ae29/transformed/navigation-compose-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/6e3a7cf8830f07f2158870796f909173/transformed/animation-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/28cdca89a633b8ef782433ca67c6b62c/transformed/material-ripple-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/242d9c4466392361d6c62bb996666171/transformed/animation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/f76c310d969a3ed4190cf1f55ee176bb/transformed/foundation-layout-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/cbe6546639853da6fb8a594013aeb656/transformed/ui-tooling-data-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1db431f96721dc6e624d816692ad9225/transformed/ui-unit-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/89848953e98c8d588066aab0938f290c/transformed/ui-geometry-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/4d53a0b2e4941440407d9517b46e2ace/transformed/ui-util-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/9ad470bcb9dd0d5af0b9b94fc39b7e89/transformed/ui-text-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/9ee90f55b447c83a3aab9722650376d2/transformed/ui-tooling-preview-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/702bacecc1d898d0817fa62ddaaf7030/transformed/ui-graphics-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:17:1-29:12
MERGED from [androidx.leanback:leanback-grid:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1c4cfbe4281709c2e0e31c16adc84baa/transformed/leanback-grid-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.leanback:leanback:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/05ab6c6afe6c5d2781ead01cf32e528f/transformed/fragment-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/ded08f79d05a34919409a716bfccd303/transformed/media-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /Users/<USER>/.gradle/caches/8.12/transforms/4b0849ab57475bc519e3009804db5066/transformed/recyclerview-1.3.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/faa2c2af5f651211821218e70a8e03c1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/65490e283df5e59ca94efab786daba03/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8091add88fcfa9f777f9166763e38edf/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f0ac5be3029922e3774fec593499362f/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/7a5a72c644b83c76a9628bee94540ee1/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6972a6ee58eb0d6ab03e9f9ac4871bbb/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8330e653c82542c9400225449ec5b3d9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b879ff7b7f4182c216ba59b30fbb7096/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/452dd94a0e16fc79053c64347a527bb7/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1d849ba0337616c8e39fee2da20a87/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a26daa49a1ffad14b4bf7d1b718d52e/transformed/autofill-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] /Users/<USER>/.gradle/caches/8.12/transforms/19814d3ab60d9adb49543525e1345304/transformed/graphics-path-1.0.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a20b5b22fef281f41d07bc97c07e91f4/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3d87ce839a0d8f794750df80379a186a/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.activity:activity:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/439c1a4af0543ca89922ac1632c4c9c8/transformed/activity-1.10.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/51359b76a2ccf3f8bc17e49b6d9bcde3/transformed/lifecycle-livedata-core-ktx-2.9.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/f7e8f107bb3fda8f282658601ab1a898/transformed/lifecycle-livedata-core-2.9.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/58b6c5774be12e4bd6db52327d7e3861/transformed/lifecycle-livedata-2.9.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/41955641cd777793c4b6fc56efde3b5c/transformed/lifecycle-viewmodel-savedstate-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/d357014b4b186d2773d1a1f74c7d45de/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/6339f6dc227bb0ccf67fa920ddfb6d8f/transformed/lifecycle-viewmodel-2.9.1/AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/dc79c5eff9efc97faf6cf37733830e43/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e0c81292c4f35d28d7c232dbf0f54b8/transformed/savedstate-ktx-1.3.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/be766cb18e97f5e740850095d827d0a7/transformed/savedstate-compose-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/5f3c52ee0c2fb38502960ec7107111e5/transformed/savedstate-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/346a08b1382d21d36796a3f1f43e619a/transformed/lifecycle-runtime-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/56857200c07fd4b0149f1ac167f0428c/transformed/lifecycle-viewmodel-ktx-2.9.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/90ecc2c8fac09d98c2a991149040f9b0/transformed/lifecycle-runtime-ktx/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/3c2e9e7c09cc6a949a087ddd843b2b9a/transformed/lifecycle-viewmodel-compose-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] /Users/<USER>/.gradle/caches/8.12/transforms/554ff9c04d2a67a73eaff4bd206bfcaf/transformed/material-icons-extended-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] /Users/<USER>/.gradle/caches/8.12/transforms/158a232d1ff117e779e327a147f94fc5/transformed/material-icons-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/bf80fe0639fa2c4a6c29aedd29e1a260/transformed/activity-ktx-1.10.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/bd8b69647fb227881321b39758590776/transformed/activity-compose-1.10.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/83b93d63196467455f22f7bd9234a6b8/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/17ab86f9b81926a0f09f7c6ea2ef0a3f/transformed/core-ktx-1.16.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1424a9d4729a8ffb8d047c5b8be57b56/transformed/runtime-saveable-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/4c389e87d81378c9da8f16f94e5b8449/transformed/runtime-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/71c4036ceb72a8e5ce8c6dd185cf24b1/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106262d2246581c9922a30248f1e7e2d/transformed/core-viewtree-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/fc0f454b52668adf35b0dcd1402e7fcc/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e9f5de0e5e33b0609acd409d7b3a0167/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/bc2876207338d2cf5f7d4005176c054b/transformed/tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f8f1d491a3f730b287db31d6a43d14b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3501f3a64435ca93d4e948aec4406631/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e983e8d270f6459b3fa2df93e56c36fe/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8ce3306c29be66cfc3ba118c260cbe25/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3069bb8b46c4b9bc4ac9f02da8c4c7e2/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/938f335705fb73374f25b12d68695201/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:2:11-69
uses-feature#android.software.leanback
ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:5:5-7:35
	android:required
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:7:9-32
	android:name
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:6:9-49
uses-feature#android.hardware.touchscreen
ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:9:5-11:36
	android:required
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:11:9-33
	android:name
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:10:9-52
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:13:5-67
	android:name
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:13:22-64
application
ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:15:5-37:19
INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:15:5-37:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:22:5-27:19
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e9f5de0e5e33b0609acd409d7b3a0167/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e9f5de0e5e33b0609acd409d7b3a0167/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f8f1d491a3f730b287db31d6a43d14b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f8f1d491a3f730b287db31d6a43d14b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:22:9-35
	android:label
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:20:9-41
	android:fullBackupContent
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:18:9-54
	android:roundIcon
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:21:9-54
	tools:targetApi
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:25:9-29
	android:icon
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:19:9-43
	android:allowBackup
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:16:9-35
	android:banner
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:23:9-46
	android:theme
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:24:9-68
	android:dataExtractionRules
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:17:9-65
activity#com.google.chuangke.MainActivity
ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:27:9-36:20
	android:label
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:30:13-45
	android:exported
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:29:13-36
	android:name
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:28:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:31:13-35:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:32:17-69
	android:name
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:32:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:33:17-77
	android:name
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:33:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:34:17-86
	android:name
		ADDED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:34:27-83
uses-sdk
INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml
MERGED from [androidx.tv:tv-foundation:1.0.0-alpha12] /Users/<USER>/.gradle/caches/8.12/transforms/284fc467713766d644bed7ba48d557a8/transformed/tv-foundation-1.0.0-alpha12/AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-foundation:1.0.0-alpha12] /Users/<USER>/.gradle/caches/8.12/transforms/284fc467713766d644bed7ba48d557a8/transformed/tv-foundation-1.0.0-alpha12/AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-material:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8d300037e97b4dc0472b872100f7b41c/transformed/tv-material-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-material:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8d300037e97b4dc0472b872100f7b41c/transformed/tv-material-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] /Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] /Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] /Users/<USER>/.gradle/caches/8.12/transforms/4540ef49c6fb64d260f0552af9d94e0d/transformed/navigation-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] /Users/<USER>/.gradle/caches/8.12/transforms/4540ef49c6fb64d260f0552af9d94e0d/transformed/navigation-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] /Users/<USER>/.gradle/caches/8.12/transforms/15a0326c8ecad61b60baefc151055566/transformed/navigation-common-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] /Users/<USER>/.gradle/caches/8.12/transforms/15a0326c8ecad61b60baefc151055566/transformed/navigation-common-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c59cb03e33148179633814ab1a1ae29/transformed/navigation-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c59cb03e33148179633814ab1a1ae29/transformed/navigation-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/6e3a7cf8830f07f2158870796f909173/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/6e3a7cf8830f07f2158870796f909173/transformed/animation-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/28cdca89a633b8ef782433ca67c6b62c/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/28cdca89a633b8ef782433ca67c6b62c/transformed/material-ripple-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/242d9c4466392361d6c62bb996666171/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/242d9c4466392361d6c62bb996666171/transformed/animation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/f76c310d969a3ed4190cf1f55ee176bb/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/f76c310d969a3ed4190cf1f55ee176bb/transformed/foundation-layout-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/cbe6546639853da6fb8a594013aeb656/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/cbe6546639853da6fb8a594013aeb656/transformed/ui-tooling-data-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1db431f96721dc6e624d816692ad9225/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1db431f96721dc6e624d816692ad9225/transformed/ui-unit-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/89848953e98c8d588066aab0938f290c/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/89848953e98c8d588066aab0938f290c/transformed/ui-geometry-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/4d53a0b2e4941440407d9517b46e2ace/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/4d53a0b2e4941440407d9517b46e2ace/transformed/ui-util-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/9ad470bcb9dd0d5af0b9b94fc39b7e89/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/9ad470bcb9dd0d5af0b9b94fc39b7e89/transformed/ui-text-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/9ee90f55b447c83a3aab9722650376d2/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/9ee90f55b447c83a3aab9722650376d2/transformed/ui-tooling-preview-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/702bacecc1d898d0817fa62ddaaf7030/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/702bacecc1d898d0817fa62ddaaf7030/transformed/ui-graphics-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback-grid:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1c4cfbe4281709c2e0e31c16adc84baa/transformed/leanback-grid-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.leanback:leanback-grid:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1c4cfbe4281709c2e0e31c16adc84baa/transformed/leanback-grid-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.leanback:leanback:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.leanback:leanback:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/05ab6c6afe6c5d2781ead01cf32e528f/transformed/fragment-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/05ab6c6afe6c5d2781ead01cf32e528f/transformed/fragment-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/ded08f79d05a34919409a716bfccd303/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/ded08f79d05a34919409a716bfccd303/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /Users/<USER>/.gradle/caches/8.12/transforms/4b0849ab57475bc519e3009804db5066/transformed/recyclerview-1.3.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /Users/<USER>/.gradle/caches/8.12/transforms/4b0849ab57475bc519e3009804db5066/transformed/recyclerview-1.3.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/faa2c2af5f651211821218e70a8e03c1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/faa2c2af5f651211821218e70a8e03c1/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/65490e283df5e59ca94efab786daba03/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/65490e283df5e59ca94efab786daba03/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8091add88fcfa9f777f9166763e38edf/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8091add88fcfa9f777f9166763e38edf/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f0ac5be3029922e3774fec593499362f/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f0ac5be3029922e3774fec593499362f/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/7a5a72c644b83c76a9628bee94540ee1/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/7a5a72c644b83c76a9628bee94540ee1/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6972a6ee58eb0d6ab03e9f9ac4871bbb/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/6972a6ee58eb0d6ab03e9f9ac4871bbb/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8330e653c82542c9400225449ec5b3d9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8330e653c82542c9400225449ec5b3d9/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b879ff7b7f4182c216ba59b30fbb7096/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b879ff7b7f4182c216ba59b30fbb7096/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/452dd94a0e16fc79053c64347a527bb7/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/452dd94a0e16fc79053c64347a527bb7/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1d849ba0337616c8e39fee2da20a87/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1d849ba0337616c8e39fee2da20a87/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a26daa49a1ffad14b4bf7d1b718d52e/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2a26daa49a1ffad14b4bf7d1b718d52e/transformed/autofill-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] /Users/<USER>/.gradle/caches/8.12/transforms/19814d3ab60d9adb49543525e1345304/transformed/graphics-path-1.0.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] /Users/<USER>/.gradle/caches/8.12/transforms/19814d3ab60d9adb49543525e1345304/transformed/graphics-path-1.0.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a20b5b22fef281f41d07bc97c07e91f4/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a20b5b22fef281f41d07bc97c07e91f4/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3d87ce839a0d8f794750df80379a186a/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3d87ce839a0d8f794750df80379a186a/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/439c1a4af0543ca89922ac1632c4c9c8/transformed/activity-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/439c1a4af0543ca89922ac1632c4c9c8/transformed/activity-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/51359b76a2ccf3f8bc17e49b6d9bcde3/transformed/lifecycle-livedata-core-ktx-2.9.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/51359b76a2ccf3f8bc17e49b6d9bcde3/transformed/lifecycle-livedata-core-ktx-2.9.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/f7e8f107bb3fda8f282658601ab1a898/transformed/lifecycle-livedata-core-2.9.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/f7e8f107bb3fda8f282658601ab1a898/transformed/lifecycle-livedata-core-2.9.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/58b6c5774be12e4bd6db52327d7e3861/transformed/lifecycle-livedata-2.9.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/58b6c5774be12e4bd6db52327d7e3861/transformed/lifecycle-livedata-2.9.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/41955641cd777793c4b6fc56efde3b5c/transformed/lifecycle-viewmodel-savedstate-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/41955641cd777793c4b6fc56efde3b5c/transformed/lifecycle-viewmodel-savedstate-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/d357014b4b186d2773d1a1f74c7d45de/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/d357014b4b186d2773d1a1f74c7d45de/transformed/lifecycle-runtime-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/6339f6dc227bb0ccf67fa920ddfb6d8f/transformed/lifecycle-viewmodel-2.9.1/AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/6339f6dc227bb0ccf67fa920ddfb6d8f/transformed/lifecycle-viewmodel-2.9.1/AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/dc79c5eff9efc97faf6cf37733830e43/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/dc79c5eff9efc97faf6cf37733830e43/transformed/lifecycle-viewmodel-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e0c81292c4f35d28d7c232dbf0f54b8/transformed/savedstate-ktx-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e0c81292c4f35d28d7c232dbf0f54b8/transformed/savedstate-ktx-1.3.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/be766cb18e97f5e740850095d827d0a7/transformed/savedstate-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/be766cb18e97f5e740850095d827d0a7/transformed/savedstate-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/5f3c52ee0c2fb38502960ec7107111e5/transformed/savedstate-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/5f3c52ee0c2fb38502960ec7107111e5/transformed/savedstate-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/346a08b1382d21d36796a3f1f43e619a/transformed/lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/346a08b1382d21d36796a3f1f43e619a/transformed/lifecycle-runtime-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/56857200c07fd4b0149f1ac167f0428c/transformed/lifecycle-viewmodel-ktx-2.9.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/56857200c07fd4b0149f1ac167f0428c/transformed/lifecycle-viewmodel-ktx-2.9.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/90ecc2c8fac09d98c2a991149040f9b0/transformed/lifecycle-runtime-ktx/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/90ecc2c8fac09d98c2a991149040f9b0/transformed/lifecycle-runtime-ktx/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/3c2e9e7c09cc6a949a087ddd843b2b9a/transformed/lifecycle-viewmodel-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/3c2e9e7c09cc6a949a087ddd843b2b9a/transformed/lifecycle-viewmodel-compose-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] /Users/<USER>/.gradle/caches/8.12/transforms/554ff9c04d2a67a73eaff4bd206bfcaf/transformed/material-icons-extended-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] /Users/<USER>/.gradle/caches/8.12/transforms/554ff9c04d2a67a73eaff4bd206bfcaf/transformed/material-icons-extended-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] /Users/<USER>/.gradle/caches/8.12/transforms/158a232d1ff117e779e327a147f94fc5/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] /Users/<USER>/.gradle/caches/8.12/transforms/158a232d1ff117e779e327a147f94fc5/transformed/material-icons-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/bf80fe0639fa2c4a6c29aedd29e1a260/transformed/activity-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/bf80fe0639fa2c4a6c29aedd29e1a260/transformed/activity-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/bd8b69647fb227881321b39758590776/transformed/activity-compose-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] /Users/<USER>/.gradle/caches/8.12/transforms/bd8b69647fb227881321b39758590776/transformed/activity-compose-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/83b93d63196467455f22f7bd9234a6b8/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/83b93d63196467455f22f7bd9234a6b8/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/17ab86f9b81926a0f09f7c6ea2ef0a3f/transformed/core-ktx-1.16.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/17ab86f9b81926a0f09f7c6ea2ef0a3f/transformed/core-ktx-1.16.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1424a9d4729a8ffb8d047c5b8be57b56/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1424a9d4729a8ffb8d047c5b8be57b56/transformed/runtime-saveable-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/4c389e87d81378c9da8f16f94e5b8449/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/4c389e87d81378c9da8f16f94e5b8449/transformed/runtime-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/71c4036ceb72a8e5ce8c6dd185cf24b1/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/71c4036ceb72a8e5ce8c6dd185cf24b1/transformed/annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106262d2246581c9922a30248f1e7e2d/transformed/core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106262d2246581c9922a30248f1e7e2d/transformed/core-viewtree-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/fc0f454b52668adf35b0dcd1402e7fcc/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/fc0f454b52668adf35b0dcd1402e7fcc/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e9f5de0e5e33b0609acd409d7b3a0167/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e9f5de0e5e33b0609acd409d7b3a0167/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/bc2876207338d2cf5f7d4005176c054b/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/bc2876207338d2cf5f7d4005176c054b/transformed/tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f8f1d491a3f730b287db31d6a43d14b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f8f1d491a3f730b287db31d6a43d14b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3501f3a64435ca93d4e948aec4406631/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3501f3a64435ca93d4e948aec4406631/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e983e8d270f6459b3fa2df93e56c36fe/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e983e8d270f6459b3fa2df93e56c36fe/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8ce3306c29be66cfc3ba118c260cbe25/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8ce3306c29be66cfc3ba118c260cbe25/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3069bb8b46c4b9bc4ac9f02da8c4c7e2/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3069bb8b46c4b9bc4ac9f02da8c4c7e2/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/938f335705fb73374f25b12d68695201/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/938f335705fb73374f25b12d68695201/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e9f5de0e5e33b0609acd409d7b3a0167/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/e9f5de0e5e33b0609acd409d7b3a0167/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
permission#com.google.chuangke.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
uses-permission#com.google.chuangke.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:50:25-92
