R_DEF: Internal format may change without notice
local
color md_theme_background
color md_theme_error
color md_theme_errorContainer
color md_theme_inverseOnSurface
color md_theme_inversePrimary
color md_theme_inverseSurface
color md_theme_onBackground
color md_theme_onError
color md_theme_onErrorContainer
color md_theme_onPrimary
color md_theme_onPrimaryContainer
color md_theme_onSecondary
color md_theme_onSecondaryContainer
color md_theme_onSurface
color md_theme_onSurfaceVariant
color md_theme_onTertiary
color md_theme_onTertiaryContainer
color md_theme_outline
color md_theme_outlineVariant
color md_theme_primary
color md_theme_primaryContainer
color md_theme_secondary
color md_theme_secondaryContainer
color md_theme_surface
color md_theme_surfaceVariant
color md_theme_tertiary
color md_theme_tertiaryContainer
drawable app_banner
drawable ic_launcher_foreground
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string menu_toggle
string nav_favorites
string nav_home
string nav_live
string nav_movies
string nav_search
string nav_settings
string nav_tv_shows
string navigation_drawer
xml backup_rules
xml data_extraction_rules
