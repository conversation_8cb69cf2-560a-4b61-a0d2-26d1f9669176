1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.google.chuangke"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="34" />
10
11    <uses-feature
11-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:5:5-7:35
12        android:name="android.software.leanback"
12-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:6:9-49
13        android:required="true" />
13-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:7:9-32
14    <uses-feature
14-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:9:5-11:36
15        android:name="android.hardware.touchscreen"
15-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:10:9-52
16        android:required="false" />
16-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:11:9-33
17
18    <uses-permission android:name="android.permission.INTERNET" />
18-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:13:5-67
18-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:13:22-64
19
20    <permission
20-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
21        android:name="com.google.chuangke.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.google.chuangke.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
25
26    <application
26-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:15:5-37:19
27        android:allowBackup="true"
27-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:16:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/AndroidManifest.xml:28:18-86
29        android:banner="@drawable/app_banner"
29-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:23:9-46
30        android:dataExtractionRules="@xml/data_extraction_rules"
30-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:17:9-65
31        android:debuggable="true"
32        android:extractNativeLibs="false"
33        android:fullBackupContent="@xml/backup_rules"
33-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:18:9-54
34        android:icon="@mipmap/ic_launcher"
34-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:19:9-43
35        android:label="@string/app_name"
35-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:20:9-41
36        android:roundIcon="@mipmap/ic_launcher_round"
36-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:21:9-54
37        android:supportsRtl="true"
37-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:22:9-35
38        android:testOnly="true"
39        android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" >
39-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:24:9-68
40        <activity
40-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:27:9-36:20
41            android:name="com.google.chuangke.MainActivity"
41-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:28:13-41
42            android:exported="true"
42-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:29:13-36
43            android:label="@string/app_name" >
43-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:30:13-45
44            <intent-filter>
44-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:31:13-35:29
45                <action android:name="android.intent.action.MAIN" />
45-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:32:17-69
45-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:32:25-66
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:33:17-77
47-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:33:27-74
48                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
48-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:34:17-86
48-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:34:27-83
49            </intent-filter>
50        </activity>
51        <activity
51-->[androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
52            android:name="androidx.compose.ui.tooling.PreviewActivity"
52-->[androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
53            android:exported="true" />
53-->[androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/1baf1db300f56629bd6e5e064faf6873/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
54        <activity
54-->[androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:23:9-26:79
55            android:name="androidx.activity.ComponentActivity"
55-->[androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:24:13-63
56            android:exported="true"
56-->[androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:25:13-36
57            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
57-->[androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/e62e611cb893a86f8cb527ccb768de89/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:26:13-76
58
59        <provider
59-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
60            android:name="androidx.startup.InitializationProvider"
60-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:25:13-67
61            android:authorities="com.google.chuangke.androidx-startup"
61-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:26:13-68
62            android:exported="false" >
62-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:27:13-37
63            <meta-data
63-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
64                android:name="androidx.emoji2.text.EmojiCompatInitializer"
64-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:30:17-75
65                android:value="androidx.startup" />
65-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b3e530dc1e3b69c667d5c7f7f486f19/transformed/emoji2-1.4.0/AndroidManifest.xml:31:17-49
66            <meta-data
66-->[androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:29:13-31:52
67                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
67-->[androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:30:17-78
68                android:value="androidx.startup" />
68-->[androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/4ad4bf79fb8cc5a14eee801ee22e16b6/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:29:13-31:52
70                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
70-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:30:17-85
71                android:value="androidx.startup" />
71-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:31:17-49
72        </provider>
73
74        <receiver
74-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:34:9-52:20
75            android:name="androidx.profileinstaller.ProfileInstallReceiver"
75-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:35:13-76
76            android:directBootAware="false"
76-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:36:13-44
77            android:enabled="true"
77-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:37:13-35
78            android:exported="true"
78-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:38:13-36
79            android:permission="android.permission.DUMP" >
79-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:39:13-57
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:40:13-42:29
81                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:41:17-91
81-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:41:25-88
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:43:13-45:29
84                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
84-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:44:17-85
84-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:44:25-82
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:46:13-48:29
87                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
87-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:47:17-88
87-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:47:25-85
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:49:13-51:29
90                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
90-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:50:17-95
90-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/ad7a3b00ca240f2598811788f284e93e/transformed/profileinstaller-1.4.1/AndroidManifest.xml:50:25-92
91            </intent-filter>
92        </receiver>
93    </application>
94
95</manifest>
