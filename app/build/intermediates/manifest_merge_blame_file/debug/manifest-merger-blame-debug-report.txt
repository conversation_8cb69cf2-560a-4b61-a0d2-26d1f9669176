1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.google.chuangke"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-feature
11-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:5:5-7:35
12        android:name="android.software.leanback"
12-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:6:9-49
13        android:required="true" />
13-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:7:9-32
14    <uses-feature
14-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:9:5-11:36
15        android:name="android.hardware.touchscreen"
15-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:10:9-52
16        android:required="false" />
16-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:11:9-33
17
18    <uses-permission android:name="android.permission.INTERNET" />
18-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:13:5-67
18-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:13:22-64
19
20    <permission
20-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/AndroidManifest.xml:22:5-24:47
21        android:name="com.google.chuangke.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.google.chuangke.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
24-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/AndroidManifest.xml:26:22-94
25
26    <application
26-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:15:5-36:19
27        android:allowBackup="true"
27-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:16:9-35
28        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
28-->[androidx.core:core:1.16.0] /Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/AndroidManifest.xml:28:18-86
29        android:banner="@drawable/app_banner"
29-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:23:9-46
30        android:dataExtractionRules="@xml/data_extraction_rules"
30-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:17:9-65
31        android:debuggable="true"
32        android:extractNativeLibs="true"
33        android:fullBackupContent="@xml/backup_rules"
33-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:18:9-54
34        android:icon="@mipmap/ic_launcher"
34-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:19:9-43
35        android:label="@string/app_name"
35-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:20:9-41
36        android:roundIcon="@mipmap/ic_launcher_round"
36-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:21:9-54
37        android:supportsRtl="true"
37-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:22:9-35
38        android:testOnly="true" >
39        <activity
39-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:26:9-35:20
40            android:name="com.google.chuangke.MainActivity"
40-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:27:13-41
41            android:exported="true"
41-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:28:13-36
42            android:label="@string/app_name" >
42-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:29:13-45
43            <intent-filter>
43-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:30:13-34:29
44                <action android:name="android.intent.action.MAIN" />
44-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:31:17-69
44-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:31:25-66
45
46                <category android:name="android.intent.category.LAUNCHER" />
46-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:32:17-77
46-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:32:27-74
47                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
47-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:33:17-86
47-->/Users/<USER>/VSSpace/litchi/app/src/main/AndroidManifest.xml:33:27-83
48            </intent-filter>
49        </activity>
50        <activity
50-->[androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/b444deaaa5cebb1d1ac07f816560ab74/transformed/ui-tooling-release/AndroidManifest.xml:23:9-25:39
51            android:name="androidx.compose.ui.tooling.PreviewActivity"
51-->[androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/b444deaaa5cebb1d1ac07f816560ab74/transformed/ui-tooling-release/AndroidManifest.xml:24:13-71
52            android:exported="true" />
52-->[androidx.compose.ui:ui-tooling-android:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/b444deaaa5cebb1d1ac07f816560ab74/transformed/ui-tooling-release/AndroidManifest.xml:25:13-36
53        <activity
53-->[androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/76d38c8e03d64bba9c9ef9b2d6709412/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:23:9-26:79
54            android:name="androidx.activity.ComponentActivity"
54-->[androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/76d38c8e03d64bba9c9ef9b2d6709412/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:24:13-63
55            android:exported="true"
55-->[androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/76d38c8e03d64bba9c9ef9b2d6709412/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:25:13-36
56            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
56-->[androidx.compose.ui:ui-test-manifest:1.8.3] /Users/<USER>/.gradle/caches/8.12/transforms/76d38c8e03d64bba9c9ef9b2d6709412/transformed/ui-test-manifest-1.8.3/AndroidManifest.xml:26:13-76
57
58        <provider
58-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/c5ab031b27b52c2383d75c0a27f7f3c8/transformed/emoji2-1.4.0/AndroidManifest.xml:24:9-32:20
59            android:name="androidx.startup.InitializationProvider"
59-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/c5ab031b27b52c2383d75c0a27f7f3c8/transformed/emoji2-1.4.0/AndroidManifest.xml:25:13-67
60            android:authorities="com.google.chuangke.androidx-startup"
60-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/c5ab031b27b52c2383d75c0a27f7f3c8/transformed/emoji2-1.4.0/AndroidManifest.xml:26:13-68
61            android:exported="false" >
61-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/c5ab031b27b52c2383d75c0a27f7f3c8/transformed/emoji2-1.4.0/AndroidManifest.xml:27:13-37
62            <meta-data
62-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/c5ab031b27b52c2383d75c0a27f7f3c8/transformed/emoji2-1.4.0/AndroidManifest.xml:29:13-31:52
63                android:name="androidx.emoji2.text.EmojiCompatInitializer"
63-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/c5ab031b27b52c2383d75c0a27f7f3c8/transformed/emoji2-1.4.0/AndroidManifest.xml:30:17-75
64                android:value="androidx.startup" />
64-->[androidx.emoji2:emoji2:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/c5ab031b27b52c2383d75c0a27f7f3c8/transformed/emoji2-1.4.0/AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/2b8d6e2455a5ac3906d3385da5b3a71a/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:29:13-31:52
66                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
66-->[androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/2b8d6e2455a5ac3906d3385da5b3a71a/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:30:17-78
67                android:value="androidx.startup" />
67-->[androidx.lifecycle:lifecycle-process:2.9.1] /Users/<USER>/.gradle/caches/8.12/transforms/2b8d6e2455a5ac3906d3385da5b3a71a/transformed/lifecycle-process-2.9.1/AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:29:13-31:52
69                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
69-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:30:17-85
70                android:value="androidx.startup" />
70-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:31:17-49
71        </provider>
72
73        <receiver
73-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:34:9-52:20
74            android:name="androidx.profileinstaller.ProfileInstallReceiver"
74-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:35:13-76
75            android:directBootAware="false"
75-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:36:13-44
76            android:enabled="true"
76-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:37:13-35
77            android:exported="true"
77-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:38:13-36
78            android:permission="android.permission.DUMP" >
78-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:39:13-57
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:40:13-42:29
80                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
80-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:41:17-91
80-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:41:25-88
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:43:13-45:29
83                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
83-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:44:17-85
83-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:44:25-82
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:46:13-48:29
86                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:47:17-88
86-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:47:25-85
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:49:13-51:29
89                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
89-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:50:17-95
89-->[androidx.profileinstaller:profileinstaller:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/6ca8154f311ad3f4b29d9c72ce30ecb6/transformed/profileinstaller-1.4.1/AndroidManifest.xml:50:25-92
90            </intent-filter>
91        </receiver>
92    </application>
93
94</manifest>
