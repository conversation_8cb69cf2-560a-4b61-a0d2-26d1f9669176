<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="urn:oasis:names:tc:xliff:document:1.2" xmlns:ns2="http://schemas.android.com/tools">
    <attr format="reference" name="coordinatorLayoutStyle"/>
    <attr format="reference" name="drawerArrowStyle"/>
    <attr format="dimension" name="height"/>
    <attr format="boolean" name="isLightTheme"/>
    <attr format="reference" name="nestedScrollViewStyle"/>
    <attr format="reference" name="recyclerViewStyle"/>
    <attr format="string" name="title"/>
    <bool name="abc_action_bar_embed_tabs">true</bool>
    <bool name="abc_allow_stacked_button_bar">false</bool>
    <bool name="abc_config_actionMenuItemAllCaps">true</bool>
    <color name="abc_input_method_navigation_guard">@android:color/black</color>
    <color name="abc_search_url_text_normal">#7fa87f</color>
    <color name="abc_search_url_text_pressed">@android:color/black</color>
    <color name="abc_search_url_text_selected">@android:color/black</color>
    <color name="accent_material_dark">@color/material_deep_teal_200</color>
    <color name="accent_material_light">@color/material_deep_teal_500</color>
    <color name="androidx_core_ripple_material_light">#1f000000</color>
    <color name="androidx_core_secondary_text_default_material_light">#8a000000</color>
    <color name="background_floating_material_dark">@color/material_grey_800</color>
    <color name="background_floating_material_light">@android:color/white</color>
    <color name="background_material_dark">@color/material_grey_850</color>
    <color name="background_material_light">@color/material_grey_50</color>
    <color name="bright_foreground_disabled_material_dark">#80ffffff</color>
    <color name="bright_foreground_disabled_material_light">#80000000</color>
    <color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color>
    <color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color>
    <color name="bright_foreground_material_dark">@android:color/white</color>
    <color name="bright_foreground_material_light">@android:color/black</color>
    <color name="button_material_dark">#ff5a595b</color>
    <color name="button_material_light">#ffd6d7d7</color>
    <color name="call_notification_answer_color">#1d873b</color>
    <color name="call_notification_decline_color">#d93025</color>
    <color name="dim_foreground_disabled_material_dark">#80bebebe</color>
    <color name="dim_foreground_disabled_material_light">#80323232</color>
    <color name="dim_foreground_material_dark">#ffbebebe</color>
    <color name="dim_foreground_material_light">#ff323232</color>
    <color name="error_color_material_dark">#ff7043</color>
    <color name="error_color_material_light">#ff5722</color>
    <color name="foreground_material_dark">@android:color/white</color>
    <color name="foreground_material_light">@android:color/black</color>
    <color name="highlighted_text_material_dark">#6680cbc4</color>
    <color name="highlighted_text_material_light">#66009688</color>
    <color name="lb_action_text_color">#EEEEEE</color>
    <color name="lb_background_protection">#99000000</color>
    <color name="lb_basic_card_bg_color">#FF263238</color>
    <color name="lb_basic_card_content_text_color">#B3EEEEEE</color>
    <color name="lb_basic_card_info_bg_color">#FF37474F</color>
    <color name="lb_basic_card_title_text_color">#FFEEEEEE</color>
    <color name="lb_browse_header_color">#FFFFFF</color>
    <color name="lb_browse_header_description_color">#AAFFFFFF</color>
    <color name="lb_browse_title_color">#EEEEEE</color>
    <color name="lb_control_button_color">#66EEEEEE</color>
    <color name="lb_control_button_text">#EEEEEE</color>
    <color name="lb_default_brand_color">#FF37474F</color>
    <color name="lb_default_brand_color_dark">#FF263238</color>
    <color name="lb_default_search_color">#FF86C739</color>
    <color name="lb_default_search_icon_color">#FFFFFFFF</color>
    <color name="lb_details_description_body_color">#B2EEEEEE</color>
    <color name="lb_details_description_color">#EEEEEE</color>
    <color name="lb_details_overview_bg_color">#1B1B1B</color>
    <color name="lb_error_background_color_opaque">#262626</color>
    <color name="lb_error_background_color_translucent">#E6000000</color>
    <color name="lb_error_message">#80EEEEEE</color>
    <color name="lb_grey">#888888</color>
    <color name="lb_guidedactions_background">#FF111111</color>
    <color name="lb_guidedactions_background_dark">#FF080808</color>
    <color name="lb_guidedactions_item_unselected_text_color">#FFF1F1F1</color>
    <color name="lb_list_item_unselected_text_color">#FFF1F1F1</color>
    <color name="lb_media_background_color">#FF384248</color>
    <color name="lb_page_indicator_arrow_background">#EEEEEE</color>
    <color name="lb_page_indicator_arrow_shadow">#4C000000</color>
    <color name="lb_page_indicator_dot">#014269</color>
    <color name="lb_playback_background_progress_color">#19FFFFFF</color>
    <color name="lb_playback_controls_background_dark">#c0000000</color>
    <color name="lb_playback_controls_background_light">#80000000</color>
    <color name="lb_playback_controls_time_text_color">#B2EEEEEE</color>
    <color name="lb_playback_icon_highlight_no_theme">#ff40c4ff</color>
    <color name="lb_playback_media_row_highlight_color">#1AFFFFFF</color>
    <color name="lb_playback_media_row_separator_highlight_color">#1AFFFFFF</color>
    <color name="lb_playback_now_playing_bar_color">#FFEEEEEE</color>
    <color name="lb_playback_progress_color_no_theme">#ff40c4ff</color>
    <color name="lb_playback_progress_secondary_color_no_theme">#d3d3d3</color>
    <color name="lb_playback_secondary_progress_color">#33FFFFFF</color>
    <color name="lb_search_bar_hint">#FF888888</color>
    <color name="lb_search_bar_hint_speech_mode">#66222222</color>
    <color name="lb_search_bar_text">#80EEEEEE</color>
    <color name="lb_search_bar_text_speech_mode">#FF444444</color>
    <color name="lb_search_plate_hint_text_color">#FFCCCCCC</color>
    <color name="lb_speech_orb_not_recording">#CCCCCC</color>
    <color name="lb_speech_orb_not_recording_icon">#555555</color>
    <color name="lb_speech_orb_not_recording_pulsed">#EEEEEE</color>
    <color name="lb_speech_orb_recording">#ff4343</color>
    <color name="lb_tv_white">#FFCCCCCC</color>
    <color name="lb_view_dim_mask_color">#000000</color>
    <color name="material_blue_grey_800">#ff37474f</color>
    <color name="material_blue_grey_900">#ff263238</color>
    <color name="material_blue_grey_950">#ff21272b</color>
    <color name="material_deep_teal_200">#ff80cbc4</color>
    <color name="material_deep_teal_500">#ff009688</color>
    <color name="material_grey_100">#fff5f5f5</color>
    <color name="material_grey_300">#ffe0e0e0</color>
    <color name="material_grey_50">#fffafafa</color>
    <color name="material_grey_600">#ff757575</color>
    <color name="material_grey_800">#ff424242</color>
    <color name="material_grey_850">#ff303030</color>
    <color name="material_grey_900">#ff212121</color>
    <color name="md_theme_background">#121212</color>
    <color name="md_theme_error">#CF6679</color>
    <color name="md_theme_errorContainer">#B00020</color>
    <color name="md_theme_inverseOnSurface">#000000</color>
    <color name="md_theme_inversePrimary">#6200EE</color>
    <color name="md_theme_inverseSurface">#FFFFFF</color>
    <color name="md_theme_onBackground">#FFFFFF</color>
    <color name="md_theme_onError">#000000</color>
    <color name="md_theme_onErrorContainer">#FFFFFF</color>
    <color name="md_theme_onPrimary">#000000</color>
    <color name="md_theme_onPrimaryContainer">#FFFFFF</color>
    <color name="md_theme_onSecondary">#000000</color>
    <color name="md_theme_onSecondaryContainer">#FFFFFF</color>
    <color name="md_theme_onSurface">#FFFFFF</color>
    <color name="md_theme_onSurfaceVariant">#CCCCCC</color>
    <color name="md_theme_onTertiary">#000000</color>
    <color name="md_theme_onTertiaryContainer">#FFFFFF</color>
    <color name="md_theme_outline">#666666</color>
    <color name="md_theme_outlineVariant">#444444</color>
    <color name="md_theme_primary">#BB86FC</color>
    <color name="md_theme_primaryContainer">#3700B3</color>
    <color name="md_theme_secondary">#03DAC6</color>
    <color name="md_theme_secondaryContainer">#018786</color>
    <color name="md_theme_surface">#1E1E1E</color>
    <color name="md_theme_surfaceVariant">#2C2C2C</color>
    <color name="md_theme_tertiary">#CF6679</color>
    <color name="md_theme_tertiaryContainer">#B00020</color>
    <color name="notification_action_color_filter">#ffffffff</color>
    <color name="notification_icon_bg_color">#ff9e9e9e</color>
    <color name="notification_material_background_media_default_color">#ff424242</color>
    <color name="primary_dark_material_dark">@android:color/black</color>
    <color name="primary_dark_material_light">@color/material_grey_600</color>
    <color name="primary_material_dark">@color/material_grey_900</color>
    <color name="primary_material_light">@color/material_grey_100</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="primary_text_default_material_light">#de000000</color>
    <color name="primary_text_disabled_material_dark">#4Dffffff</color>
    <color name="primary_text_disabled_material_light">#39000000</color>
    <color name="ripple_material_dark">#33ffffff</color>
    <color name="ripple_material_light">#1f000000</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="secondary_text_default_material_light">#8a000000</color>
    <color name="secondary_text_disabled_material_dark">#36ffffff</color>
    <color name="secondary_text_disabled_material_light">#24000000</color>
    <color name="switch_thumb_disabled_material_dark">#ff616161</color>
    <color name="switch_thumb_disabled_material_light">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_dark">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_light">#fff1f1f1</color>
    <color name="tooltip_background_dark">#e6616161</color>
    <color name="tooltip_background_light">#e6FFFFFF</color>
    <dimen name="abc_action_bar_content_inset_material">16dp</dimen>
    <dimen name="abc_action_bar_content_inset_with_nav">72dp</dimen>
    <dimen name="abc_action_bar_default_height_material">56dp</dimen>
    <dimen name="abc_action_bar_default_padding_end_material">0dp</dimen>
    <dimen name="abc_action_bar_default_padding_start_material">0dp</dimen>
    <dimen name="abc_action_bar_elevation_material">4dp</dimen>
    <dimen name="abc_action_bar_icon_vertical_padding_material">16dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_end_material">10dp</dimen>
    <dimen name="abc_action_bar_overflow_padding_start_material">6dp</dimen>
    <dimen name="abc_action_bar_stacked_max_height">48dp</dimen>
    <dimen name="abc_action_bar_stacked_tab_max_width">180dp</dimen>
    <dimen name="abc_action_bar_subtitle_bottom_margin_material">5dp</dimen>
    <dimen name="abc_action_bar_subtitle_top_margin_material">-3dp</dimen>
    <dimen name="abc_action_button_min_height_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_material">48dp</dimen>
    <dimen name="abc_action_button_min_width_overflow_material">36dp</dimen>
    <dimen name="abc_alert_dialog_button_bar_height">48dp</dimen>
    <dimen name="abc_alert_dialog_button_dimen">48dp</dimen>
    <dimen name="abc_button_inset_horizontal_material">@dimen/abc_control_inset_material</dimen>
    <dimen name="abc_button_inset_vertical_material">6dp</dimen>
    <dimen name="abc_button_padding_horizontal_material">8dp</dimen>
    <dimen name="abc_button_padding_vertical_material">@dimen/abc_control_padding_material</dimen>
    <dimen name="abc_cascading_menus_min_smallest_width">720dp</dimen>
    <dimen name="abc_config_prefDialogWidth">320dp</dimen>
    <dimen name="abc_control_corner_material">2dp</dimen>
    <dimen name="abc_control_inset_material">4dp</dimen>
    <dimen name="abc_control_padding_material">4dp</dimen>
    <dimen name="abc_dialog_corner_radius_material">2dp</dimen>
    <item name="abc_dialog_fixed_height_major" type="dimen">80%</item>
    <item name="abc_dialog_fixed_height_minor" type="dimen">100%</item>
    <item name="abc_dialog_fixed_width_major" type="dimen">320dp</item>
    <item name="abc_dialog_fixed_width_minor" type="dimen">320dp</item>
    <dimen name="abc_dialog_list_padding_bottom_no_buttons">8dp</dimen>
    <dimen name="abc_dialog_list_padding_top_no_title">8dp</dimen>
    <item name="abc_dialog_min_width_major" type="dimen">65%</item>
    <item name="abc_dialog_min_width_minor" type="dimen">95%</item>
    <dimen name="abc_dialog_padding_material">24dp</dimen>
    <dimen name="abc_dialog_padding_top_material">18dp</dimen>
    <dimen name="abc_dialog_title_divider_material">8dp</dimen>
    <item format="float" name="abc_disabled_alpha_material_dark" type="dimen">0.30</item>
    <item format="float" name="abc_disabled_alpha_material_light" type="dimen">0.26</item>
    <dimen name="abc_dropdownitem_icon_width">32dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_left">8dip</dimen>
    <dimen name="abc_dropdownitem_text_padding_right">8dip</dimen>
    <dimen name="abc_edit_text_inset_bottom_material">7dp</dimen>
    <dimen name="abc_edit_text_inset_horizontal_material">4dp</dimen>
    <dimen name="abc_edit_text_inset_top_material">10dp</dimen>
    <dimen name="abc_floating_window_z">16dp</dimen>
    <dimen name="abc_list_item_padding_horizontal_material">@dimen/abc_action_bar_content_inset_material</dimen>
    <dimen name="abc_panel_menu_list_width">296dp</dimen>
    <dimen name="abc_progress_bar_height_material">4dp</dimen>
    <dimen name="abc_search_view_preferred_height">48dip</dimen>
    <dimen name="abc_search_view_preferred_width">320dip</dimen>
    <dimen name="abc_seekbar_track_background_height_material">2dp</dimen>
    <dimen name="abc_seekbar_track_progress_height_material">2dp</dimen>
    <dimen name="abc_select_dialog_padding_start_material">20dp</dimen>
    <dimen name="abc_switch_padding">3dp</dimen>
    <dimen name="abc_text_size_body_1_material">14sp</dimen>
    <dimen name="abc_text_size_body_2_material">14sp</dimen>
    <dimen name="abc_text_size_button_material">14sp</dimen>
    <dimen name="abc_text_size_caption_material">12sp</dimen>
    <dimen name="abc_text_size_display_1_material">34sp</dimen>
    <dimen name="abc_text_size_display_2_material">45sp</dimen>
    <dimen name="abc_text_size_display_3_material">56sp</dimen>
    <dimen name="abc_text_size_display_4_material">112sp</dimen>
    <dimen name="abc_text_size_headline_material">24sp</dimen>
    <dimen name="abc_text_size_large_material">22sp</dimen>
    <dimen name="abc_text_size_medium_material">18sp</dimen>
    <dimen name="abc_text_size_menu_header_material">14sp</dimen>
    <dimen name="abc_text_size_menu_material">16sp</dimen>
    <dimen name="abc_text_size_small_material">14sp</dimen>
    <dimen name="abc_text_size_subhead_material">16sp</dimen>
    <dimen name="abc_text_size_subtitle_material_toolbar">16dp</dimen>
    <dimen name="abc_text_size_title_material">20sp</dimen>
    <dimen name="abc_text_size_title_material_toolbar">20dp</dimen>
    <dimen name="compat_button_inset_horizontal_material">4dp</dimen>
    <dimen name="compat_button_inset_vertical_material">6dp</dimen>
    <dimen name="compat_button_padding_horizontal_material">8dp</dimen>
    <dimen name="compat_button_padding_vertical_material">4dp</dimen>
    <dimen name="compat_control_corner_material">2dp</dimen>
    <dimen name="compat_notification_large_icon_max_height">320dp</dimen>
    <dimen name="compat_notification_large_icon_max_width">320dp</dimen>
    <item format="float" name="disabled_alpha_material_dark" type="dimen">0.30</item>
    <item format="float" name="disabled_alpha_material_light" type="dimen">0.26</item>
    <dimen name="fastscroll_default_thickness">8dp</dimen>
    <dimen name="fastscroll_margin">0dp</dimen>
    <dimen name="fastscroll_minimum_range">50dp</dimen>
    <item format="float" name="highlight_alpha_material_colored" type="dimen">0.26</item>
    <item format="float" name="highlight_alpha_material_dark" type="dimen">0.20</item>
    <item format="float" name="highlight_alpha_material_light" type="dimen">0.12</item>
    <item format="float" name="hint_alpha_material_dark" type="dimen">0.50</item>
    <item format="float" name="hint_alpha_material_light" type="dimen">0.38</item>
    <item format="float" name="hint_pressed_alpha_material_dark" type="dimen">0.70</item>
    <item format="float" name="hint_pressed_alpha_material_light" type="dimen">0.54</item>
    <dimen name="item_touch_helper_max_drag_scroll_per_frame">20dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_max_velocity">800dp</dimen>
    <dimen name="item_touch_helper_swipe_escape_velocity">120dp</dimen>
    <dimen name="lb_action_1_line_height">36dp</dimen>
    <dimen name="lb_action_2_lines_height">56dp</dimen>
    <dimen name="lb_action_button_corner_radius">2dp</dimen>
    <dimen name="lb_action_icon_margin">12dp</dimen>
    <dimen name="lb_action_padding_horizontal">24dp</dimen>
    <dimen name="lb_action_text_size">16sp</dimen>
    <dimen name="lb_action_with_icon_padding_end">20dp</dimen>
    <dimen name="lb_action_with_icon_padding_start">14dp</dimen>
    <dimen name="lb_basic_card_content_text_size">12sp</dimen>
    <dimen name="lb_basic_card_info_badge_margin">4dp</dimen>
    <dimen name="lb_basic_card_info_badge_size">16dp</dimen>
    <dimen name="lb_basic_card_info_height">52dp</dimen>
    <dimen name="lb_basic_card_info_height_no_content">34dp</dimen>
    <dimen name="lb_basic_card_info_padding_bottom">8dp</dimen>
    <dimen name="lb_basic_card_info_padding_horizontal">11dp</dimen>
    <dimen name="lb_basic_card_info_padding_top">7dp</dimen>
    <dimen name="lb_basic_card_info_text_margin">1dp</dimen>
    <dimen name="lb_basic_card_main_height">188dp</dimen>
    <dimen name="lb_basic_card_main_width">140dp</dimen>
    <dimen name="lb_basic_card_title_text_size">14sp</dimen>
    <dimen name="lb_browse_expanded_row_no_hovercard_bottom_padding">28dp</dimen>
    <dimen name="lb_browse_expanded_selected_row_top_padding">16dp</dimen>
    <dimen name="lb_browse_header_description_text_size">14sp</dimen>
    <dimen name="lb_browse_header_fading_length">12dp</dimen>
    <dimen name="lb_browse_header_height">24dp</dimen>
    <dimen name="lb_browse_header_padding_end">8dp</dimen>
    <item format="integer" name="lb_browse_header_select_duration" type="dimen">150</item>
    <item format="float" name="lb_browse_header_select_scale" type="dimen">1.2</item>
    <dimen name="lb_browse_header_text_size">20sp</dimen>
    <dimen name="lb_browse_headers_vertical_spacing">21dp</dimen>
    <dimen name="lb_browse_headers_width">270dp</dimen>
    <dimen name="lb_browse_headers_z">@dimen/lb_material_shadow_focused_z</dimen>
    <dimen name="lb_browse_item_horizontal_spacing">8dp</dimen>
    <dimen name="lb_browse_item_vertical_spacing">8dp</dimen>
    <dimen name="lb_browse_padding_bottom">48dp</dimen>
    <dimen name="lb_browse_padding_end">56dp</dimen>
    <dimen name="lb_browse_padding_start">56dp</dimen>
    <dimen name="lb_browse_padding_top">27dp</dimen>
    <dimen name="lb_browse_row_hovercard_description_font_size">14sp</dimen>
    <dimen name="lb_browse_row_hovercard_max_width">400dp</dimen>
    <dimen name="lb_browse_row_hovercard_title_font_size">18sp</dimen>
    <dimen name="lb_browse_rows_fading_edge">16dp</dimen>
    <dimen name="lb_browse_rows_margin_start">238dp</dimen>
    <dimen name="lb_browse_rows_margin_top">167dp</dimen>
    <dimen name="lb_browse_section_header_text_size">16sp</dimen>
    <dimen name="lb_browse_selected_row_top_padding">20dp</dimen>
    <dimen name="lb_browse_title_height">60dp</dimen>
    <dimen name="lb_browse_title_icon_height">60dp</dimen>
    <dimen name="lb_browse_title_icon_max_width">584dp</dimen>
    <dimen name="lb_browse_title_text_size">44sp</dimen>
    <dimen name="lb_control_button_diameter">90dp</dimen>
    <dimen name="lb_control_button_height">64dp</dimen>
    <dimen name="lb_control_button_secondary_diameter">48dp</dimen>
    <dimen name="lb_control_button_secondary_height">48dp</dimen>
    <dimen name="lb_control_button_text_size">22sp</dimen>
    <dimen name="lb_control_icon_height">32dp</dimen>
    <dimen name="lb_control_icon_width">32dp</dimen>
    <dimen name="lb_details_cover_drawable_parallax_movement">50dip</dimen>
    <dimen name="lb_details_description_body_line_spacing">20dp</dimen>
    <dimen name="lb_details_description_body_text_size">14sp</dimen>
    <dimen name="lb_details_description_subtitle_text_size">16sp</dimen>
    <dimen name="lb_details_description_title_baseline">26dp</dimen>
    <dimen name="lb_details_description_title_line_spacing">40dp</dimen>
    <dimen name="lb_details_description_title_padding_adjust_bottom">2dp</dimen>
    <dimen name="lb_details_description_title_padding_adjust_top">-1dp</dimen>
    <dimen name="lb_details_description_title_resized_text_size">28sp</dimen>
    <dimen name="lb_details_description_title_text_size">34sp</dimen>
    <dimen name="lb_details_description_under_subtitle_baseline_margin">32dp</dimen>
    <dimen name="lb_details_description_under_title_baseline_margin">32dp</dimen>
    <dimen name="lb_details_overview_action_items_spacing">16dp</dimen>
    <item format="integer" name="lb_details_overview_action_select_duration" type="dimen">150</item>
    <dimen name="lb_details_overview_actions_fade_size">16dp</dimen>
    <dimen name="lb_details_overview_actions_height">56dp</dimen>
    <dimen name="lb_details_overview_actions_padding_end">132dp</dimen>
    <dimen name="lb_details_overview_actions_padding_start">294dp</dimen>
    <dimen name="lb_details_overview_description_margin_bottom">12dp</dimen>
    <dimen name="lb_details_overview_description_margin_end">24dp</dimen>
    <dimen name="lb_details_overview_description_margin_start">24dp</dimen>
    <dimen name="lb_details_overview_description_margin_top">24dp</dimen>
    <dimen name="lb_details_overview_height_large">274dp</dimen>
    <dimen name="lb_details_overview_height_small">159dp</dimen>
    <dimen name="lb_details_overview_image_margin_horizontal">24dp</dimen>
    <dimen name="lb_details_overview_image_margin_vertical">24dp</dimen>
    <dimen name="lb_details_overview_margin_bottom">40dp</dimen>
    <dimen name="lb_details_overview_margin_end">132dp</dimen>
    <dimen name="lb_details_overview_margin_start">132dp</dimen>
    <dimen name="lb_details_overview_z">@dimen/lb_material_shadow_details_z</dimen>
    <dimen name="lb_details_rows_align_top">167dp</dimen>
    <dimen name="lb_details_v2_actions_height">56dip</dimen>
    <dimen name="lb_details_v2_align_pos_for_actions">270dp</dimen>
    <dimen name="lb_details_v2_align_pos_for_description">0dp</dimen>
    <dimen name="lb_details_v2_blank_height">160dp</dimen>
    <dimen name="lb_details_v2_card_height">540dp</dimen>
    <dimen name="lb_details_v2_description_margin_end">54dp</dimen>
    <dimen name="lb_details_v2_description_margin_start">24dp</dimen>
    <dimen name="lb_details_v2_description_margin_top">24dp</dimen>
    <dimen name="lb_details_v2_left">270dip</dimen>
    <dimen name="lb_details_v2_logo_margin_start">128dp</dimen>
    <dimen name="lb_details_v2_logo_max_height">210dp</dimen>
    <dimen name="lb_details_v2_logo_max_width">150dp</dimen>
    <dimen name="lb_error_image_max_height">120dp</dimen>
    <dimen name="lb_error_message_max_width">600dp</dimen>
    <dimen name="lb_error_message_text_size">16sp</dimen>
    <dimen name="lb_error_under_image_baseline_margin">36dp</dimen>
    <dimen name="lb_error_under_message_baseline_margin">24dp</dimen>
    <dimen name="lb_guidedactions_elevation">12dp</dimen>
    <dimen name="lb_guidedactions_item_bottom_padding">13dp</dimen>
    <dimen name="lb_guidedactions_item_checkmark_diameter">16dp</dimen>
    <dimen name="lb_guidedactions_item_delimiter_padding">4dp</dimen>
    <dimen name="lb_guidedactions_item_description_font_size">12sp</dimen>
    <item format="float" name="lb_guidedactions_item_disabled_chevron_alpha" type="dimen">0.50</item>
    <item format="float" name="lb_guidedactions_item_disabled_description_text_alpha" type="dimen">0.25</item>
    <item format="float" name="lb_guidedactions_item_disabled_text_alpha" type="dimen">0.25</item>
    <item format="float" name="lb_guidedactions_item_enabled_chevron_alpha" type="dimen">1.00</item>
    <dimen name="lb_guidedactions_item_end_padding">16dp</dimen>
    <dimen name="lb_guidedactions_item_icon_height">32dp</dimen>
    <dimen name="lb_guidedactions_item_icon_width">32dp</dimen>
    <dimen name="lb_guidedactions_item_space_between_title_and_description">2dp</dimen>
    <dimen name="lb_guidedactions_item_start_padding">16dp</dimen>
    <dimen name="lb_guidedactions_item_text_width">248dp</dimen>
    <dimen name="lb_guidedactions_item_text_width_no_icon">284dp</dimen>
    <dimen name="lb_guidedactions_item_title_font_size">14sp</dimen>
    <dimen name="lb_guidedactions_item_top_padding">14dp</dimen>
    <item format="float" name="lb_guidedactions_item_unselected_description_text_alpha" type="dimen">0.50</item>
    <item format="float" name="lb_guidedactions_item_unselected_text_alpha" type="dimen">1.00</item>
    <dimen name="lb_guidedactions_list_padding_end">24dp</dimen>
    <dimen name="lb_guidedactions_list_padding_start">24dp</dimen>
    <dimen name="lb_guidedactions_list_vertical_spacing">8dp</dimen>
    <dimen name="lb_guidedactions_section_shadow_width">32dp</dimen>
    <dimen name="lb_guidedactions_sublist_bottom_margin">28dp</dimen>
    <dimen name="lb_guidedactions_sublist_padding_bottom">8dip</dimen>
    <dimen name="lb_guidedactions_sublist_padding_top">8dip</dimen>
    <dimen name="lb_guidedactions_vertical_padding">14dp</dimen>
    <item format="float" name="lb_guidedactions_width_weight" type="dimen">0.71428571428</item>
    <item format="float" name="lb_guidedactions_width_weight_two_panels" type="dimen">1.191780822</item>
    <item format="float" name="lb_guidedbuttonactions_width_weight" type="dimen">0.45</item>
    <item format="float" name="lb_guidedstep_height_weight" type="dimen">2.0</item>
    <item format="float" name="lb_guidedstep_height_weight_translucent" type="dimen">1.0</item>
    <item format="float" name="lb_guidedstep_keyline" type="dimen">40.0</item>
    <dimen name="lb_guidedstep_slide_ime_distance">-100dp</dimen>
    <dimen name="lb_list_row_height">224dp</dimen>
    <dimen name="lb_material_shadow_details_z">8dp</dimen>
    <dimen name="lb_material_shadow_focused_z">10dp</dimen>
    <dimen name="lb_material_shadow_normal_z">0dp</dimen>
    <dimen name="lb_onboarding_content_margin_bottom">98dp</dimen>
    <dimen name="lb_onboarding_content_margin_top">164dp</dimen>
    <dimen name="lb_onboarding_content_width">536dp</dimen>
    <dimen name="lb_onboarding_header_height">100dp</dimen>
    <dimen name="lb_onboarding_header_margin_top">64dp</dimen>
    <dimen name="lb_onboarding_navigation_height">40dp</dimen>
    <dimen name="lb_onboarding_start_button_height">36dp</dimen>
    <dimen name="lb_onboarding_start_button_margin_bottom">62dp</dimen>
    <dimen name="lb_onboarding_start_button_translation_offset">16dp</dimen>
    <dimen name="lb_page_indicator_arrow_gap">32dp</dimen>
    <dimen name="lb_page_indicator_arrow_radius">18dp</dimen>
    <dimen name="lb_page_indicator_arrow_shadow_offset">1dp</dimen>
    <dimen name="lb_page_indicator_arrow_shadow_radius">2dp</dimen>
    <dimen name="lb_page_indicator_dot_gap">16dp</dimen>
    <dimen name="lb_page_indicator_dot_radius">5dp</dimen>
    <dimen name="lb_playback_controls_card_height">176dp</dimen>
    <dimen name="lb_playback_controls_child_margin_bigger">64dp</dimen>
    <dimen name="lb_playback_controls_child_margin_biggest">88dp</dimen>
    <dimen name="lb_playback_controls_child_margin_default">48dp</dimen>
    <dimen name="lb_playback_controls_margin_bottom">20dp</dimen>
    <dimen name="lb_playback_controls_margin_end">132dp</dimen>
    <dimen name="lb_playback_controls_margin_start">132dp</dimen>
    <dimen name="lb_playback_controls_padding_bottom">28dp</dimen>
    <dimen name="lb_playback_controls_time_text_size">12sp</dimen>
    <dimen name="lb_playback_controls_z">@dimen/lb_material_shadow_details_z</dimen>
    <dimen name="lb_playback_current_time_margin_start">16dp</dimen>
    <dimen name="lb_playback_description_margin_end">24dp</dimen>
    <dimen name="lb_playback_description_margin_start">24dp</dimen>
    <dimen name="lb_playback_description_margin_top">24dp</dimen>
    <dimen name="lb_playback_major_fade_translate_y">200dp</dimen>
    <dimen name="lb_playback_media_item_radio_icon_size">24dp</dimen>
    <dimen name="lb_playback_media_radio_width_with_padding">88dp</dimen>
    <dimen name="lb_playback_media_row_details_selector_width">668dp</dimen>
    <dimen name="lb_playback_media_row_horizontal_padding">32dp</dimen>
    <dimen name="lb_playback_media_row_radio_selector_width">72dp</dimen>
    <dimen name="lb_playback_media_row_selector_round_rect_radius">36dp</dimen>
    <dimen name="lb_playback_media_row_separator_height">1dp</dimen>
    <dimen name="lb_playback_minor_fade_translate_y">16dp</dimen>
    <dimen name="lb_playback_now_playing_bar_height">18dp</dimen>
    <dimen name="lb_playback_now_playing_bar_left_margin">3dp</dimen>
    <dimen name="lb_playback_now_playing_bar_margin">1dp</dimen>
    <dimen name="lb_playback_now_playing_bar_top_margin">3dp</dimen>
    <dimen name="lb_playback_now_playing_bar_width">5dp</dimen>
    <dimen name="lb_playback_now_playing_view_size">28dp</dimen>
    <dimen name="lb_playback_other_rows_center_to_bottom">270dp</dimen>
    <dimen name="lb_playback_play_icon_size">14dp</dimen>
    <dimen name="lb_playback_time_padding_top">8dp</dimen>
    <dimen name="lb_playback_total_time_margin_end">16dp</dimen>
    <dimen name="lb_playback_transport_control_info_margin_bottom">20dp</dimen>
    <dimen name="lb_playback_transport_control_row_padding_bottom">20dp</dimen>
    <dimen name="lb_playback_transport_controlbar_margin_start">-12dp</dimen>
    <dimen name="lb_playback_transport_hero_thumbs_height">192dp</dimen>
    <dimen name="lb_playback_transport_hero_thumbs_width">192dp</dimen>
    <dimen name="lb_playback_transport_image_height">176dp</dimen>
    <dimen name="lb_playback_transport_image_margin_end">24dp</dimen>
    <dimen name="lb_playback_transport_progressbar_active_bar_height">6dp</dimen>
    <dimen name="lb_playback_transport_progressbar_active_radius">6dp</dimen>
    <dimen name="lb_playback_transport_progressbar_bar_height">4dp</dimen>
    <dimen name="lb_playback_transport_progressbar_height">28dp</dimen>
    <dimen name="lb_playback_transport_thumbs_bottom_margin">18dp</dimen>
    <dimen name="lb_playback_transport_thumbs_height">154dp</dimen>
    <dimen name="lb_playback_transport_thumbs_margin">4dp</dimen>
    <dimen name="lb_playback_transport_thumbs_width">154dp</dimen>
    <dimen name="lb_playback_transport_time_margin">8dp</dimen>
    <dimen name="lb_playback_transport_time_margin_top">8dp</dimen>
    <dimen name="lb_rounded_rect_corner_radius">2dp</dimen>
    <dimen name="lb_search_bar_edit_text_margin_start">24dp</dimen>
    <dimen name="lb_search_bar_height">60dp</dimen>
    <dimen name="lb_search_bar_hint_margin_start">52dp</dimen>
    <dimen name="lb_search_bar_icon_height">32dp</dimen>
    <dimen name="lb_search_bar_icon_margin_start">16dp</dimen>
    <dimen name="lb_search_bar_icon_width">32dp</dimen>
    <dimen name="lb_search_bar_inner_margin_bottom">2dp</dimen>
    <dimen name="lb_search_bar_inner_margin_top">2dp</dimen>
    <dimen name="lb_search_bar_items_height">56dp</dimen>
    <dimen name="lb_search_bar_items_layout_margin_top">27dp</dimen>
    <dimen name="lb_search_bar_items_margin_start">70dp</dimen>
    <dimen name="lb_search_bar_items_width">600dp</dimen>
    <dimen name="lb_search_bar_padding_start">56dp</dimen>
    <dimen name="lb_search_bar_padding_top">27dp</dimen>
    <dimen name="lb_search_bar_speech_orb_margin_start">56dp</dimen>
    <dimen name="lb_search_bar_speech_orb_size">52dp</dimen>
    <dimen name="lb_search_bar_text_size">18sp</dimen>
    <dimen name="lb_search_bar_unfocused_text_size">18sp</dimen>
    <dimen name="lb_search_browse_row_padding_start">56dp</dimen>
    <dimen name="lb_search_browse_rows_align_top">147dp</dimen>
    <dimen name="lb_search_orb_focused_z">8dp</dimen>
    <dimen name="lb_search_orb_margin_bottom">4dp</dimen>
    <dimen name="lb_search_orb_margin_end">4dp</dimen>
    <dimen name="lb_search_orb_margin_start">4dp</dimen>
    <dimen name="lb_search_orb_margin_top">4dp</dimen>
    <dimen name="lb_search_orb_size">52dp</dimen>
    <dimen name="lb_search_orb_unfocused_z">2dp</dimen>
    <dimen name="lb_vertical_grid_padding_bottom">87dp</dimen>
    <dimen name="notification_action_icon_size">32dp</dimen>
    <dimen name="notification_action_text_size">13sp</dimen>
    <dimen name="notification_big_circle_margin">12dp</dimen>
    <dimen name="notification_content_margin_start">8dp</dimen>
    <dimen name="notification_large_icon_height">64dp</dimen>
    <dimen name="notification_large_icon_width">64dp</dimen>
    <dimen name="notification_main_column_padding_top">10dp</dimen>
    <dimen name="notification_media_narrow_margin">@dimen/notification_content_margin_start</dimen>
    <dimen name="notification_right_icon_size">16dp</dimen>
    <dimen name="notification_right_side_padding_top">4dp</dimen>
    <dimen name="notification_small_icon_background_padding">3dp</dimen>
    <dimen name="notification_small_icon_size_as_large">24dp</dimen>
    <dimen name="notification_subtext_size">13sp</dimen>
    <dimen name="notification_top_pad">10dp</dimen>
    <dimen name="notification_top_pad_large_text">5dp</dimen>
    <dimen name="picker_column_horizontal_padding">8dp</dimen>
    <dimen name="picker_item_height">32dp</dimen>
    <dimen name="picker_item_spacing">32dp</dimen>
    <dimen name="picker_separator_horizontal_padding">4dp</dimen>
    <dimen name="pinpicker_text_size">24sp</dimen>
    <dimen name="subtitle_corner_radius">2dp</dimen>
    <dimen name="subtitle_outline_width">2dp</dimen>
    <dimen name="subtitle_shadow_offset">2dp</dimen>
    <dimen name="subtitle_shadow_radius">2dp</dimen>
    <dimen name="tooltip_corner_radius">2dp</dimen>
    <dimen name="tooltip_horizontal_padding">16dp</dimen>
    <dimen name="tooltip_margin">8dp</dimen>
    <dimen name="tooltip_precise_anchor_extra_offset">8dp</dimen>
    <dimen name="tooltip_precise_anchor_threshold">96dp</dimen>
    <dimen name="tooltip_vertical_padding">6.5dp</dimen>
    <dimen name="tooltip_y_offset_non_touch">0dp</dimen>
    <dimen name="tooltip_y_offset_touch">16dp</dimen>
    <drawable name="notification_template_icon_bg">#3333B5E5</drawable>
    <drawable name="notification_template_icon_low_bg">#0cffffff</drawable>
    <item name="lb_browse_header_unselect_alpha" type="fraction">50%</item>
    <item name="lb_browse_rows_scale" type="fraction">80%</item>
    <item name="lb_focus_zoom_factor_large" type="fraction">118%</item>
    <item name="lb_focus_zoom_factor_medium" type="fraction">114%</item>
    <item name="lb_focus_zoom_factor_small" type="fraction">110%</item>
    <item name="lb_focus_zoom_factor_xsmall" type="fraction">106%</item>
    <item name="lb_search_bar_speech_orb_max_level_zoom" type="fraction">144%</item>
    <item name="lb_search_orb_focused_zoom" type="fraction">120%</item>
    <item name="lb_view_active_level" type="fraction">0%</item>
    <item name="lb_view_dimmed_level" type="fraction">60%</item>
    <item name="accessibility_action_clickable_span" type="id"/>
    <item name="accessibility_custom_action_0" type="id"/>
    <item name="accessibility_custom_action_1" type="id"/>
    <item name="accessibility_custom_action_10" type="id"/>
    <item name="accessibility_custom_action_11" type="id"/>
    <item name="accessibility_custom_action_12" type="id"/>
    <item name="accessibility_custom_action_13" type="id"/>
    <item name="accessibility_custom_action_14" type="id"/>
    <item name="accessibility_custom_action_15" type="id"/>
    <item name="accessibility_custom_action_16" type="id"/>
    <item name="accessibility_custom_action_17" type="id"/>
    <item name="accessibility_custom_action_18" type="id"/>
    <item name="accessibility_custom_action_19" type="id"/>
    <item name="accessibility_custom_action_2" type="id"/>
    <item name="accessibility_custom_action_20" type="id"/>
    <item name="accessibility_custom_action_21" type="id"/>
    <item name="accessibility_custom_action_22" type="id"/>
    <item name="accessibility_custom_action_23" type="id"/>
    <item name="accessibility_custom_action_24" type="id"/>
    <item name="accessibility_custom_action_25" type="id"/>
    <item name="accessibility_custom_action_26" type="id"/>
    <item name="accessibility_custom_action_27" type="id"/>
    <item name="accessibility_custom_action_28" type="id"/>
    <item name="accessibility_custom_action_29" type="id"/>
    <item name="accessibility_custom_action_3" type="id"/>
    <item name="accessibility_custom_action_30" type="id"/>
    <item name="accessibility_custom_action_31" type="id"/>
    <item name="accessibility_custom_action_4" type="id"/>
    <item name="accessibility_custom_action_5" type="id"/>
    <item name="accessibility_custom_action_6" type="id"/>
    <item name="accessibility_custom_action_7" type="id"/>
    <item name="accessibility_custom_action_8" type="id"/>
    <item name="accessibility_custom_action_9" type="id"/>
    <item name="action_bar_activity_content" type="id"/>
    <item name="action_bar_spinner" type="id"/>
    <item name="action_menu_divider" type="id"/>
    <item name="action_menu_presenter" type="id"/>
    <item name="androidx_compose_ui_view_composition_context" type="id"/>
    <item name="compose_view_saveable_id_tag" type="id"/>
    <item name="consume_window_insets_tag" type="id"/>
    <item name="hide_graphics_layer_in_inspector_tag" type="id"/>
    <item name="hide_in_inspector_tag" type="id"/>
    <item name="home" type="id"/>
    <item name="inspection_slot_table_set" type="id"/>
    <item name="is_pooling_container_tag" type="id"/>
    <item name="item_touch_helper_previous_elevation" type="id"/>
    <item name="lb_control_closed_captioning" type="id"/>
    <item name="lb_control_fast_forward" type="id"/>
    <item name="lb_control_fast_rewind" type="id"/>
    <item name="lb_control_high_quality" type="id"/>
    <item name="lb_control_more_actions" type="id"/>
    <item name="lb_control_picture_in_picture" type="id"/>
    <item name="lb_control_play_pause" type="id"/>
    <item name="lb_control_repeat" type="id"/>
    <item name="lb_control_shuffle" type="id"/>
    <item name="lb_control_skip_next" type="id"/>
    <item name="lb_control_skip_previous" type="id"/>
    <item name="lb_control_thumbs_down" type="id"/>
    <item name="lb_control_thumbs_up" type="id"/>
    <item name="lb_focus_animator" type="id"/>
    <item name="lb_guidedstep_background" type="id"/>
    <item name="lb_parallax_source" type="id"/>
    <item name="lb_shadow_impl" type="id"/>
    <item name="lb_slide_transition_value" type="id"/>
    <item name="line1" type="id"/>
    <item name="line3" type="id"/>
    <item name="nav_controller_view_tag" type="id"/>
    <item name="pooling_container_listener_holder_tag" type="id"/>
    <item name="progress_circular" type="id"/>
    <item name="progress_horizontal" type="id"/>
    <item name="report_drawn" type="id"/>
    <item name="split_action_bar" type="id"/>
    <item name="tag_accessibility_actions" type="id"/>
    <item name="tag_accessibility_clickable_spans" type="id"/>
    <item name="tag_accessibility_heading" type="id"/>
    <item name="tag_accessibility_pane_title" type="id"/>
    <item name="tag_compat_insets_dispatch" type="id"/>
    <item name="tag_on_apply_window_listener" type="id"/>
    <item name="tag_on_receive_content_listener" type="id"/>
    <item name="tag_on_receive_content_mime_types" type="id"/>
    <item name="tag_screen_reader_focusable" type="id"/>
    <item name="tag_state_description" type="id"/>
    <item name="tag_system_bar_state_monitor" type="id"/>
    <item name="tag_transition_group" type="id"/>
    <item name="tag_unhandled_key_event_manager" type="id"/>
    <item name="tag_unhandled_key_listeners" type="id"/>
    <item name="tag_window_insets_animation_callback" type="id"/>
    <item name="text" type="id"/>
    <item name="text2" type="id"/>
    <item name="title" type="id"/>
    <item name="transitionPosition" type="id"/>
    <item name="up" type="id"/>
    <id name="view_tree_disjoint_parent"/>
    <id name="view_tree_lifecycle_owner"/>
    <id name="view_tree_on_back_pressed_dispatcher_owner"/>
    <id name="view_tree_saved_state_registry_owner"/>
    <id name="view_tree_view_model_store_owner"/>
    <item name="wrapped_composition_tag" type="id"/>
    <integer name="abc_config_activityDefaultDur">220</integer>
    <integer name="abc_config_activityShortDur">150</integer>
    <integer name="cancel_button_image_alpha">127</integer>
    <integer name="config_tooltipAnimTime">150</integer>
    <integer name="lb_browse_headers_transition_delay">150</integer>
    <integer name="lb_browse_headers_transition_duration">250</integer>
    <integer name="lb_browse_rows_anim_duration">250</integer>
    <integer name="lb_card_activated_animation_duration">150</integer>
    <integer name="lb_card_selected_animation_delay">400</integer>
    <integer name="lb_card_selected_animation_duration">150</integer>
    <integer name="lb_details_description_body_max_lines">5</integer>
    <integer name="lb_details_description_body_min_lines">3</integer>
    <integer name="lb_details_description_subtitle_max_lines">1</integer>
    <integer name="lb_details_description_title_max_lines">2</integer>
    <integer name="lb_error_message_max_lines">3</integer>
    <integer name="lb_guidedactions_item_animation_duration">100</integer>
    <integer name="lb_guidedactions_item_description_min_lines">2</integer>
    <integer name="lb_guidedactions_item_title_max_lines">3</integer>
    <integer name="lb_guidedactions_item_title_min_lines">1</integer>
    <integer name="lb_guidedstep_activity_background_fade_duration_ms">350</integer>
    <integer name="lb_onboarding_header_description_delay">33</integer>
    <integer name="lb_onboarding_header_title_delay">33</integer>
    <integer name="lb_playback_bg_fade_in_ms">325</integer>
    <integer name="lb_playback_bg_fade_out_ms">500</integer>
    <integer name="lb_playback_controls_fade_in_ms">250</integer>
    <integer name="lb_playback_controls_fade_out_ms">325</integer>
    <integer name="lb_playback_controls_show_time_ms">3000</integer>
    <integer name="lb_playback_controls_tickle_timeout_ms">0</integer>
    <integer name="lb_playback_description_fade_in_ms">250</integer>
    <integer name="lb_playback_description_fade_out_ms">200</integer>
    <integer name="lb_playback_rows_fade_delay_ms">100</integer>
    <integer name="lb_playback_rows_fade_in_ms">150</integer>
    <integer name="lb_playback_rows_fade_out_ms">250</integer>
    <integer name="lb_search_bar_speech_mode_background_alpha">179</integer>
    <integer name="lb_search_bar_text_mode_background_alpha">51</integer>
    <item name="lb_search_orb_pulse_duration_ms" type="integer">1000</item>
    <item name="lb_search_orb_scale_duration_ms" type="integer">150</item>
    <integer name="m3c_window_layout_in_display_cutout_mode">1</integer>
    <integer name="slideEdgeEnd">5</integer>
    <integer name="slideEdgeStart">3</integer>
    <integer name="status_bar_notification_info_maxnum">999</integer>
    <string name="abc_action_bar_home_description">Navigate home</string>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_menu_overflow_description">More options</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_activity_chooser_view_see_all">See all</string>
    <string name="abc_activitychooserview_choose_application">Choose an app</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_font_family_body_1_material">sans-serif</string>
    <string name="abc_font_family_body_2_material">sans-serif-medium</string>
    <string name="abc_font_family_button_material">sans-serif-medium</string>
    <string name="abc_font_family_caption_material">sans-serif</string>
    <string name="abc_font_family_display_1_material">sans-serif</string>
    <string name="abc_font_family_display_2_material">sans-serif</string>
    <string name="abc_font_family_display_3_material">sans-serif</string>
    <string name="abc_font_family_display_4_material">sans-serif-light</string>
    <string name="abc_font_family_headline_material">sans-serif</string>
    <string name="abc_font_family_menu_material">sans-serif</string>
    <string name="abc_font_family_subhead_material">sans-serif</string>
    <string name="abc_font_family_title_material">sans-serif-medium</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_query">Search query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_shareactionprovider_share_with">Share with</string>
    <string name="abc_shareactionprovider_share_with_application">Share with <ns1:g example="Mail" id="application_name">%s</ns1:g></string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="androidx_startup" translatable="false">androidx.startup</string>
    <string name="app_name">荔枝TV</string>
    <string name="autofill">Autofill</string>
    <string name="call_notification_answer_action">Answer</string>
    <string name="call_notification_answer_video_action">Video</string>
    <string name="call_notification_decline_action">Decline</string>
    <string name="call_notification_hang_up_action">Hang Up</string>
    <string name="call_notification_incoming_text">Incoming call</string>
    <string name="call_notification_ongoing_text">Ongoing call</string>
    <string name="call_notification_screening_text">Screening an incoming call</string>
    <string name="close_drawer">"Close navigation menu"</string>
    <string name="close_sheet">"Close sheet"</string>
    <string name="default_error_message">"Invalid input"</string>
    <string name="default_popup_window_title" ns2:ignore="ExtraTranslation">"Pop-Up Window"</string>
    <string name="dropdown_menu">"Dropdown menu"</string>
    <string name="in_progress">In progress</string>
    <string name="indeterminate">Partially checked</string>
    <string name="lb_control_display_fast_forward_multiplier">%1$dX</string>
    <string name="lb_control_display_rewind_multiplier">%1$dX</string>
    <string name="lb_guidedaction_continue_title">Continue</string>
    <string name="lb_guidedaction_finish_title">Finish</string>
    <string name="lb_media_player_error">MediaPlayer error code %1$d extra %2$d</string>
    <string name="lb_navigation_menu_contentDescription">Navigation menu</string>
    <string name="lb_onboarding_accessibility_next">Next</string>
    <string name="lb_onboarding_get_started">GET STARTED</string>
    <string name="lb_playback_controls_closed_captioning_disable">Disable Closed Captioning</string>
    <string name="lb_playback_controls_closed_captioning_enable">Enable Closed Captioning</string>
    <string name="lb_playback_controls_fast_forward">Fast Forward</string>
    <string name="lb_playback_controls_fast_forward_multiplier">Fast Forward %1$dX</string>
    <string name="lb_playback_controls_hidden">Media controls hidden, press d-pad to show</string>
    <string name="lb_playback_controls_high_quality_disable">Disable High Quality</string>
    <string name="lb_playback_controls_high_quality_enable">Enable High Quality</string>
    <string name="lb_playback_controls_more_actions">More Actions</string>
    <string name="lb_playback_controls_pause">Pause</string>
    <string name="lb_playback_controls_picture_in_picture">Enter Picture In Picture Mode</string>
    <string name="lb_playback_controls_play">Play</string>
    <string name="lb_playback_controls_repeat_all">Repeat All</string>
    <string name="lb_playback_controls_repeat_none">Repeat None</string>
    <string name="lb_playback_controls_repeat_one">Repeat One</string>
    <string name="lb_playback_controls_rewind">Rewind</string>
    <string name="lb_playback_controls_rewind_multiplier">Rewind %1$dX</string>
    <string name="lb_playback_controls_shown">Media controls shown</string>
    <string name="lb_playback_controls_shuffle_disable">Disable Shuffle</string>
    <string name="lb_playback_controls_shuffle_enable">Enable Shuffle</string>
    <string name="lb_playback_controls_skip_next">Skip Next</string>
    <string name="lb_playback_controls_skip_previous">Skip Previous</string>
    <string name="lb_playback_controls_thumb_down">Deselect Thumb Down</string>
    <string name="lb_playback_controls_thumb_down_outline">Select Thumb Down</string>
    <string name="lb_playback_controls_thumb_up">Deselect Thumb Up</string>
    <string name="lb_playback_controls_thumb_up_outline">Select Thumb Up</string>
    <string name="lb_playback_time_separator">/</string>
    <string name="lb_search_bar_hint">Search</string>
    <string name="lb_search_bar_hint_speech">Speak to search</string>
    <string name="lb_search_bar_hint_with_title">Search <ns1:g id="search context">%1$s</ns1:g></string>
    <string name="lb_search_bar_hint_with_title_speech">Speak to search <ns1:g id="search context">%1$s</ns1:g></string>
    <string name="m3c_bottom_sheet_collapse_description">Collapse bottom sheet</string>
    <string name="m3c_bottom_sheet_dismiss_description">Dismiss bottom sheet</string>
    <string name="m3c_bottom_sheet_drag_handle_description">Drag handle</string>
    <string name="m3c_bottom_sheet_expand_description">Expand bottom sheet</string>
    <string name="m3c_bottom_sheet_pane_title">Bottom Sheet</string>
    <string name="m3c_date_input_headline">Entered date</string>
    <string name="m3c_date_input_headline_description">Entered date: %1$s</string>
    <string name="m3c_date_input_invalid_for_pattern">Date does not match expected pattern: %1$s</string>
    <string name="m3c_date_input_invalid_not_allowed">Date not allowed: %1$s</string>
    <string name="m3c_date_input_invalid_year_range">
        Date out of expected year range %1$s - %2$s
    </string>
    <string name="m3c_date_input_label">Date</string>
    <string name="m3c_date_input_no_input_description">None</string>
    <string name="m3c_date_input_title">Select date</string>
    <string name="m3c_date_picker_headline">"Selected date"</string>
    <string name="m3c_date_picker_headline_description">Current selection: %1$s</string>
    <string name="m3c_date_picker_navigate_to_year_description">Navigate to year %1$s</string>
    <string name="m3c_date_picker_no_selection_description">None</string>
    <string name="m3c_date_picker_scroll_to_earlier_years">Scroll to show earlier years</string>
    <string name="m3c_date_picker_scroll_to_later_years">Scroll to show later years</string>
    <string name="m3c_date_picker_switch_to_calendar_mode">Switch to calendar input mode</string>
    <string name="m3c_date_picker_switch_to_day_selection">
        "Swipe to select a year, or tap to switch back to selecting a day"
    </string>
    <string name="m3c_date_picker_switch_to_input_mode">Switch to text input mode</string>
    <string name="m3c_date_picker_switch_to_next_month">"Change to next month"</string>
    <string name="m3c_date_picker_switch_to_previous_month">"Change to previous month"</string>
    <string name="m3c_date_picker_switch_to_year_selection">"Switch to selecting a year"</string>
    <string name="m3c_date_picker_title">"Select date"</string>
    <string name="m3c_date_picker_today_description">Today</string>
    <string name="m3c_date_picker_year_picker_pane_title">Year picker visible</string>
    <string name="m3c_date_range_input_invalid_range_input">Invalid date range input</string>
    <string name="m3c_date_range_input_title">Enter dates</string>
    <string name="m3c_date_range_picker_day_in_range">In range</string>
    <string name="m3c_date_range_picker_end_headline">End date</string>
    <string name="m3c_date_range_picker_scroll_to_next_month">Scroll to show the next month</string>
    <string name="m3c_date_range_picker_scroll_to_previous_month">Scroll to show the previous month</string>
    <string name="m3c_date_range_picker_start_headline">Start date</string>
    <string name="m3c_date_range_picker_title">Select dates</string>
    <string name="m3c_dialog">"Dialog"</string>
    <string name="m3c_dropdown_menu_collapsed">Collapsed</string>
    <string name="m3c_dropdown_menu_expanded">Expanded</string>
    <string name="m3c_dropdown_menu_toggle">Toggle dropdown menu</string>
    <string name="m3c_search_bar_search">Search</string>
    <string name="m3c_snackbar_dismiss">Dismiss</string>
    <string name="m3c_suggestions_available">Suggestions below</string>
    <string name="m3c_time_picker_am">AM</string>
    <string name="m3c_time_picker_hour">Hour</string>
    <string name="m3c_time_picker_hour_24h_suffix">%1$d hours</string>
    <string name="m3c_time_picker_hour_selection">Select hour</string>
    <string name="m3c_time_picker_hour_suffix">%1$d o\'clock</string>
    <string name="m3c_time_picker_hour_text_field">for hour</string>
    <string name="m3c_time_picker_minute">Minute</string>
    <string name="m3c_time_picker_minute_selection">Select minutes</string>
    <string name="m3c_time_picker_minute_suffix">%1$d minutes</string>
    <string name="m3c_time_picker_minute_text_field">for minutes</string>
    <string name="m3c_time_picker_period_toggle_description">Select AM or PM</string>
    <string name="m3c_time_picker_pm">PM</string>
    <string name="m3c_tooltip_long_press_label">Show tooltip</string>
    <string name="m3c_tooltip_pane_description">Tooltip</string>
    <string name="mc2_snackbar_pane_title">Alert</string>
    <string name="menu_toggle">菜单切换</string>
    <string name="nav_favorites">收藏</string>
    <string name="nav_home">主页</string>
    <string name="nav_live">直播</string>
    <string name="nav_movies">电影</string>
    <string name="nav_search">搜索</string>
    <string name="nav_settings">设置</string>
    <string name="nav_tv_shows">电视剧</string>
    <string name="navigation_drawer">导航抽屉</string>
    <string name="navigation_menu">"Navigation menu"</string>
    <string name="not_selected">Not selected</string>
    <string name="orb_search_action">Search Action</string>
    <string name="range_end">"Range end"</string>
    <string name="range_start">"Range start"</string>
    <string name="search_menu_title">Search</string>
    <string name="selected">Selected</string>
    <string name="snackbar_pane_title">Alert</string>
    <string name="state_empty">Empty</string>
    <string name="state_off">Off</string>
    <string name="state_on">On</string>
    <string name="status_bar_notification_info_overflow">999+</string>
    <string name="switch_role">Switch</string>
    <string name="tab">Tab</string>
    <string name="template_percent"><ns1:g id="percentage">%1$d</ns1:g> percent.</string>
    <string name="tooltip_description">tooltip</string>
    <string name="tooltip_label">show tooltip</string>
    <style name="AlertDialog.AppCompat" parent="Base.AlertDialog.AppCompat"/>
    <style name="AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat.Light"/>
    <style name="Animation.AppCompat.Dialog" parent="Base.Animation.AppCompat.Dialog"/>
    <style name="Animation.AppCompat.DropDownUp" parent="Base.Animation.AppCompat.DropDownUp"/>
    <style name="Animation.AppCompat.Tooltip" parent="Base.Animation.AppCompat.Tooltip"/>
    <style name="Base.AlertDialog.AppCompat" parent="android:Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="Base.AlertDialog.AppCompat"/>
    <style name="Base.Animation.AppCompat.Dialog" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.Animation.AppCompat.Tooltip" parent="android:Animation">
        <item name="android:windowEnterAnimation">@anim/abc_tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_tooltip_exit</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="android:Widget">
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="android:Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat" parent="android:TextAppearance">
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
        <item name="android:textColorHighlight">?android:textColorHighlight</item>
        <item name="android:textColorLink">?android:textColorLink</item>
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1">
        <item name="android:textSize">@dimen/abc_text_size_body_1_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2">
        <item name="android:textSize">@dimen/abc_text_size_body_2_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/abc_text_size_button_material</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption">
        <item name="android:textSize">@dimen/abc_text_size_caption_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1">
        <item name="android:textSize">@dimen/abc_text_size_display_1_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2">
        <item name="android:textSize">@dimen/abc_text_size_display_2_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3">
        <item name="android:textSize">@dimen/abc_text_size_display_3_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4">
        <item name="android:textSize">@dimen/abc_text_size_display_4_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline">
        <item name="android:textSize">@dimen/abc_text_size_headline_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large">
        <item name="android:textSize">@dimen/abc_text_size_large_material</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Large.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium">
        <item name="android:textSize">@dimen/abc_text_size_medium_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse">
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Menu">
        <item name="android:textSize">@dimen/abc_text_size_menu_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">?android:textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title">
        <item name="android:textSize">18sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small">
        <item name="android:textSize">@dimen/abc_text_size_small_material</item>
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Small.Inverse">
        <item name="android:textColor">?android:attr/textColorTertiaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subhead_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
        <item name="android:textColorHint">?android:attr/textColorHintInverse</item>
        <item name="android:textColorHighlight">?android:attr/textColorHighlightInverse</item>
        <item name="android:textColorLink">?android:attr/textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Tooltip">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?attr/actionMenuTextColor</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="TextAppearance.AppCompat.Subhead.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_subtitle_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorSecondaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="TextAppearance.AppCompat.Title">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="TextAppearance.AppCompat.Title.Inverse">
        <item name="android:textSize">@dimen/abc_text_size_title_material_toolbar</item>
        <item name="android:textColor">?android:attr/textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored">
        <item name="android:textColor">@color/abc_btn_colored_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="TextAppearance.AppCompat.Button">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="android:TextAppearance.Small">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?attr/colorAccent</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="TextAppearance.AppCompat.Button"/>
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="TextAppearance.AppCompat.Menu"/>
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="android:TextAppearance.Medium">
        <item name="android:textColor">?android:attr/textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="Base.Theme.AppCompat" parent="Base.V7.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="Base.V7.Theme.AppCompat.Dialog"/>
    <style name="Base.Theme.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="Theme.AppCompat"/>
    <style name="Base.Theme.AppCompat.Light" parent="Base.V7.Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>

        
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="Base.V7.Theme.AppCompat.Light.Dialog"/>
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize">
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="Theme.AppCompat.Light"/>
    <style name="Base.ThemeOverlay.AppCompat" parent="Platform.ThemeOverlay.AppCompat"/>
    <style name="Base.ThemeOverlay.AppCompat.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>

        
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="Base.V7.ThemeOverlay.AppCompat.Dialog"/>
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>

        
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat" parent="Platform.AppCompat">
        <item name="viewInflaterClass">androidx.appcompat.app.AppCompatViewInflater</item>
        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>

        
        <item name="isLightTheme">false</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>

        
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>

        
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">64dp</item>
        <item name="listPreferredItemHeightSmall">48dp</item>
        <item name="listPreferredItemHeightLarge">80dp</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>

        
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>

        
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorAccent">@color/accent_material_dark</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="listDividerAlertDialog">@null</item>

        
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>

        <item name="colorError">@color/error_color_material_dark</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="Platform.AppCompat.Light">
        <item name="viewInflaterClass">androidx.appcompat.app.AppCompatViewInflater</item>

        <item name="windowNoTitle">false</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="actionBarPopupTheme">@null</item>

        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>

        
        <item name="isLightTheme">true</item>

        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>

        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>

        
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>

        
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>

        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>

        
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>

        
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>

        
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="listPreferredItemHeight">64dp</item>
        <item name="listPreferredItemHeightSmall">48dp</item>
        <item name="listPreferredItemHeightLarge">80dp</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>

        
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>

        
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>

        
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>

        
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>

        
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>

        
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>

        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>

        
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorAccent">@color/accent_material_light</item>

        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>

        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>

        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>

        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>

        
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>

        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>

        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>

        
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>

        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="listDividerAlertDialog">@null</item>

        
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>

        <item name="colorError">@color/error_color_material_light</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>

        <item name="android:windowFrame">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>

        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>

        <item name="listPreferredItemPaddingLeft">24dip</item>
        <item name="listPreferredItemPaddingRight">24dip</item>

        <item name="android:listDivider">@null</item>

        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>

        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="android:Widget.AutoCompleteTextView">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.EditText" parent="android:Widget.EditText">
        <item name="android:background">?attr/editTextBackground</item>
        <item name="android:textColor">?attr/editTextColor</item>
        <item name="android:textAppearance">?android:attr/textAppearanceMediumInverse</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar" parent="android:Widget">
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="titleMargin">4dp</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="buttonGravity">top</item>
        <item name="collapseIcon">?attr/homeAsUpIndicator</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="displayOptions">showTitle</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="height">?attr/actionBarSize</item>

        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>

        <item name="background">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="backgroundSplit">@null</item>

        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>

        <item name="android:gravity">center_vertical</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="popupTheme">?attr/actionBarPopupTheme</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?attr/actionBarDivider</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">8dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:ellipsize">marquee</item>
        <item name="android:maxLines">2</item>
        <item name="android:maxWidth">180dp</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
        <item name="android:layout_width">0dip</item>
        <item name="android:layout_weight">1</item>
        <item name="android:minWidth">80dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton" parent="RtlUnderlay.Widget.AppCompat.ActionButton">
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
        <item name="android:scaleType">center</item>
        <item name="android:gravity">center</item>
        <item name="android:maxLines">2</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow">
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:contentDescription">@string/abc_action_menu_overflow_description</item>
        <item name="android:minWidth">@dimen/abc_action_button_min_width_overflow_material</item>
        <item name="android:minHeight">@dimen/abc_action_button_min_height_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?attr/actionModeBackground</item>
        <item name="backgroundSplit">?attr/actionModeSplitBackground</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="showDividers">middle</item>
        <item name="dividerPadding">6dip</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="Base.V7.Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Base.Widget.AppCompat.Button" parent="android:Widget">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
        <item name="android:textAppearance">?android:attr/textAppearanceButton</item>
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">88dip</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">center_vertical|center_horizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless">
        <item name="android:background">@drawable/abc_btn_borderless_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored">
        <item name="android:background">@drawable/abc_btn_colored_material</item>
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small">
        <item name="android:minHeight">48dip</item>
        <item name="android:minWidth">48dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar" parent="android:Widget">
        <item name="android:background">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="android:Widget.CompoundButton.CheckBox">
        <item name="android:button">?android:attr/listChoiceIndicatorMultiple</item>
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="android:Widget.CompoundButton.RadioButton">
        <item name="android:button">?android:attr/listChoiceIndicatorSingle</item>
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="android:Widget.CompoundButton">
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="android:background">?attr/controlBackground</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18dp</item>
        <item name="gapBetweenBars">3dp</item>
        <item name="drawableSize">24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="color">?android:attr/textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2dp</item>
        <item name="arrowShaftLength">16dp</item>
        <item name="arrowHeadLength">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.DropDownItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="Base.Widget.AppCompat.EditText" parent="Base.V7.Widget.AppCompat.EditText"/>
    <style name="Base.Widget.AppCompat.ImageButton" parent="android:Widget.ImageButton">
        <item name="android:background">@drawable/abc_btn_default_mtrl_shape</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Medium.Inverse</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
        <item name="android:background">@drawable/abc_tab_indicator_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView" parent="android:Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="">
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView" parent="android:Widget.ListView">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown">
        <item name="android:divider">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="android:Widget.ListView.Menu">
        <item name="android:listSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:divider">?attr/dividerHorizontal</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@style/Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow">
        <item name="overlapAnchor">true</item>
        <item name="android:dropDownHorizontalOffset">-4dip</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="android:Widget.PopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar" parent="android:Widget.Holo.ProgressBar">
    </style>
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="android:Widget.Holo.ProgressBar.Horizontal">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_material</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:maxHeight">36dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="android:Widget.RatingBar">
        <item name="android:progressDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:minHeight">16dp</item>
        <item name="android:maxHeight">16dp</item>
        <item name="android:isIndicator">true</item>
        <item name="android:thumb">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="android:Widget">
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar">
        <item name="queryBackground">@null</item>
        <item name="submitBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="defaultQueryHint">@string/abc_search_hint</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="android:Widget">
        <item name="android:indeterminateOnly">false</item>
        <item name="android:progressDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:indeterminateDrawable">@drawable/abc_seekbar_track_material</item>
        <item name="android:thumb">@drawable/abc_seekbar_thumb_material</item>
        <item name="android:focusable">true</item>
        <item name="android:paddingLeft">16dip</item>
        <item name="android:paddingRight">16dip</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar.Discrete">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="Platform.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_mtrl_am_alpha</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:dropDownSelector">?attr/listChoiceBackgroundIndicator</item>
        <item name="android:dropDownVerticalOffset">0dip</item>
        <item name="android:dropDownHorizontalOffset">0dip</item>
        <item name="android:dropDownWidth">wrap_content</item>
        <item name="android:clickable">true</item>
        <item name="android:gravity">left|start|center_vertical</item>
        <item name="overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner.Underlined">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="android:Widget.TextView.SpinnerItem">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="Base.Widget.AppCompat.Toolbar" parent="Base.V7.Widget.AppCompat.Toolbar"/>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="android:Widget">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:minWidth">56dp</item>
        <item name="android:scaleType">center</item>
    </style>
    <style name="DialogWindowTheme">
        <item name="android:windowClipToOutline">false</item>
    </style>
    <style name="EdgeToEdgeFloatingDialogTheme" parent="android:Theme.DeviceDefault.Dialog">
        <item name="android:windowLayoutInDisplayCutoutMode" ns2:targetApi="27">@integer/m3c_window_layout_in_display_cutout_mode</item>
        <item name="android:windowClipToOutline">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowElevation">0dp</item>
    </style>
    <style name="EdgeToEdgeFloatingDialogWindowTheme">
        <item name="android:dialogTheme">@style/EdgeToEdgeFloatingDialogTheme</item>
    </style>
    <style name="FloatingDialogTheme">
        <item name="android:windowIsFloating">false</item>
    </style>
    <style name="FloatingDialogWindowTheme">
        <item name="android:windowClipToOutline">false</item>
        <item name="android:dialogTheme">@style/FloatingDialogTheme</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="Platform.AppCompat" parent="android:Theme.Holo">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_dark</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_dark</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_light</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>

        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_dark</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_dark</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_dark</item>
    </style>
    <style name="Platform.AppCompat.Light" parent="android:Theme.Holo.Light">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>

        <item name="android:buttonBarStyle">?attr/buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:borderlessButtonStyle">?attr/borderlessButtonStyle</item>

        
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="android:disabledAlpha">@dimen/abc_disabled_alpha_material_light</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowBackground">@color/background_material_light</item>

        
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHighlightInverse">@color/highlighted_text_material_dark</item>
        <item name="android:textColorLink">?attr/colorAccent</item>
        <item name="android:textColorLinkInverse">?attr/colorAccent</item>
        <item name="android:textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>

        
        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>
        <item name="android:textAppearanceInverse">@style/TextAppearance.AppCompat.Inverse</item>
        <item name="android:textAppearanceLarge">@style/TextAppearance.AppCompat.Large</item>
        <item name="android:textAppearanceLargeInverse">@style/TextAppearance.AppCompat.Large.Inverse</item>
        <item name="android:textAppearanceMedium">@style/TextAppearance.AppCompat.Medium</item>
        <item name="android:textAppearanceMediumInverse">@style/TextAppearance.AppCompat.Medium.Inverse</item>
        <item name="android:textAppearanceSmall">@style/TextAppearance.AppCompat.Small</item>
        <item name="android:textAppearanceSmallInverse">@style/TextAppearance.AppCompat.Small.Inverse</item>

        <item name="android:listChoiceIndicatorSingle">@drawable/abc_btn_radio_material</item>
        <item name="android:listChoiceIndicatorMultiple">@drawable/abc_btn_check_material</item>

        <item name="android:listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="android:listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>

        <item name="android:actionModeCutDrawable">?actionModeCutDrawable</item>
        <item name="android:actionModeCopyDrawable">?actionModeCopyDrawable</item>
        <item name="android:actionModePasteDrawable">?actionModePasteDrawable</item>
        <item name="android:actionModeSelectAllDrawable">?actionModeSelectAllDrawable</item>

        <item name="android:textSelectHandle">@drawable/abc_text_select_handle_middle_mtrl_light</item>
        <item name="android:textSelectHandleLeft">@drawable/abc_text_select_handle_left_mtrl_light</item>
        <item name="android:textSelectHandleRight">@drawable/abc_text_select_handle_right_mtrl_light</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat" parent=""/>
    <style name="Platform.ThemeOverlay.AppCompat.Dark">
        
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>

        
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light">
        <item name="actionBarItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>

        
        <item name="android:autoCompleteTextViewStyle">@style/Widget.AppCompat.Light.AutoCompleteTextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
    </style>
    <style name="Platform.Widget.AppCompat.Spinner" parent="android:Widget.Holo.Spinner"/>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="Base.DialogWindowTitle.AppCompat">
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="android:Widget">
        <item name="android:layout_gravity">center_vertical|left</item>
        <item name="android:paddingRight">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="android:Widget">
        <item name="android:layout_marginRight">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="android:Widget">
        <item name="android:paddingRight">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="android:Widget">
        <item name="android:layout_marginLeft">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="android:Widget">
        <item name="android:layout_marginLeft">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="android:Widget">
        <item name="android:paddingLeft">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingRight">4dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="android:Widget">
        <item name="android:layout_alignParentLeft">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="android:Widget">
        <item name="android:layout_toLeftOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="android:Widget">
        <item name="android:layout_alignParentRight">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toLeftOf">@android:id/icon2</item>
        <item name="android:layout_toRightOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="android:Widget">
        <item name="android:layout_marginLeft">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="android:Widget">
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingRight">12dp</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingLeft">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="TextAppearance.AppCompat" parent="Base.TextAppearance.AppCompat"/>
    <style name="TextAppearance.AppCompat.Body1" parent="Base.TextAppearance.AppCompat.Body1"/>
    <style name="TextAppearance.AppCompat.Body2" parent="Base.TextAppearance.AppCompat.Body2"/>
    <style name="TextAppearance.AppCompat.Button" parent="Base.TextAppearance.AppCompat.Button"/>
    <style name="TextAppearance.AppCompat.Caption" parent="Base.TextAppearance.AppCompat.Caption"/>
    <style name="TextAppearance.AppCompat.Display1" parent="Base.TextAppearance.AppCompat.Display1"/>
    <style name="TextAppearance.AppCompat.Display2" parent="Base.TextAppearance.AppCompat.Display2"/>
    <style name="TextAppearance.AppCompat.Display3" parent="Base.TextAppearance.AppCompat.Display3"/>
    <style name="TextAppearance.AppCompat.Display4" parent="Base.TextAppearance.AppCompat.Display4"/>
    <style name="TextAppearance.AppCompat.Headline" parent="Base.TextAppearance.AppCompat.Headline"/>
    <style name="TextAppearance.AppCompat.Inverse" parent="Base.TextAppearance.AppCompat.Inverse"/>
    <style name="TextAppearance.AppCompat.Large" parent="Base.TextAppearance.AppCompat.Large"/>
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="Base.TextAppearance.AppCompat.Large.Inverse"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="TextAppearance.AppCompat.SearchResult.Subtitle"/>
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="TextAppearance.AppCompat.SearchResult.Title"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Medium" parent="Base.TextAppearance.AppCompat.Medium"/>
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="Base.TextAppearance.AppCompat.Medium.Inverse"/>
    <style name="TextAppearance.AppCompat.Menu" parent="Base.TextAppearance.AppCompat.Menu"/>
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="Base.TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Small" parent="Base.TextAppearance.AppCompat.Small"/>
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="Base.TextAppearance.AppCompat.Small.Inverse"/>
    <style name="TextAppearance.AppCompat.Subhead" parent="Base.TextAppearance.AppCompat.Subhead"/>
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="Base.TextAppearance.AppCompat.Subhead.Inverse"/>
    <style name="TextAppearance.AppCompat.Title" parent="Base.TextAppearance.AppCompat.Title"/>
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="Base.TextAppearance.AppCompat.Title.Inverse"/>
    <style name="TextAppearance.AppCompat.Tooltip" parent="Base.TextAppearance.AppCompat.Tooltip"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title"/>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Subtitle"/>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="Base.TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="TextAppearance.AppCompat.Widget.ActionMode.Title"/>
    <style name="TextAppearance.AppCompat.Widget.Button" parent="Base.TextAppearance.AppCompat.Widget.Button"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="Base.TextAppearance.AppCompat.Widget.Button.Colored"/>
    <style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="Base.TextAppearance.AppCompat.Widget.Button.Inverse"/>
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="Base.TextAppearance.AppCompat.Widget.DropDownItem">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large"/>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small"/>
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="Base.TextAppearance.AppCompat.Widget.Switch"/>
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem"/>
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.StatusBar.EventContent"/>
    <style name="TextAppearance.Compat.Notification.Info">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Info.Media"/>
    <style name="TextAppearance.Compat.Notification.Line2" parent="TextAppearance.Compat.Notification.Info"/>
    <style name="TextAppearance.Compat.Notification.Line2.Media" parent="TextAppearance.Compat.Notification.Info.Media"/>
    <style name="TextAppearance.Compat.Notification.Media"/>
    <style name="TextAppearance.Compat.Notification.Time">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time.Media"/>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.StatusBar.EventContent.Title"/>
    <style name="TextAppearance.Compat.Notification.Title.Media"/>
    <style name="TextAppearance.Leanback" parent="TextAppearance.LeanbackBase">
        
        <item name="android:fontFamily">sans-serif-condensed</item>
    </style>
    <style name="TextAppearance.Leanback.DetailsActionButton">
        <item name="android:textSize">@dimen/lb_action_text_size</item>
        <item name="android:textColor">@color/lb_action_text_color</item>
        <item name="android:textAllCaps">true</item>
    </style>
    <style name="TextAppearance.Leanback.DetailsDescriptionBody">
        <item name="android:textSize">@dimen/lb_details_description_body_text_size</item>
        <item name="android:textColor">@color/lb_details_description_body_color</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.Leanback.DetailsDescriptionSubtitle">
        <item name="android:textSize">@dimen/lb_details_description_subtitle_text_size</item>
        <item name="android:textColor">@color/lb_details_description_color</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.Leanback.DetailsDescriptionTitle">
        <item name="android:textSize">@dimen/lb_details_description_title_text_size</item>
        <item name="android:textColor">@color/lb_details_description_color</item>
        <item name="android:fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.Leanback.ErrorMessage">
        <item name="android:textSize">@dimen/lb_error_message_text_size</item>
        <item name="android:textColor">@color/lb_error_message</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.Leanback.Header" parent="TextAppearance.Leanback">
        <item name="android:textSize">@dimen/lb_browse_header_text_size</item>
        <item name="android:textColor">@color/lb_browse_header_color</item>
    </style>
    <style name="TextAppearance.Leanback.Header.Section">
        <item name="android:textColor">?defaultSectionHeaderColor</item>
        <item name="android:textSize">@dimen/lb_browse_section_header_text_size</item>
    </style>
    <style name="TextAppearance.Leanback.ImageCardView">
    </style>
    <style name="TextAppearance.Leanback.ImageCardView.Content">
        <item name="android:textColor">@color/lb_basic_card_content_text_color</item>
        <item name="android:textSize">@dimen/lb_basic_card_content_text_size</item>
    </style>
    <style name="TextAppearance.Leanback.ImageCardView.Title">
        <item name="android:textColor">@color/lb_basic_card_title_text_color</item>
        <item name="android:textSize">@dimen/lb_basic_card_title_text_size</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackControlLabel">
        <item name="android:textSize">@dimen/lb_control_button_text_size</item>
        <item name="android:textColor">@color/lb_control_button_text</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackControlsTime">
        <item name="android:textSize">@dimen/lb_playback_controls_time_text_size</item>
        <item name="android:textColor">@color/lb_playback_controls_time_text_color</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackMediaItemDuration">
        <item name="android:textColor">#80FFFFFF</item>
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">sans-serif-regular</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackMediaItemName">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">sans-serif-regular</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackMediaItemNumber">
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">sans-serif-regular</item>
    </style>
    <style name="TextAppearance.Leanback.PlaybackMediaListHeaderTitle">
        <item name="android:textColor">#80EEEEEE</item>
        <item name="android:textSize">18sp</item>
        <item name="android:fontFamily">sans-serif-regular</item>
    </style>
    <style name="TextAppearance.Leanback.Row.Header" parent="TextAppearance.Leanback.Header">
    </style>
    <style name="TextAppearance.Leanback.Row.Header.Description" parent="TextAppearance.Leanback.Header">
        <item name="android:textSize">@dimen/lb_browse_header_description_text_size</item>
        <item name="android:textColor">@color/lb_browse_header_description_color</item>
    </style>
    <style name="TextAppearance.Leanback.Row.HoverCardDescription" parent="TextAppearance.Leanback">
        <item name="android:textSize">@dimen/lb_browse_row_hovercard_description_font_size</item>
    </style>
    <style name="TextAppearance.Leanback.Row.HoverCardTitle" parent="TextAppearance.Leanback">
        <item name="android:textSize">@dimen/lb_browse_row_hovercard_title_font_size</item>
    </style>
    <style name="TextAppearance.Leanback.SearchTextEdit" parent="TextAppearance.Leanback">
        <item name="android:textSize">@dimen/lb_search_bar_text_size</item>
    </style>
    <style name="TextAppearance.Leanback.Title" parent="TextAppearance.Leanback">
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:textSize">@dimen/lb_browse_title_text_size</item>
        <item name="android:textColor">@color/lb_browse_title_color</item>
    </style>
    <style name="TextAppearance.LeanbackBase" parent="android:TextAppearance.Holo">
    </style>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="Base.TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style>
    <style name="Theme.AppCompat" parent="Base.Theme.AppCompat"/>
    <style name="Theme.AppCompat.CompactMenu" parent="Base.Theme.AppCompat.CompactMenu"/>
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.Light.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar"/>
    <style name="Theme.AppCompat.Dialog" parent="Base.Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.Dialog.Alert" parent="Base.Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="Base.Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DialogWhenLarge" parent="Base.Theme.AppCompat.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Leanback" parent="Theme.AppCompat.LeanbackBase">

        <item name="baseCardViewStyle">@style/Widget.Leanback.BaseCardViewStyle</item>
        <item name="imageCardViewStyle">@style/Widget.Leanback.ImageCardViewStyle</item>
        <item name="imageCardViewImageStyle">@style/Widget.Leanback.ImageCardView.ImageStyle</item>
        <item name="imageCardViewTitleStyle">@style/Widget.Leanback.ImageCardView.TitleStyle</item>
        <item name="imageCardViewContentStyle">@style/Widget.Leanback.ImageCardView.ContentStyle</item>
        <item name="imageCardViewBadgeStyle">@style/Widget.Leanback.ImageCardView.BadgeStyle</item>
        <item name="imageCardViewInfoAreaStyle">@style/Widget.Leanback.ImageCardView.InfoAreaStyle</item>

        <item name="browsePaddingStart">@dimen/lb_browse_padding_start</item>
        <item name="browsePaddingEnd">@dimen/lb_browse_padding_end</item>
        <item name="browsePaddingTop">@dimen/lb_browse_padding_top</item>
        <item name="browsePaddingBottom">@dimen/lb_browse_padding_bottom</item>
        <item name="browseRowsMarginStart">@dimen/lb_browse_rows_margin_start</item>
        <item name="browseRowsMarginTop">@dimen/lb_browse_rows_margin_top</item>
        <item name="browseRowsFadingEdgeLength">@dimen/lb_browse_rows_fading_edge</item>

        <item name="headersVerticalGridStyle">@style/Widget.Leanback.Headers.VerticalGridView</item>
        <item name="headerStyle">@style/Widget.Leanback.Header</item>
        <item name="sectionHeaderStyle">@style/Widget.Leanback.Header.Section</item>

        <item name="rowsVerticalGridStyle">@style/Widget.Leanback.Rows.VerticalGridView</item>
        <item name="rowHorizontalGridStyle">@style/Widget.Leanback.Row.HorizontalGridView</item>
        <item name="itemsVerticalGridStyle">@style/Widget.Leanback.GridItems.VerticalGridView</item>

        <item name="browseTitleViewLayout">@layout/lb_browse_title</item>
        <item name="browseTitleTextStyle">@style/Widget.Leanback.Title.Text</item>
        <item name="browseTitleIconStyle">@style/Widget.Leanback.Title.Icon</item>
        <item name="browseTitleViewStyle">@style/Widget.Leanback.TitleView</item>

        <item name="rowHeaderStyle">@style/Widget.Leanback.Row.Header</item>
        <item name="rowHeaderDescriptionStyle">@style/Widget.Leanback.Row.Header.Description</item>
        <item name="rowHoverCardTitleStyle">@style/Widget.Leanback.Row.HoverCardTitle</item>
        <item name="rowHoverCardDescriptionStyle">@style/Widget.Leanback.Row.HoverCardDescription</item>
        <item name="rowHeaderDockStyle">@style/Widget.Leanback.Row.HeaderDock</item>

        <item name="searchOrbViewStyle">@style/Widget.Leanback.SearchOrbViewStyle</item>


        <item name="detailsDescriptionTitleStyle">@style/Widget.Leanback.DetailsDescriptionTitleStyle</item>
        <item name="detailsDescriptionSubtitleStyle">@style/Widget.Leanback.DetailsDescriptionSubtitleStyle</item>
        <item name="detailsDescriptionBodyStyle">@style/Widget.Leanback.DetailsDescriptionBodyStyle</item>
        <item name="detailsActionButtonStyle">@style/Widget.Leanback.DetailsActionButtonStyle</item>
        
        <item name="playbackPaddingStart">@dimen/lb_playback_controls_margin_start</item>
        <item name="playbackPaddingEnd">@dimen/lb_playback_controls_margin_end</item>
        <item name="playbackMediaItemPaddingStart">@dimen/lb_playback_media_row_horizontal_padding</item>

        <item name="playbackMediaListHeaderStyle">@style/Widget.Leanback.PlaybackMediaListHeaderStyle</item>
        <item name="playbackMediaItemRowStyle">@style/Widget.Leanback.PlaybackMediaItemRowStyle</item>
        <item name="playbackMediaItemSeparatorStyle">@style/Widget.Leanback.PlaybackMediaItemSeparatorStyle</item>
        <item name="playbackMediaListHeaderTitleStyle">@style/Widget.Leanback.PlaybackMediaListHeaderTitleStyle</item>
        <item name="playbackMediaItemDetailsStyle">@style/Widget.Leanback.PlaybackMediaItemDetailsStyle</item>
        <item name="playbackMediaItemNumberViewFlipperStyle">@style/Widget.Leanback.PlaybackMediaItemNumberViewFlipperStyle</item>
        <item name="playbackMediaItemNumberViewFlipperLayout">@layout/lb_media_item_number_view_flipper</item>
        <item name="playbackMediaItemNumberStyle">@style/Widget.Leanback.PlaybackMediaItemNumberStyle</item>
        <item name="playbackMediaItemNameStyle">@style/Widget.Leanback.PlaybackMediaItemNameStyle</item>
        <item name="playbackMediaItemDurationStyle">@style/Widget.Leanback.PlaybackMediaItemDurationStyle</item>

        <item name="playbackControlsButtonStyle">@style/Widget.Leanback.PlaybackControlsButtonStyle</item>
        <item name="playbackControlButtonLabelStyle">@style/Widget.Leanback.PlaybackControlLabelStyle</item>
        <item name="playbackControlsTimeStyle">@style/Widget.Leanback.PlaybackControlsTimeStyle</item>
        <item name="playbackControlsActionIcons">@style/Widget.Leanback.PlaybackControlsActionIconsStyle</item>
        <item name="playbackControlsAutoHideTimeout">@integer/lb_playback_controls_show_time_ms</item>
        <item name="playbackControlsAutoHideTickleTimeout">@integer/lb_playback_controls_tickle_timeout_ms</item>

        <item name="errorMessageStyle">@style/Widget.Leanback.ErrorMessageStyle</item>

        <item name="defaultSearchColor">@color/lb_default_search_color</item>
        <item name="defaultSearchIconColor">@color/lb_default_search_icon_color</item>
        <item name="defaultSearchBrightColor">?attr/defaultSearchColor</item>
        <item name="defaultSearchIcon">@drawable/lb_ic_in_app_search</item>

        <item name="defaultSectionHeaderColor">?attr/defaultSearchColor</item>

        <item name="overlayDimMaskColor">@color/lb_view_dim_mask_color</item>
        <item name="overlayDimActiveLevel">@fraction/lb_view_active_level</item>
        <item name="overlayDimDimmedLevel">@fraction/lb_view_dimmed_level</item>

        <item name="pickerStyle">@style/Widget.Leanback.PickerStyle</item>
        <item name="datePickerStyle">@style/Widget.Leanback.PickerStyle.DatePickerStyle</item>
        <item name="timePickerStyle">@style/Widget.Leanback.PickerStyle.TimePickerStyle</item>
        <item name="pinPickerStyle">@style/Widget.Leanback.PickerStyle.PinPickerStyle</item>
    </style>
    <style name="Theme.AppCompat.Leanback.Browse" parent="Theme.AppCompat.Leanback">
    </style>
    <style name="Theme.AppCompat.Leanback.Details" parent="Theme.AppCompat.Leanback">
    </style>
    <style name="Theme.AppCompat.Leanback.Details.NoSharedElementTransition">
    </style>
    <style name="Theme.AppCompat.Leanback.GuidedStep" parent="Theme.AppCompat.Leanback.GuidedStepBase">
        <item name="guidedStepThemeFlag">true</item>
        <item name="guidedStepHeightWeight">@dimen/lb_guidedstep_height_weight</item>


        
        <item name="guidedStepBackground">?android:attr/colorBackground</item>
        
        <item name="android:windowBackground">@android:color/transparent</item>

        <item name="guidedStepImeAppearingAnimation">@animator/lb_guidedstep_slide_up</item>
        <item name="guidedStepImeDisappearingAnimation">@animator/lb_guidedstep_slide_down</item>

        <item name="guidanceContainerStyle">@style/Widget.Leanback.GuidanceContainerStyle</item>
        <item name="guidanceIconStyle">@style/Widget.Leanback.GuidanceIconStyle</item>
        <item name="guidanceTitleStyle">@style/Widget.Leanback.GuidanceTitleStyle</item>
        <item name="guidanceBreadcrumbStyle">@style/Widget.Leanback.GuidanceBreadcrumbStyle</item>
        <item name="guidanceDescriptionStyle">@style/Widget.Leanback.GuidanceDescriptionStyle</item>

        <item name="guidedActionsElevation">@dimen/lb_guidedactions_elevation</item>
        <item name="guidedActionsShadowWidth">@dimen/lb_guidedactions_section_shadow_width</item>
        <item name="guidedActionsBackground">@color/lb_guidedactions_background</item>
        <item name="guidedActionsBackgroundDark">@color/lb_guidedactions_background_dark</item>
        <item name="guidedActionsListStyle">@style/Widget.Leanback.GuidedActionsListStyle</item>
        <item name="guidedSubActionsListStyle">@style/Widget.Leanback.GuidedSubActionsListStyle</item>
        <item name="guidedButtonActionsListStyle">@style/Widget.Leanback.GuidedButtonActionsListStyle</item>

        <item name="guidedActionItemContainerStyle">@style/Widget.Leanback.GuidedActionItemContainerStyle</item>
        <item name="guidedActionItemCheckmarkStyle">@style/Widget.Leanback.GuidedActionItemCheckmarkStyle</item>
        <item name="guidedActionItemIconStyle">@style/Widget.Leanback.GuidedActionItemIconStyle</item>
        <item name="guidedActionItemContentStyle">@style/Widget.Leanback.GuidedActionItemContentStyle</item>
        <item name="guidedActionItemTitleStyle">@style/Widget.Leanback.GuidedActionItemTitleStyle</item>
        <item name="guidedActionItemDescriptionStyle">@style/Widget.Leanback.GuidedActionItemDescriptionStyle</item>
        <item name="guidedActionItemChevronStyle">@style/Widget.Leanback.GuidedActionItemChevronStyle</item>

        <item name="guidedActionPressedAnimation">@animator/lb_guidedactions_item_pressed</item>
        <item name="guidedActionUnpressedAnimation">@animator/lb_guidedactions_item_unpressed</item>
        <item name="guidedActionEnabledChevronAlpha">@dimen/lb_guidedactions_item_enabled_chevron_alpha</item>
        <item name="guidedActionDisabledChevronAlpha">@dimen/lb_guidedactions_item_disabled_chevron_alpha</item>
        <item name="guidedActionContentWidthWeight">@dimen/lb_guidedactions_width_weight</item>
        <item name="guidedActionContentWidthWeightTwoPanels">@dimen/lb_guidedactions_width_weight_two_panels</item>
        <item name="guidedButtonActionsWidthWeight">@dimen/lb_guidedbuttonactions_width_weight</item>
        <item name="guidedActionTitleMinLines">@integer/lb_guidedactions_item_title_min_lines</item>
        <item name="guidedActionTitleMaxLines">@integer/lb_guidedactions_item_title_max_lines</item>
        <item name="guidedActionDescriptionMinLines">@integer/lb_guidedactions_item_description_min_lines</item>
        <item name="guidedActionVerticalPadding">@dimen/lb_guidedactions_vertical_padding</item>
        <item name="guidedStepKeyline">@dimen/lb_guidedstep_keyline</item>
    </style>
    <style name="Theme.AppCompat.Leanback.GuidedStep.Half" parent="Theme.AppCompat.Leanback.GuidedStep.HalfBase">
        <item name="guidedStepHeightWeight">@dimen/lb_guidedstep_height_weight_translucent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
    <style name="Theme.AppCompat.Leanback.GuidedStep.HalfBase" parent="Theme.AppCompat.Leanback.GuidedStep">
    </style>
    <style name="Theme.AppCompat.Leanback.GuidedStepBase" parent="Theme.AppCompat.LeanbackBase">
        <item name="guidedActionsSelectorDrawable">?android:attr/selectableItemBackground</item>
    </style>
    <style name="Theme.AppCompat.Leanback.Onboarding" parent="Theme.AppCompat.LeanbackBase">
        <item name="onboardingHeaderStyle">@style/Widget.Leanback.OnboardingHeaderStyle</item>
        <item name="onboardingTitleStyle">@style/Widget.Leanback.OnboardingTitleStyle</item>
        <item name="onboardingDescriptionStyle">@style/Widget.Leanback.OnboardingDescriptionStyle</item>
        <item name="onboardingNavigatorContainerStyle">@style/Widget.Leanback.OnboardingNavigatorContainerStyle</item>
        <item name="onboardingPageIndicatorStyle">@style/Widget.Leanback.OnboardingPageIndicatorStyle</item>
        <item name="onboardingStartButtonStyle">@style/Widget.Leanback.OnboardingStartButtonStyle</item>
        <item name="onboardingLogoStyle">@style/Widget.Leanback.OnboardingLogoStyle</item>
        <item name="onboardingMainIconStyle">@style/Widget.Leanback.OnboardingMainIconStyle</item>
    </style>
    <style name="Theme.AppCompat.Leanback.VerticalGrid" parent="Theme.AppCompat.Leanback">
    </style>
    <style name="Theme.AppCompat.LeanbackBase" parent="Theme.AppCompat.NoActionBar">
        <item name="viewInflaterClass">androidx.leanback.widget.LeanbackAppCompatViewInflater</item>
        <item name="playbackProgressPrimaryColor">@color/lb_playback_progress_color_no_theme</item>
        <item name="playbackProgressSecondaryColor">@color/lb_playback_progress_secondary_color_no_theme</item>
        <item name="playbackControlsIconHighlightColor">@color/lb_playback_icon_highlight_no_theme</item>
        <item name="defaultBrandColor">@color/lb_default_brand_color</item>
        <item name="defaultBrandColorDark">@color/lb_default_brand_color_dark</item>

        <item name="guidedStepTheme">@style/Theme.AppCompat.Leanback.GuidedStep</item>
    </style>
    <style name="Theme.AppCompat.Light" parent="Base.Theme.AppCompat.Light"/>
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="Base.Theme.AppCompat.Light.DarkActionBar"/>
    <style name="Theme.AppCompat.Light.Dialog" parent="Base.Theme.AppCompat.Light.Dialog"/>
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="Base.Theme.AppCompat.Light.Dialog.Alert"/>
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="Base.Theme.AppCompat.Light.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="Base.Theme.AppCompat.Light.DialogWhenLarge">
    </style>
    <style name="Theme.AppCompat.Light.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Leanback" parent="Theme.LeanbackBase">

        <item name="baseCardViewStyle">@style/Widget.Leanback.BaseCardViewStyle</item>
        <item name="imageCardViewStyle">@style/Widget.Leanback.ImageCardViewStyle</item>
        <item name="imageCardViewImageStyle">@style/Widget.Leanback.ImageCardView.ImageStyle</item>
        <item name="imageCardViewTitleStyle">@style/Widget.Leanback.ImageCardView.TitleStyle</item>
        <item name="imageCardViewContentStyle">@style/Widget.Leanback.ImageCardView.ContentStyle</item>
        <item name="imageCardViewBadgeStyle">@style/Widget.Leanback.ImageCardView.BadgeStyle</item>
        <item name="imageCardViewInfoAreaStyle">@style/Widget.Leanback.ImageCardView.InfoAreaStyle</item>

        <item name="browsePaddingStart">@dimen/lb_browse_padding_start</item>
        <item name="browsePaddingEnd">@dimen/lb_browse_padding_end</item>
        <item name="browsePaddingTop">@dimen/lb_browse_padding_top</item>
        <item name="browsePaddingBottom">@dimen/lb_browse_padding_bottom</item>
        <item name="browseRowsMarginStart">@dimen/lb_browse_rows_margin_start</item>
        <item name="browseRowsMarginTop">@dimen/lb_browse_rows_margin_top</item>
        <item name="browseRowsFadingEdgeLength">@dimen/lb_browse_rows_fading_edge</item>

        <item name="headersVerticalGridStyle">@style/Widget.Leanback.Headers.VerticalGridView</item>
        <item name="headerStyle">@style/Widget.Leanback.Header</item>
        <item name="sectionHeaderStyle">@style/Widget.Leanback.Header.Section</item>

        <item name="rowsVerticalGridStyle">@style/Widget.Leanback.Rows.VerticalGridView</item>
        <item name="rowHorizontalGridStyle">@style/Widget.Leanback.Row.HorizontalGridView</item>
        <item name="itemsVerticalGridStyle">@style/Widget.Leanback.GridItems.VerticalGridView</item>

        <item name="browseTitleViewLayout">@layout/lb_browse_title</item>
        <item name="browseTitleTextStyle">@style/Widget.Leanback.Title.Text</item>
        <item name="browseTitleIconStyle">@style/Widget.Leanback.Title.Icon</item>
        <item name="browseTitleViewStyle">@style/Widget.Leanback.TitleView</item>

        <item name="rowHeaderStyle">@style/Widget.Leanback.Row.Header</item>
        <item name="rowHeaderDescriptionStyle">@style/Widget.Leanback.Row.Header.Description</item>
        <item name="rowHoverCardTitleStyle">@style/Widget.Leanback.Row.HoverCardTitle</item>
        <item name="rowHoverCardDescriptionStyle">@style/Widget.Leanback.Row.HoverCardDescription</item>
        <item name="rowHeaderDockStyle">@style/Widget.Leanback.Row.HeaderDock</item>

        <item name="searchOrbViewStyle">@style/Widget.Leanback.SearchOrbViewStyle</item>


        <item name="detailsDescriptionTitleStyle">@style/Widget.Leanback.DetailsDescriptionTitleStyle</item>
        <item name="detailsDescriptionSubtitleStyle">@style/Widget.Leanback.DetailsDescriptionSubtitleStyle</item>
        <item name="detailsDescriptionBodyStyle">@style/Widget.Leanback.DetailsDescriptionBodyStyle</item>
        <item name="detailsActionButtonStyle">@style/Widget.Leanback.DetailsActionButtonStyle</item>
        
        <item name="playbackPaddingStart">@dimen/lb_playback_controls_margin_start</item>
        <item name="playbackPaddingEnd">@dimen/lb_playback_controls_margin_end</item>
        <item name="playbackMediaItemPaddingStart">@dimen/lb_playback_media_row_horizontal_padding</item>

        <item name="playbackMediaListHeaderStyle">@style/Widget.Leanback.PlaybackMediaListHeaderStyle</item>
        <item name="playbackMediaItemRowStyle">@style/Widget.Leanback.PlaybackMediaItemRowStyle</item>
        <item name="playbackMediaItemSeparatorStyle">@style/Widget.Leanback.PlaybackMediaItemSeparatorStyle</item>
        <item name="playbackMediaListHeaderTitleStyle">@style/Widget.Leanback.PlaybackMediaListHeaderTitleStyle</item>
        <item name="playbackMediaItemDetailsStyle">@style/Widget.Leanback.PlaybackMediaItemDetailsStyle</item>
        <item name="playbackMediaItemNumberViewFlipperStyle">@style/Widget.Leanback.PlaybackMediaItemNumberViewFlipperStyle</item>
        <item name="playbackMediaItemNumberViewFlipperLayout">@layout/lb_media_item_number_view_flipper</item>
        <item name="playbackMediaItemNumberStyle">@style/Widget.Leanback.PlaybackMediaItemNumberStyle</item>
        <item name="playbackMediaItemNameStyle">@style/Widget.Leanback.PlaybackMediaItemNameStyle</item>
        <item name="playbackMediaItemDurationStyle">@style/Widget.Leanback.PlaybackMediaItemDurationStyle</item>

        <item name="playbackControlsButtonStyle">@style/Widget.Leanback.PlaybackControlsButtonStyle</item>
        <item name="playbackControlButtonLabelStyle">@style/Widget.Leanback.PlaybackControlLabelStyle</item>
        <item name="playbackControlsTimeStyle">@style/Widget.Leanback.PlaybackControlsTimeStyle</item>
        <item name="playbackControlsActionIcons">@style/Widget.Leanback.PlaybackControlsActionIconsStyle</item>
        <item name="playbackControlsAutoHideTimeout">@integer/lb_playback_controls_show_time_ms</item>
        <item name="playbackControlsAutoHideTickleTimeout">@integer/lb_playback_controls_tickle_timeout_ms</item>

        <item name="errorMessageStyle">@style/Widget.Leanback.ErrorMessageStyle</item>

        <item name="defaultSearchColor">@color/lb_default_search_color</item>
        <item name="defaultSearchIconColor">@color/lb_default_search_icon_color</item>
        <item name="defaultSearchBrightColor">?attr/defaultSearchColor</item>
        <item name="defaultSearchIcon">@drawable/lb_ic_in_app_search</item>

        <item name="defaultSectionHeaderColor">?attr/defaultSearchColor</item>

        <item name="overlayDimMaskColor">@color/lb_view_dim_mask_color</item>
        <item name="overlayDimActiveLevel">@fraction/lb_view_active_level</item>
        <item name="overlayDimDimmedLevel">@fraction/lb_view_dimmed_level</item>

        <item name="pickerStyle">@style/Widget.Leanback.PickerStyle</item>
        <item name="datePickerStyle">@style/Widget.Leanback.PickerStyle.DatePickerStyle</item>
        <item name="timePickerStyle">@style/Widget.Leanback.PickerStyle.TimePickerStyle</item>
        <item name="pinPickerStyle">@style/Widget.Leanback.PickerStyle.PinPickerStyle</item>
    </style>
    <style name="Theme.Leanback.Browse" parent="Theme.Leanback">
    </style>
    <style name="Theme.Leanback.Details" parent="Theme.Leanback">
    </style>
    <style name="Theme.Leanback.Details.NoSharedElementTransition">
    </style>
    <style name="Theme.Leanback.GuidedStep" parent="Theme.Leanback.GuidedStepBase">
        <item name="guidedStepThemeFlag">true</item>
        <item name="guidedStepHeightWeight">@dimen/lb_guidedstep_height_weight</item>


        
        <item name="guidedStepBackground">?android:attr/colorBackground</item>
        
        <item name="android:windowBackground">@android:color/transparent</item>

        <item name="guidedStepImeAppearingAnimation">@animator/lb_guidedstep_slide_up</item>
        <item name="guidedStepImeDisappearingAnimation">@animator/lb_guidedstep_slide_down</item>

        <item name="guidanceContainerStyle">@style/Widget.Leanback.GuidanceContainerStyle</item>
        <item name="guidanceIconStyle">@style/Widget.Leanback.GuidanceIconStyle</item>
        <item name="guidanceTitleStyle">@style/Widget.Leanback.GuidanceTitleStyle</item>
        <item name="guidanceBreadcrumbStyle">@style/Widget.Leanback.GuidanceBreadcrumbStyle</item>
        <item name="guidanceDescriptionStyle">@style/Widget.Leanback.GuidanceDescriptionStyle</item>

        <item name="guidedActionsElevation">@dimen/lb_guidedactions_elevation</item>
        <item name="guidedActionsShadowWidth">@dimen/lb_guidedactions_section_shadow_width</item>
        <item name="guidedActionsBackground">@color/lb_guidedactions_background</item>
        <item name="guidedActionsBackgroundDark">@color/lb_guidedactions_background_dark</item>
        <item name="guidedActionsListStyle">@style/Widget.Leanback.GuidedActionsListStyle</item>
        <item name="guidedSubActionsListStyle">@style/Widget.Leanback.GuidedSubActionsListStyle</item>
        <item name="guidedButtonActionsListStyle">@style/Widget.Leanback.GuidedButtonActionsListStyle</item>

        <item name="guidedActionItemContainerStyle">@style/Widget.Leanback.GuidedActionItemContainerStyle</item>
        <item name="guidedActionItemCheckmarkStyle">@style/Widget.Leanback.GuidedActionItemCheckmarkStyle</item>
        <item name="guidedActionItemIconStyle">@style/Widget.Leanback.GuidedActionItemIconStyle</item>
        <item name="guidedActionItemContentStyle">@style/Widget.Leanback.GuidedActionItemContentStyle</item>
        <item name="guidedActionItemTitleStyle">@style/Widget.Leanback.GuidedActionItemTitleStyle</item>
        <item name="guidedActionItemDescriptionStyle">@style/Widget.Leanback.GuidedActionItemDescriptionStyle</item>
        <item name="guidedActionItemChevronStyle">@style/Widget.Leanback.GuidedActionItemChevronStyle</item>

        <item name="guidedActionPressedAnimation">@animator/lb_guidedactions_item_pressed</item>
        <item name="guidedActionUnpressedAnimation">@animator/lb_guidedactions_item_unpressed</item>
        <item name="guidedActionEnabledChevronAlpha">@dimen/lb_guidedactions_item_enabled_chevron_alpha</item>
        <item name="guidedActionDisabledChevronAlpha">@dimen/lb_guidedactions_item_disabled_chevron_alpha</item>
        <item name="guidedActionContentWidthWeight">@dimen/lb_guidedactions_width_weight</item>
        <item name="guidedActionContentWidthWeightTwoPanels">@dimen/lb_guidedactions_width_weight_two_panels</item>
        <item name="guidedButtonActionsWidthWeight">@dimen/lb_guidedbuttonactions_width_weight</item>
        <item name="guidedActionTitleMinLines">@integer/lb_guidedactions_item_title_min_lines</item>
        <item name="guidedActionTitleMaxLines">@integer/lb_guidedactions_item_title_max_lines</item>
        <item name="guidedActionDescriptionMinLines">@integer/lb_guidedactions_item_description_min_lines</item>
        <item name="guidedActionVerticalPadding">@dimen/lb_guidedactions_vertical_padding</item>
        <item name="guidedStepKeyline">@dimen/lb_guidedstep_keyline</item>
    </style>
    <style name="Theme.Leanback.GuidedStep.Half" parent="Theme.Leanback.GuidedStep.HalfBase">
      <item name="guidedStepHeightWeight">@dimen/lb_guidedstep_height_weight_translucent</item>
      <item name="android:windowIsTranslucent">true</item>
      <item name="android:windowBackground">@android:color/transparent</item>
    </style>
    <style name="Theme.Leanback.GuidedStep.HalfBase" parent="Theme.Leanback.GuidedStep">
    </style>
    <style name="Theme.Leanback.GuidedStepBase" parent="Theme.LeanbackBase">
        <item name="guidedActionsSelectorDrawable">?android:attr/selectableItemBackground</item>
    </style>
    <style name="Theme.Leanback.Onboarding" parent="Theme.LeanbackBase">
        <item name="onboardingHeaderStyle">@style/Widget.Leanback.OnboardingHeaderStyle</item>
        <item name="onboardingTitleStyle">@style/Widget.Leanback.OnboardingTitleStyle</item>
        <item name="onboardingDescriptionStyle">@style/Widget.Leanback.OnboardingDescriptionStyle</item>
        <item name="onboardingNavigatorContainerStyle">@style/Widget.Leanback.OnboardingNavigatorContainerStyle</item>
        <item name="onboardingPageIndicatorStyle">@style/Widget.Leanback.OnboardingPageIndicatorStyle</item>
        <item name="onboardingStartButtonStyle">@style/Widget.Leanback.OnboardingStartButtonStyle</item>
        <item name="onboardingLogoStyle">@style/Widget.Leanback.OnboardingLogoStyle</item>
        <item name="onboardingMainIconStyle">@style/Widget.Leanback.OnboardingMainIconStyle</item>
    </style>
    <style name="Theme.Leanback.VerticalGrid" parent="Theme.Leanback">
    </style>
    <style name="Theme.LeanbackBase" parent="android:Theme.Holo.NoActionBar">
        <item name="playbackProgressPrimaryColor">@color/lb_playback_progress_color_no_theme</item>
        <item name="playbackProgressSecondaryColor">@color/lb_playback_progress_secondary_color_no_theme</item>
        <item name="playbackControlsIconHighlightColor">@color/lb_playback_icon_highlight_no_theme</item>
        <item name="defaultBrandColor">@color/lb_default_brand_color</item>
        <item name="defaultBrandColorDark">@color/lb_default_brand_color_dark</item>

        <item name="guidedStepTheme">@style/Theme.Leanback.GuidedStep</item>
    </style>
    <style name="ThemeOverlay.AppCompat" parent="Base.ThemeOverlay.AppCompat"/>
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="Base.ThemeOverlay.AppCompat.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.Dark" parent="Base.ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="Base.ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="ThemeOverlay.AppCompat.Dialog" parent="Base.ThemeOverlay.AppCompat.Dialog"/>
    <style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="Base.ThemeOverlay.AppCompat.Dialog.Alert"/>
    <style name="ThemeOverlay.AppCompat.Light" parent="Base.ThemeOverlay.AppCompat.Light"/>
    <style name="Widget.AppCompat.ActionBar" parent="Base.Widget.AppCompat.ActionBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.Solid" parent="Base.Widget.AppCompat.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="Base.Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabText" parent="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabView" parent="Base.Widget.AppCompat.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.ActionButton" parent="Base.Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="Base.Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="Base.Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.ActionMode" parent="Base.Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.ActivityChooserView" parent="Base.Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="Base.Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Button" parent="Base.Widget.AppCompat.Button"/>
    <style name="Widget.AppCompat.Button.Borderless" parent="Base.Widget.AppCompat.Button.Borderless"/>
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="Base.Widget.AppCompat.Button.Borderless.Colored"/>
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.Button.Colored" parent="Base.Widget.AppCompat.Button.Colored"/>
    <style name="Widget.AppCompat.Button.Small" parent="Base.Widget.AppCompat.Button.Small"/>
    <style name="Widget.AppCompat.ButtonBar" parent="Base.Widget.AppCompat.ButtonBar"/>
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="Base.Widget.AppCompat.ButtonBar.AlertDialog"/>
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="Base.Widget.AppCompat.CompoundButton.CheckBox"/>
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="Base.Widget.AppCompat.CompoundButton.RadioButton"/>
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="Base.Widget.AppCompat.CompoundButton.Switch"/>
    <style name="Widget.AppCompat.DrawerArrowToggle" parent="Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?attr/colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="RtlOverlay.Widget.AppCompat.Search.DropDown.Text"/>
    <style name="Widget.AppCompat.EditText" parent="Base.Widget.AppCompat.EditText"/>
    <style name="Widget.AppCompat.ImageButton" parent="Base.Widget.AppCompat.ImageButton"/>
    <style name="Widget.AppCompat.Light.ActionBar" parent="Base.Widget.AppCompat.Light.ActionBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="Base.Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="Base.Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="Base.Widget.AppCompat.Light.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="Base.Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse"/>
    <style name="Widget.AppCompat.Light.ActionButton" parent="Widget.AppCompat.ActionButton"/>
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="Widget.AppCompat.ActionButton.CloseMode"/>
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="Widget.AppCompat.ActionButton.Overflow"/>
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="Widget.AppCompat.ActionMode"/>
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="Widget.AppCompat.ActivityChooserView"/>
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="Widget.AppCompat.AutoCompleteTextView"/>
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="Widget.AppCompat.DropDownItem.Spinner"/>
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="Widget.AppCompat.ListPopupWindow"/>
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.Light.PopupMenu" parent="Base.Widget.AppCompat.Light.PopupMenu"/>
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.SearchView" parent="Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.ListMenuView" parent="Base.Widget.AppCompat.ListMenuView"/>
    <style name="Widget.AppCompat.ListPopupWindow" parent="Base.Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.ListView" parent="Base.Widget.AppCompat.ListView"/>
    <style name="Widget.AppCompat.ListView.DropDown" parent="Base.Widget.AppCompat.ListView.DropDown"/>
    <style name="Widget.AppCompat.ListView.Menu" parent="Base.Widget.AppCompat.ListView.Menu"/>
    <style name="Widget.AppCompat.PopupMenu" parent="Base.Widget.AppCompat.PopupMenu"/>
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="Base.Widget.AppCompat.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.PopupWindow" parent="Base.Widget.AppCompat.PopupWindow">
    </style>
    <style name="Widget.AppCompat.ProgressBar" parent="Base.Widget.AppCompat.ProgressBar">
    </style>
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
    </style>
    <style name="Widget.AppCompat.RatingBar" parent="Base.Widget.AppCompat.RatingBar"/>
    <style name="Widget.AppCompat.RatingBar.Indicator" parent="Base.Widget.AppCompat.RatingBar.Indicator"/>
    <style name="Widget.AppCompat.RatingBar.Small" parent="Base.Widget.AppCompat.RatingBar.Small"/>
    <style name="Widget.AppCompat.SearchView" parent="Base.Widget.AppCompat.SearchView"/>
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="Base.Widget.AppCompat.SearchView.ActionBar"/>
    <style name="Widget.AppCompat.SeekBar" parent="Base.Widget.AppCompat.SeekBar"/>
    <style name="Widget.AppCompat.SeekBar.Discrete" parent="Base.Widget.AppCompat.SeekBar.Discrete"/>
    <style name="Widget.AppCompat.Spinner" parent="Base.Widget.AppCompat.Spinner"/>
    <style name="Widget.AppCompat.Spinner.DropDown"/>
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar"/>
    <style name="Widget.AppCompat.Spinner.Underlined" parent="Base.Widget.AppCompat.Spinner.Underlined"/>
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="Base.Widget.AppCompat.TextView.SpinnerItem"/>
    <style name="Widget.AppCompat.Toolbar" parent="Base.Widget.AppCompat.Toolbar"/>
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="Base.Widget.AppCompat.Toolbar.Button.Navigation"/>
    <style name="Widget.Compat.NotificationActionContainer" parent=""/>
    <style name="Widget.Compat.NotificationActionText" parent=""/>
    <style name="Widget.Leanback" parent="Widget.LeanbackBase"/>
    <style name="Widget.Leanback.BaseCardViewStyle">
        <item name="cardForeground">@drawable/lb_card_foreground</item>
        <item name="cardBackground">@color/lb_basic_card_bg_color</item>
    </style>
    <style name="Widget.Leanback.DetailsActionButtonStyle" parent="Widget.Leanback.DetailsActionButtonStyleBase">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.DetailsActionButton</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:drawablePadding">@dimen/lb_action_icon_margin</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:paddingStart">@dimen/lb_action_padding_horizontal</item>
        <item name="android:paddingEnd">@dimen/lb_action_padding_horizontal</item>
    </style>
    <style name="Widget.Leanback.DetailsActionButtonStyleBase" parent="android:Widget.Holo.Button.Borderless">
    </style>
    <style name="Widget.Leanback.DetailsDescriptionBodyStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.DetailsDescriptionBody</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="Widget.Leanback.DetailsDescriptionSubtitleStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.DetailsDescriptionSubtitle</item>
        <item name="android:maxLines">@integer/lb_details_description_subtitle_max_lines</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="Widget.Leanback.DetailsDescriptionTitleStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.DetailsDescriptionTitle</item>
        <item name="android:maxLines">@integer/lb_details_description_title_max_lines</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:ellipsize">end</item>
        <item name="resizeTrigger">maxLines</item>
        <item name="resizedTextSize">@dimen/lb_details_description_title_resized_text_size</item>
        <item name="resizedPaddingAdjustmentTop">@dimen/lb_details_description_title_padding_adjust_top</item>
        <item name="resizedPaddingAdjustmentBottom">@dimen/lb_details_description_title_padding_adjust_bottom</item>
    </style>
    <style name="Widget.Leanback.ErrorMessageStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.ErrorMessage</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:maxLines">@integer/lb_error_message_max_lines</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="Widget.Leanback.GridItems"/>
    <style name="Widget.Leanback.GridItems.VerticalGridView">
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:paddingStart">?attr/browsePaddingStart</item>
        <item name="android:paddingEnd">?attr/browsePaddingEnd</item>
        <item name="android:paddingBottom">@dimen/lb_vertical_grid_padding_bottom</item>
        <item name="android:paddingTop">?attr/browseRowsMarginTop</item>
        <item name="android:gravity">center_horizontal</item>
        <item name="android:horizontalSpacing">@dimen/lb_browse_item_horizontal_spacing</item>
        <item name="android:verticalSpacing">@dimen/lb_browse_item_vertical_spacing</item>
        <item name="focusOutFront">true</item>
    </style>
    <style name="Widget.Leanback.GuidanceBreadcrumbStyle">
        <item name="android:importantForAccessibility">no</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_toEndOf">@id/guidance_icon</item>
        <item name="android:ellipsize">end</item>
        <item name="android:fontFamily">sans-serif-condensed</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">#88F1F1F1</item>
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">start</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Widget.Leanback.GuidanceContainerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:paddingStart">56dp</item>
        <item name="android:paddingEnd">32dp</item>
        <item name="android:clipToPadding">false</item>
    </style>
    <style name="Widget.Leanback.GuidanceDescriptionStyle">
        <item name="android:importantForAccessibility">no</item>
        <item name="android:layout_below">@id/guidance_title</item>
        <item name="android:layout_toEndOf">@id/guidance_icon</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignWithParentIfMissing">true</item>
        <item name="android:ellipsize">end</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:gravity">start</item>
        <item name="android:maxLines">6</item>
        <item name="android:textColor">#88F1F1F1</item>
        <item name="android:textSize">14sp</item>
        <item name="android:lineSpacingExtra">3dp</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Widget.Leanback.GuidanceIconStyle">
        <item name="android:layout_width">140dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_marginEnd">24dp</item>
        <item name="android:maxHeight">280dp</item>
        <item name="android:scaleType">fitCenter</item>
    </style>
    <style name="Widget.Leanback.GuidanceTitleStyle">
        <item name="android:importantForAccessibility">no</item>
        <item name="android:layout_below">@id/guidance_breadcrumb</item>
        <item name="android:layout_toEndOf">@id/guidance_icon</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:ellipsize">end</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:gravity">start</item>
        <item name="android:maxLines">2</item>
        <item name="android:textColor">#FFF1F1F1</item>
        <item name="android:textSize">36sp</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:paddingBottom">4dp</item>
        <item name="android:paddingTop">2dp</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemCheckmarkStyle">
        <item name="android:layout_width">@dimen/lb_guidedactions_item_checkmark_diameter</item>
        <item name="android:layout_height">@dimen/lb_guidedactions_item_checkmark_diameter</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginEnd">@dimen/lb_guidedactions_item_delimiter_padding</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemChevronStyle">
        <item name="android:layout_width">12dp</item>
        <item name="android:layout_height">12dp</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginStart">@dimen/lb_guidedactions_item_delimiter_padding</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:src">@drawable/lb_ic_guidedactions_item_chevron</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemContainerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:foreground">?attr/guidedActionsSelectorDrawable</item>
        <item name="android:focusable">true</item>
        
        <item name="android:focusableInTouchMode">false</item>
        <item name="android:paddingBottom">@dimen/lb_guidedactions_item_bottom_padding</item>
        <item name="android:paddingStart">@dimen/lb_guidedactions_item_start_padding</item>
        <item name="android:paddingEnd">@dimen/lb_guidedactions_item_end_padding</item>
        <item name="android:paddingTop">@dimen/lb_guidedactions_item_top_padding</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemContentStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">start|center_vertical</item>
        <item name="android:layout_weight">1</item>
        <item name="android:orientation">vertical</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemDescriptionStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:alpha">@dimen/lb_guidedactions_item_unselected_description_text_alpha</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginTop">@dimen/lb_guidedactions_item_space_between_title_and_description</item>
        <item name="android:fontFamily">sans-serif-condensed</item>
        <item name="android:maxLines">@integer/lb_guidedactions_item_description_min_lines</item>
        <item name="android:textColor">@color/lb_guidedactions_item_unselected_text_color</item>
        <item name="android:textSize">@dimen/lb_guidedactions_item_description_font_size</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemIconStyle">
        <item name="android:layout_width">@dimen/lb_guidedactions_item_icon_width</item>
        <item name="android:layout_height">@dimen/lb_guidedactions_item_icon_height</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:layout_marginEnd">@dimen/lb_guidedactions_item_delimiter_padding</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.GuidedActionItemTitleStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:alpha">@dimen/lb_guidedactions_item_unselected_text_alpha</item>
        <item name="android:ellipsize">end</item>
        <item name="android:fontFamily">sans-serif-condensed</item>
        <item name="android:maxLines">@integer/lb_guidedactions_item_title_min_lines</item>
        <item name="android:textColor">@color/lb_guidedactions_item_unselected_text_color</item>
        <item name="android:textSize">@dimen/lb_guidedactions_item_title_font_size</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Widget.Leanback.GuidedActionsContainerStyle"/>
    <style name="Widget.Leanback.GuidedActionsListStyle">
        <item name="android:focusable">false</item>
        <item name="android:focusableInTouchMode">false</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:verticalSpacing">@dimen/lb_guidedactions_list_vertical_spacing</item>
        <item name="android:paddingStart">@dimen/lb_guidedactions_list_padding_start</item>
        <item name="android:paddingEnd">@dimen/lb_guidedactions_list_padding_end</item>
        <item name="android:clipToPadding">false</item>
        <item name="focusOutEnd">false</item>
        <item name="focusOutFront">false</item>
    </style>
    <style name="Widget.Leanback.GuidedActionsSelectorStyle"/>
    <style name="Widget.Leanback.GuidedButtonActionsListStyle" parent="Widget.Leanback.GuidedActionsListStyle">
    </style>
    <style name="Widget.Leanback.GuidedSubActionsListStyle" parent="Widget.Leanback.GuidedActionsListStyle">
        <item name="android:paddingTop">@dimen/lb_guidedactions_sublist_padding_top</item>
        <item name="android:paddingBottom">@dimen/lb_guidedactions_sublist_padding_bottom</item>
        <item name="android:clipToPadding">false</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="focusOutSideStart">false</item>
        <item name="focusOutSideEnd">false</item>
        <item name="android:layout_marginBottom">@dimen/lb_guidedactions_sublist_bottom_margin</item>
    </style>
    <style name="Widget.Leanback.Header">
        <item name="android:minHeight">@dimen/lb_browse_header_height</item>
        <item name="android:minWidth">1dp</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Header</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:singleLine">false</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">none</item>
    </style>
    <style name="Widget.Leanback.Header.Section">
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Header.Section</item>
        <item name="android:singleLine">true</item>
    </style>
    <style name="Widget.Leanback.Headers"/>
    <style name="Widget.Leanback.Headers.VerticalGridView">
        <item name="android:background">?attr/defaultBrandColor</item>
        <item name="android:paddingStart">?attr/browsePaddingStart</item>
        <item name="focusOutFront">true</item>
        <item name="focusOutEnd">true</item>
        <item name="android:verticalSpacing">@dimen/lb_browse_headers_vertical_spacing</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:contentDescription">@string/lb_navigation_menu_contentDescription</item>
    </style>
    <style name="Widget.Leanback.ImageCardView"/>
    <style name="Widget.Leanback.ImageCardView.BadgeStyle">
        <item name="android:id">@id/extra_badge</item>
        <item name="android:layout_width">@dimen/lb_basic_card_info_badge_size</item>
        <item name="android:layout_height">@dimen/lb_basic_card_info_badge_size</item>
        <item name="android:contentDescription">@null</item>
        <item name="android:scaleType">fitCenter</item>
    </style>
    <style name="Widget.Leanback.ImageCardView.ContentStyle">
        <item name="android:id">@id/content_text</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_below">@+id/title_text</item>
        <item name="android:layout_toStartOf">@+id/extra_badge</item>
        <item name="android:maxLines">1</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.ImageCardView.Content</item>
    </style>
    <style name="Widget.Leanback.ImageCardView.ImageStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:adjustViewBounds">true</item>
        <item name="android:contentDescription">@null</item>
        <item name="android:scaleType">centerCrop</item>
        <item name="layout_viewType">main</item>
    </style>
    <style name="Widget.Leanback.ImageCardView.InfoAreaStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="layout_viewType">info</item>
        <item name="android:paddingBottom">@dimen/lb_basic_card_info_padding_bottom</item>
        <item name="android:paddingEnd">@dimen/lb_basic_card_info_padding_horizontal</item>
        <item name="android:paddingStart">@dimen/lb_basic_card_info_padding_horizontal</item>
        <item name="android:paddingTop">@dimen/lb_basic_card_info_padding_top</item>
        <item name="android:background">@color/lb_basic_card_info_bg_color</item>
    </style>
    <style name="Widget.Leanback.ImageCardView.TitleStyle">
        <item name="android:id">@id/title_text</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:maxLines">1</item>
        <item name="android:layout_marginBottom">@dimen/lb_basic_card_info_text_margin</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.ImageCardView.Title</item>
    </style>
    <style name="Widget.Leanback.ImageCardViewStyle" parent="Widget.Leanback.BaseCardViewStyle">
        <item name="cardType">infoUnder</item>
        <item name="infoVisibility">activated</item>
        
        <item name="lbImageCardViewType">Title|Content|IconOnRight</item>
        
        <item name="infoAreaBackground">@null</item>
    </style>
    <style name="Widget.Leanback.OnboardingDescriptionStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">0.5</item>
        <item name="android:layout_marginTop">3dp</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">#B3EEEEEE</item>
        <item name="android:textSize">14sp</item>
        <item name="android:lineSpacingExtra">10sp</item>
    </style>
    <style name="Widget.Leanback.OnboardingHeaderStyle">
        <item name="android:layout_width">@dimen/lb_onboarding_content_width</item>
        <item name="android:layout_height">@dimen/lb_onboarding_header_height</item>
        <item name="android:layout_alignParentTop">true</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_marginTop">@dimen/lb_onboarding_header_margin_top</item>
        <item name="android:clipChildren">false</item>
        <item name="android:clipToPadding">false</item>
        <item name="android:orientation">vertical</item>
    </style>
    <style name="Widget.Leanback.OnboardingLogoStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerInParent">true</item>
        <item name="android:contentDescription">@null</item>
    </style>
    <style name="Widget.Leanback.OnboardingMainIconStyle">
        <item name="android:layout_width">64dp</item>
        <item name="android:layout_height">64dp</item>
        <item name="android:layout_above">@id/page_container</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_marginBottom">16dp</item>
        <item name="android:contentDescription">@null</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.OnboardingNavigatorContainerStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">58dp</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_alignParentBottom">true</item>
    </style>
    <style name="Widget.Leanback.OnboardingPageIndicatorStyle">
        <item name="android:layout_width">@dimen/lb_onboarding_content_width</item>
        <item name="android:layout_height">@dimen/lb_onboarding_navigation_height</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:focusable">true</item>
        <item name="android:contentDescription">@string/lb_onboarding_accessibility_next</item>
        <item name="lbDotRadius">@dimen/lb_page_indicator_dot_radius</item>
        <item name="arrowRadius">@dimen/lb_page_indicator_arrow_radius</item>
        <item name="dotToDotGap">@dimen/lb_page_indicator_dot_gap</item>
        <item name="dotToArrowGap">@dimen/lb_page_indicator_arrow_gap</item>
        <item name="dotBgColor">@color/lb_page_indicator_dot</item>
        <item name="arrowBgColor">@color/lb_page_indicator_arrow_background</item>
    </style>
    <style name="Widget.Leanback.OnboardingStartButtonStyle" parent="Widget.Leanback.OnboardingStartButtonStyleBase">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">36dp</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:layout_marginBottom">4dp</item>
        <item name="android:background">@drawable/lb_onboarding_start_button_background</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:text">@string/lb_onboarding_get_started</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:textColor">#014269</item>
        <item name="android:textSize">16sp</item>
    </style>
    <style name="Widget.Leanback.OnboardingStartButtonStyleBase">
    </style>
    <style name="Widget.Leanback.OnboardingTitleStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0dp</item>
        <item name="android:layout_weight">0.5</item>
        <item name="android:layout_marginBottom">3dp</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">#EEEEEE</item>
        <item name="android:textSize">34sp</item>
        <item name="android:lineSpacingExtra">14sp</item>
    </style>
    <style name="Widget.Leanback.PickerStyle">
        <item name="pickerItemLayout">@layout/lb_picker_item</item>
    </style>
    <style name="Widget.Leanback.PickerStyle.DatePickerStyle"/>
    <style name="Widget.Leanback.PickerStyle.PinPickerStyle">
        <item name="pickerItemLayout">@layout/lb_pinpicker_item</item>
    </style>
    <style name="Widget.Leanback.PickerStyle.TimePickerStyle"/>
    <style name="Widget.Leanback.PlaybackControlLabelStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackControlLabel</item>
    </style>
    <style name="Widget.Leanback.PlaybackControlsActionIconsStyle">
        <item name="play">@drawable/lb_ic_play</item>
        <item name="pause">@drawable/lb_ic_pause</item>
        <item name="fast_forward">@drawable/lb_ic_fast_forward</item>
        <item name="rewind">@drawable/lb_ic_fast_rewind</item>
        <item name="skip_next">@drawable/lb_ic_skip_next</item>
        <item name="skip_previous">@drawable/lb_ic_skip_previous</item>
        <item name="thumb_up_outline">@drawable/lb_ic_thumb_up_outline</item>
        <item name="thumb_up">@drawable/lb_ic_thumb_up</item>
        <item name="thumb_down_outline">@drawable/lb_ic_thumb_down_outline</item>
        <item name="thumb_down">@drawable/lb_ic_thumb_down</item>
        <item name="repeat">@drawable/lb_ic_loop</item>
        <item name="repeat_one">@drawable/lb_ic_loop_one</item>
        <item name="shuffle">@drawable/lb_ic_shuffle</item>
        <item name="high_quality">@drawable/lb_ic_hq</item>
        <item name="closed_captioning">@drawable/lb_ic_cc</item>
        <item name="picture_in_picture">@drawable/lb_ic_pip</item>
    </style>
    <style name="Widget.Leanback.PlaybackControlsButtonStyle">
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
    </style>
    <style name="Widget.Leanback.PlaybackControlsTimeStyle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackControlsTime</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemDetailsStyle">
        <item name="android:paddingStart">?attr/playbackMediaItemPaddingStart</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemDurationStyle">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:singleLine">true</item>
        <item name="android:gravity">center_vertical|right</item>
        <item name="android:visibility">gone</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackMediaItemDuration</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemNameStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:singleLine">true</item>
        <item name="android:gravity">center_vertical</item>"
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackMediaItemName</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemNumberStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:visibility">gone</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackMediaItemNumber</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemNumberViewFlipperStyle">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemRowStyle" parent="Widget.Leanback.PlaybackRow">
        <item name="android:focusable">false</item>
        <item name="android:focusableInTouchMode">false</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaItemSeparatorStyle" parent="Widget.Leanback.PlaybackRow">
        <item name="android:background">@color/lb_media_background_color</item>
        <item name="android:src">@color/lb_playback_media_row_separator_highlight_color</item>
        <item name="android:layout_height">@dimen/lb_playback_media_row_separator_height</item>"
    </style>
    <style name="Widget.Leanback.PlaybackMediaListHeaderStyle" parent="Widget.Leanback.PlaybackRow">
        <item name="android:background">#263238</item>
        <item name="android:focusable">false</item>
        <item name="android:focusableInTouchMode">false</item>
    </style>
    <style name="Widget.Leanback.PlaybackMediaListHeaderTitleStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_alignParentTop">true</item>
        <item name="android:gravity">center_vertical</item>"
        <item name="android:paddingLeft">?attr/playbackMediaItemPaddingStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.PlaybackMediaListHeaderTitle</item>
    </style>
    <style name="Widget.Leanback.PlaybackRow">
        <item name="android:layout_marginStart">?attr/playbackPaddingStart</item>
        <item name="android:layout_marginEnd">?attr/playbackPaddingEnd</item>
        <item name="android:clipChildren">true</item>
        <item name="android:clipToPadding">true</item>
        <item name="android:foreground">@null</item>
        <item name="android:background">#384248</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
    </style>
    <style name="Widget.Leanback.Row">
    </style>
    <style name="Widget.Leanback.Row.Header" parent="Widget.Leanback.Header">
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Row.Header</item>
    </style>
    <style name="Widget.Leanback.Row.Header.Description" parent="Widget.Leanback.Header">
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Row.Header.Description</item>
    </style>
    <style name="Widget.Leanback.Row.HeaderDock">
        <item name="android:paddingStart">?attr/browsePaddingStart</item>
    </style>
    <style name="Widget.Leanback.Row.HorizontalGridView">
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:paddingStart">?attr/browsePaddingStart</item>
        <item name="android:paddingEnd">?attr/browsePaddingEnd</item>
        <item name="android:paddingBottom">@dimen/lb_browse_item_vertical_spacing</item>
        <item name="android:paddingTop">@dimen/lb_browse_item_vertical_spacing</item>
        <item name="android:horizontalSpacing">@dimen/lb_browse_item_horizontal_spacing</item>
        <item name="android:verticalSpacing">@dimen/lb_browse_item_vertical_spacing</item>
        <item name="focusOutFront">true</item>
    </style>
    <style name="Widget.Leanback.Row.HoverCardDescription">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Row.HoverCardDescription</item>
        <item name="android:maxWidth">@dimen/lb_browse_row_hovercard_max_width</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">4</item>
    </style>
    <style name="Widget.Leanback.Row.HoverCardTitle">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Row.HoverCardTitle</item>
        <item name="android:maxWidth">@dimen/lb_browse_row_hovercard_max_width</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
    </style>
    <style name="Widget.Leanback.Rows">
    </style>
    <style name="Widget.Leanback.Rows.VerticalGridView">
        <item name="android:paddingBottom">?attr/browsePaddingBottom</item>
        <item name="focusOutFront">true</item>
        <item name="focusOutEnd">true</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
    </style>
    <style name="Widget.Leanback.SearchOrbViewStyle">
        <item name="searchOrbIcon">?attr/defaultSearchIcon</item>
        <item name="searchOrbColor">?attr/defaultSearchColor</item>
        <item name="searchOrbIconColor">?attr/defaultSearchIconColor</item>
        <item name="searchOrbBrightColor">?attr/defaultSearchBrightColor</item>
    </style>
    <style name="Widget.Leanback.Title"/>
    <style name="Widget.Leanback.Title.Icon">
    </style>
    <style name="Widget.Leanback.Title.Text">
        <item name="android:singleLine">true</item>
        <item name="android:gravity">end</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:textAppearance">@style/TextAppearance.Leanback.Title</item>
    </style>
    <style name="Widget.Leanback.TitleView">
        <item name="android:paddingTop">?attr/browsePaddingTop</item>
        <item name="android:paddingBottom">?attr/browsePaddingTop</item>
        <item name="android:paddingStart">?attr/browsePaddingStart</item>
        <item name="android:paddingEnd">?attr/browsePaddingEnd</item>
    </style>
    <style name="Widget.LeanbackBase" parent="android:Widget.Holo"/>
    <style name="Widget.Support.CoordinatorLayout" parent="android:Widget">
        <item name="statusBarBackground">#000000</item>
    </style>
    <declare-styleable name="ActionBar">
        
        <attr name="navigationMode">
            <!-- Normal static title text -->
            <enum name="normal" value="0"/>
            <!-- The action bar will use a selection list for navigation. -->
            <enum name="listMode" value="1"/>
            <!-- The action bar will use a series of horizontal tabs for navigation. -->
            <enum name="tabMode" value="2"/>
        </attr>
        
        <attr name="displayOptions">
            <flag name="none" value="0"/>
            <flag name="useLogo" value="0x1"/>
            <flag name="showHome" value="0x2"/>
            <flag name="homeAsUp" value="0x4"/>
            <flag name="showTitle" value="0x8"/>
            <flag name="showCustom" value="0x10"/>
            <flag name="disableHome" value="0x20"/>
        </attr>
        
        <attr name="title"/>
        
        <attr format="string" name="subtitle"/>
        
        <attr format="reference" name="titleTextStyle"/>
        
        <attr format="reference" name="subtitleTextStyle"/>
        
        <attr format="reference" name="icon"/>
        
        <attr format="reference" name="logo"/>
        
        <attr format="reference" name="divider"/>
        
        <attr format="reference" name="background"/>
        
        <attr format="reference|color" name="backgroundStacked"/>
        
        <attr format="reference|color" name="backgroundSplit"/>
        
        <attr format="reference" name="customNavigationLayout"/>
        
        <attr name="height"/>
        
        <attr format="reference" name="homeLayout"/>
        
        <attr format="reference" name="progressBarStyle"/>
        
        <attr format="reference" name="indeterminateProgressStyle"/>
        
        <attr format="dimension" name="progressBarPadding"/>
        
        <attr name="homeAsUpIndicator"/>
        
        <attr format="dimension" name="itemPadding"/>
        
        <attr format="boolean" name="hideOnContentScroll"/>
        
        <attr format="dimension" name="contentInsetStart"/>
        
        <attr format="dimension" name="contentInsetEnd"/>
        
        <attr format="dimension" name="contentInsetLeft"/>
        
        <attr format="dimension" name="contentInsetRight"/>
        
        <attr format="dimension" name="contentInsetStartWithNavigation"/>
        
        <attr format="dimension" name="contentInsetEndWithActions"/>
        
        <attr format="dimension" name="elevation"/>
        
        <attr format="reference" name="popupTheme"/>
    </declare-styleable>
    <declare-styleable name="ActionBarLayout">
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuItemView">
        <attr name="android:minWidth"/>
    </declare-styleable>
    <declare-styleable name="ActionMenuView">
        
    </declare-styleable>
    <declare-styleable name="ActionMode">
        
        <attr name="titleTextStyle"/>
        
        <attr name="subtitleTextStyle"/>
        
        <attr name="background"/>
        
        <attr name="backgroundSplit"/>
        
        <attr name="height"/>
        
        <attr format="reference" name="closeItemLayout"/>
    </declare-styleable>
    <declare-styleable name="ActivityChooserView">
        
        <attr format="string" name="initialActivityCount"/>
        
        <attr format="reference" name="expandActivityOverflowButtonDrawable"/>
    </declare-styleable>
    <declare-styleable name="ActivityNavigator">
        <attr name="android:name"/>
        <attr format="string" name="action"/>
        <attr format="string" name="data"/>
        <attr format="string" name="dataPattern"/>
        <attr format="string" name="targetPackage"/>
    </declare-styleable>
    <declare-styleable name="AlertDialog">
        <attr name="android:layout"/>
        <attr format="reference" name="buttonPanelSideLayout"/>
        <attr format="reference" name="listLayout"/>
        <attr format="reference" name="multiChoiceItemLayout"/>
        <attr format="reference" name="singleChoiceItemLayout"/>
        <attr format="reference" name="listItemLayout"/>
        <attr format="boolean" name="showTitle"/>
        <attr format="dimension" name="buttonIconDimen"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableCompat">
        
        <attr name="android:visible"/>
        
        <attr name="android:variablePadding"/>
        
        <attr name="android:constantSize"/>
        
        <attr name="android:dither"/>
        
        <attr name="android:enterFadeDuration"/>
        
        <attr name="android:exitFadeDuration"/>
        
        
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableItem">
        
        <attr name="android:drawable"/>
        
        <attr name="android:id"/>
    </declare-styleable>
    <declare-styleable name="AnimatedStateListDrawableTransition">
        
        <attr name="android:fromId"/>
        
        <attr name="android:toId"/>
        
        <attr name="android:drawable"/>
        
        <attr name="android:reversible"/>
    </declare-styleable>
    <declare-styleable name="AppCompatImageView">
        <attr name="android:src"/>
        
        <attr format="reference" name="srcCompat"/>

        
        <attr format="color" name="tint"/>

        
        <attr name="tintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatSeekBar">
        <attr name="android:thumb"/>
        
        <attr format="reference" name="tickMark"/>
        
        <attr format="color" name="tickMarkTint"/>
        
        <attr name="tickMarkTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="AppCompatTextHelper">
        <attr name="android:drawableLeft"/>
        <attr name="android:drawableTop"/>
        <attr name="android:drawableRight"/>
        <attr name="android:drawableBottom"/>
        <attr name="android:drawableStart"/>
        <attr name="android:drawableEnd"/>
        <attr name="android:textAppearance"/>
    </declare-styleable>
    <declare-styleable name="AppCompatTextView">
        
        <attr format="reference|boolean" name="textAllCaps"/>
        <attr name="android:textAppearance"/>
        
        <attr format="enum" name="autoSizeTextType">
            <!-- No auto-sizing (default). -->
            <enum name="none" value="0"/>
            <!-- Uniform horizontal and vertical text size scaling to fit within the
            container. -->
            <enum name="uniform" value="1"/>
        </attr>
        
        <attr format="dimension" name="autoSizeStepGranularity"/>
        
        <attr format="reference" name="autoSizePresetSizes"/>
        
        <attr format="dimension" name="autoSizeMinTextSize"/>
        
        <attr format="dimension" name="autoSizeMaxTextSize"/>
        
        <attr format="string" name="fontFamily"/>
        
        <attr format="dimension" name="lineHeight"/>
        
        <attr format="dimension" name="firstBaselineToTopHeight"/>
        
        <attr format="dimension" name="lastBaselineToBottomHeight"/>
    </declare-styleable>
    <declare-styleable name="AppCompatTheme">

        
        
        
        <eat-comment/>

        
        <attr format="boolean" name="windowActionBar"/>

        
        <attr format="boolean" name="windowNoTitle"/>

        
        <attr format="boolean" name="windowActionBarOverlay"/>

        
        <attr format="boolean" name="windowActionModeOverlay"/>

        
        <attr format="dimension|fraction" name="windowFixedWidthMajor"/>
        
        <attr format="dimension|fraction" name="windowFixedHeightMinor"/>

        
        <attr format="dimension|fraction" name="windowFixedWidthMinor"/>
        
        <attr format="dimension|fraction" name="windowFixedHeightMajor"/>

        
        <attr format="dimension|fraction" name="windowMinWidthMajor"/>
        
        <attr format="dimension|fraction" name="windowMinWidthMinor"/>

        <attr name="android:windowIsFloating"/>
        <attr name="android:windowAnimationStyle"/>

        
        
        
        <eat-comment/>
        
        <attr format="reference" name="actionBarTabStyle"/>
        <attr format="reference" name="actionBarTabBarStyle"/>
        <attr format="reference" name="actionBarTabTextStyle"/>
        <attr format="reference" name="actionOverflowButtonStyle"/>
        <attr format="reference" name="actionOverflowMenuStyle"/>
        
        <attr format="reference" name="actionBarPopupTheme"/>
        
        <attr format="reference" name="actionBarStyle"/>
        
        <attr format="reference" name="actionBarSplitStyle"/>
        
        <attr format="reference" name="actionBarTheme"/>
        
        <attr format="reference" name="actionBarWidgetTheme"/>
        
        <attr format="dimension" name="actionBarSize">
            <enum name="wrap_content" value="0"/>
        </attr>
        
        <attr format="reference" name="actionBarDivider"/>
        
        <attr format="reference" name="actionBarItemBackground"/>
        
        <attr format="reference" name="actionMenuTextAppearance"/>
        
        
        <attr format="color|reference" name="actionMenuTextColor"/>


        
        
        
        <eat-comment/>
        <attr format="reference" name="actionModeStyle"/>
        <attr format="reference" name="actionModeCloseButtonStyle"/>
        
        <attr format="reference" name="actionModeBackground"/>
        
        <attr format="reference" name="actionModeSplitBackground"/>
        
        <attr format="reference" name="actionModeCloseDrawable"/>
        
        <attr format="reference" name="actionModeCutDrawable"/>
        
        <attr format="reference" name="actionModeCopyDrawable"/>
        
        <attr format="reference" name="actionModePasteDrawable"/>
        
        <attr format="reference" name="actionModeSelectAllDrawable"/>
        
        <attr format="reference" name="actionModeShareDrawable"/>
        
        <attr format="reference" name="actionModeFindDrawable"/>
        
        <attr format="reference" name="actionModeWebSearchDrawable"/>

        
        <attr format="reference" name="actionModePopupWindowStyle"/>


        
        
        
        <eat-comment/>
        
        <attr format="reference" name="textAppearanceLargePopupMenu"/>
        
        <attr format="reference" name="textAppearanceSmallPopupMenu"/>
        
        <attr format="reference" name="textAppearancePopupMenuHeader"/>


        
        
        
        <eat-comment/>

        
        <attr format="reference" name="dialogTheme"/>
        
        <attr format="dimension" name="dialogPreferredPadding"/>
        
        <attr format="reference" name="listDividerAlertDialog"/>
        
        <attr format="dimension" name="dialogCornerRadius"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="actionDropDownStyle"/>
        
        <attr format="dimension" name="dropdownListPreferredItemHeight"/>
        
        <attr format="reference" name="spinnerDropDownItemStyle"/>
        
        <attr format="reference" name="homeAsUpIndicator"/>

        
        <attr format="reference" name="actionButtonStyle"/>

        
        <attr format="reference" name="buttonBarStyle"/>
        
        <attr format="reference" name="buttonBarButtonStyle"/>
        
        <attr format="reference" name="selectableItemBackground"/>
        
        <attr format="reference" name="selectableItemBackgroundBorderless"/>
        
        <attr format="reference" name="borderlessButtonStyle"/>
        
        <attr format="reference" name="dividerVertical"/>
        
        <attr format="reference" name="dividerHorizontal"/>
        
        <attr format="reference" name="activityChooserViewStyle"/>

        
        <attr format="reference" name="toolbarStyle"/>
        
        <attr format="reference" name="toolbarNavigationButtonStyle"/>

        
        <attr format="reference" name="popupMenuStyle"/>
        
        <attr format="reference" name="popupWindowStyle"/>

        
        <attr format="reference|color" name="editTextColor"/>
        
        <attr format="reference" name="editTextBackground"/>

        
        <attr format="reference" name="imageButtonStyle"/>

        
        
        
        <eat-comment/>
        
        <attr format="reference" name="textAppearanceSearchResultTitle"/>
        
        <attr format="reference" name="textAppearanceSearchResultSubtitle"/>
        
        <attr format="reference|color" name="textColorSearchUrl"/>
        
        <attr format="reference" name="searchViewStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="dimension" name="listPreferredItemHeight"/>
        
        <attr format="dimension" name="listPreferredItemHeightSmall"/>
        
        <attr format="dimension" name="listPreferredItemHeightLarge"/>

        
        <attr format="dimension" name="listPreferredItemPaddingLeft"/>
        
        <attr format="dimension" name="listPreferredItemPaddingRight"/>

        
        <attr format="reference" name="dropDownListViewStyle"/>
        <attr format="reference" name="listPopupWindowStyle"/>

        
        <attr format="reference" name="textAppearanceListItem"/>
        
        <attr format="reference" name="textAppearanceListItemSecondary"/>
        
        <attr format="reference" name="textAppearanceListItemSmall"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="panelBackground"/>
        
        <attr format="dimension" name="panelMenuListWidth"/>
        
        <attr format="reference" name="panelMenuListTheme"/>
        
        <attr format="reference" name="listChoiceBackgroundIndicator"/>

        
        
        
        <eat-comment/>

        
        <attr format="color" name="colorPrimary"/>

        
        <attr format="color" name="colorPrimaryDark"/>

        
        <attr format="color" name="colorAccent"/>

        
        <attr format="color" name="colorControlNormal"/>

        
        <attr format="color" name="colorControlActivated"/>

        
        <attr format="color" name="colorControlHighlight"/>

        
        <attr format="color" name="colorButtonNormal"/>

        
        <attr format="color" name="colorSwitchThumbNormal"/>

        
        <attr format="reference" name="controlBackground"/>

        
        <attr format="color" name="colorBackgroundFloating"/>

        
        
        
        <eat-comment/>
        <attr format="reference" name="alertDialogStyle"/>
        <attr format="reference" name="alertDialogButtonGroupStyle"/>
        <attr format="boolean" name="alertDialogCenterButtons"/>
        
        <attr format="reference" name="alertDialogTheme"/>

        
        <attr format="reference|color" name="textColorAlertDialogListItem"/>

        
        <attr format="reference" name="buttonBarPositiveButtonStyle"/>

        
        <attr format="reference" name="buttonBarNegativeButtonStyle"/>

        
        <attr format="reference" name="buttonBarNeutralButtonStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="autoCompleteTextViewStyle"/>
        
        <attr format="reference" name="buttonStyle"/>
        
        <attr format="reference" name="buttonStyleSmall"/>
        
        <attr format="reference" name="checkboxStyle"/>
        
        <attr format="reference" name="checkedTextViewStyle"/>
        
        <attr format="reference" name="editTextStyle"/>
        
        <attr format="reference" name="radioButtonStyle"/>
        
        <attr format="reference" name="ratingBarStyle"/>
        
        <attr format="reference" name="ratingBarStyleIndicator"/>
        
        <attr format="reference" name="ratingBarStyleSmall"/>
        
        <attr format="reference" name="seekBarStyle"/>
        
        <attr format="reference" name="spinnerStyle"/>
        
        <attr format="reference" name="switchStyle"/>

        
        <attr format="reference" name="listMenuViewStyle"/>

        
        
        
        <eat-comment/>

        
        <attr format="reference" name="tooltipFrameBackground"/>
        
        <attr format="reference|color" name="tooltipForegroundColor"/>

        
        <attr format="reference|color" name="colorError"/>

        <attr format="string" name="viewInflaterClass"/>
    </declare-styleable>
    <declare-styleable name="ButtonBarLayout">
        
        <attr format="boolean" name="allowStacking"/>
    </declare-styleable>
    <declare-styleable name="Capability">
        
        <attr format="reference" name="queryPatterns"/>
        
        <attr format="boolean" name="shortcutMatchRequired"/>
    </declare-styleable>
    <declare-styleable name="ColorStateListItem">
        
        <attr name="android:color"/>
        
        <attr format="float" name="alpha"/>
        <attr name="android:alpha"/>
        
        <attr format="float" name="lStar"/>
        <attr name="android:lStar"/>
    </declare-styleable>
    <declare-styleable name="CompoundButton">
        <attr name="android:button"/>
        
        <attr format="color" name="buttonTint"/>

        
        <attr name="buttonTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout">
        
        <attr format="reference" name="keylines"/>
        
        <attr format="color|reference" name="statusBarBackground"/>
    </declare-styleable>
    <declare-styleable name="CoordinatorLayout_Layout">
        <attr name="android:layout_gravity"/>
        
        <attr format="string" name="layout_behavior"/>
        
        <attr format="reference" name="layout_anchor"/>
        
        <attr format="integer" name="layout_keyline"/>

        
        <attr name="layout_anchorGravity">
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
            <!-- Push object to the left of its container, not changing its size. -->
            <flag name="left" value="0x03"/>
            <!-- Push object to the right of its container, not changing its size. -->
            <flag name="right" value="0x05"/>
            <!-- Place object in the vertical center of its container, not changing its size. -->
            <flag name="center_vertical" value="0x10"/>
            <!-- Grow the vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill_vertical" value="0x70"/>
            <!-- Place object in the horizontal center of its container, not changing its size. -->
            <flag name="center_horizontal" value="0x01"/>
            <!-- Grow the horizontal size of the object if needed so it completely fills its container. -->
            <flag name="fill_horizontal" value="0x07"/>
            <!-- Place the object in the center of its container in both the vertical and horizontal axis, not changing its size. -->
            <flag name="center" value="0x11"/>
            <!-- Grow the horizontal and vertical size of the object if needed so it completely fills its container. -->
            <flag name="fill" value="0x77"/>
            <!-- Additional option that can be set to have the top and/or bottom edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the vertical gravity: a top gravity will clip the bottom
                 edge, a bottom gravity will clip the top edge, and neither will clip both edges. -->
            <flag name="clip_vertical" value="0x80"/>
            <!-- Additional option that can be set to have the left and/or right edges of
                 the child clipped to its container's bounds.
                 The clip will be based on the horizontal gravity: a left gravity will clip the right
                 edge, a right gravity will clip the left edge, and neither will clip both edges. -->
            <flag name="clip_horizontal" value="0x08"/>
            <!-- Push object to the beginning of its container, not changing its size. -->
            <flag name="start" value="0x00800003"/>
            <!-- Push object to the end of its container, not changing its size. -->
            <flag name="end" value="0x00800005"/>
        </attr>

        
        <attr format="enum" name="layout_insetEdge">
            <!-- Don't inset. -->
            <enum name="none" value="0x0"/>
            <!-- Inset the top edge. -->
            <enum name="top" value="0x30"/>
            <!-- Inset the bottom edge. -->
            <enum name="bottom" value="0x50"/>
            <!-- Inset the left edge. -->
            <enum name="left" value="0x03"/>
            <!-- Inset the right edge. -->
            <enum name="right" value="0x05"/>
            <!-- Inset the start edge. -->
            <enum name="start" value="0x00800003"/>
            <!-- Inset the end edge. -->
            <enum name="end" value="0x00800005"/>
        </attr>
        
        <attr name="layout_dodgeInsetEdges">
            <!-- Don't dodge any edges -->
            <flag name="none" value="0x0"/>
            <!-- Dodge the top inset edge. -->
            <flag name="top" value="0x30"/>
            <!-- Dodge the bottom inset edge. -->
            <flag name="bottom" value="0x50"/>
            <!-- Dodge the left inset edge. -->
            <flag name="left" value="0x03"/>
            <!-- Dodge the right inset edge. -->
            <flag name="right" value="0x05"/>
            <!-- Dodge the start inset edge. -->
            <flag name="start" value="0x00800003"/>
            <!-- Dodge the end inset edge. -->
            <flag name="end" value="0x00800005"/>
            <!-- Dodge all the inset edges. -->
            <flag name="all" value="0x77"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="DrawerArrowToggle">
        
        <attr format="color" name="color"/>
        
        <attr format="boolean" name="spinBars"/>
        
        <attr format="dimension" name="drawableSize"/>
        
        <attr format="dimension" name="gapBetweenBars"/>
        
        <attr format="dimension" name="arrowHeadLength"/>
        
        <attr format="dimension" name="arrowShaftLength"/>
        
        <attr format="dimension" name="barLength"/>
        
        <attr format="dimension" name="thickness"/>
    </declare-styleable>
    <declare-styleable name="FontFamily">
        
        <attr format="string" name="fontProviderAuthority"/>
        
        <attr format="string" name="fontProviderPackage"/>
        
        <attr format="string" name="fontProviderQuery"/>
        
        <attr format="string" name="fontProviderFallbackQuery"/>
        
        <attr format="reference" name="fontProviderCerts"/>
        
        <attr name="fontProviderFetchStrategy">
            <!-- The blocking font fetch works as follows.
              First, check the local cache, then if the requested font is not cached, request the
              font from the provider and wait until it is finished.  You can change the length of
              the timeout by modifying fontProviderFetchTimeout.  If the timeout happens, the
              default typeface will be used instead. -->
            <enum name="blocking" value="0"/>
            <!-- The async font fetch works as follows.
              First, check the local cache, then if the requested font is not cached, trigger a
              request the font and continue with layout inflation. Once the font fetch succeeds, the
              target text view will be refreshed with the downloaded font data. The
              fontProviderFetchTimeout will be ignored if async loading is specified. -->
            <enum name="async" value="1"/>
        </attr>
        
        <attr format="integer" name="fontProviderFetchTimeout">
            <!-- A special value for the timeout. In this case, the blocking font fetching will not
              timeout and wait until a reply is received from the font provider. -->
            <enum name="forever" value="-1"/>
        </attr>
        
        <attr format="string" name="fontProviderSystemFontFamily"/>
    </declare-styleable>
    <declare-styleable name="FontFamilyFont">
        
        <attr name="fontStyle">
            <enum name="normal" value="0"/>
            <enum name="italic" value="1"/>
        </attr>
        
        <attr format="reference" name="font"/>
        
        <attr format="integer" name="fontWeight"/>
        
        <attr format="string" name="fontVariationSettings"/>
        
        <attr format="integer" name="ttcIndex"/>
        
        <attr name="android:fontStyle"/>
        <attr name="android:font"/>
        <attr name="android:fontWeight"/>
        <attr name="android:fontVariationSettings"/>
        <attr name="android:ttcIndex"/>
    </declare-styleable>
    <declare-styleable name="GradientColor">
        
        <attr name="android:startColor"/>
        
        <attr name="android:centerColor"/>
        
        <attr name="android:endColor"/>
        
        <attr name="android:type"/>

        
        
        <attr name="android:gradientRadius"/>

        
        
        <attr name="android:centerX"/>
        
        <attr name="android:centerY"/>

        
        
        <attr name="android:startX"/>
        
        <attr name="android:startY"/>
        
        <attr name="android:endX"/>
        
        <attr name="android:endY"/>

        
        <attr name="android:tileMode"/>
    </declare-styleable>
    <declare-styleable name="GradientColorItem">
        
        <attr name="android:offset"/>
        
        <attr name="android:color"/>
    </declare-styleable>
    <declare-styleable name="LeanbackGuidedStepTheme">

        
        <attr format="reference" name="guidedStepTheme"/>
        
        <attr format="float" name="guidedStepHeightWeight"/>

        
        <attr format="float" name="guidedStepKeyline"/>

        
        <attr format="boolean" name="guidedStepThemeFlag"/>

        
        <attr format="reference|color" name="guidedStepBackground"/>

        
        <attr format="reference" name="guidedStepImeAppearingAnimation"/>
        
        <attr format="reference" name="guidedStepImeDisappearingAnimation"/>

        
        <attr format="reference" name="guidanceContainerStyle"/>
        
        <attr format="reference" name="guidanceTitleStyle"/>
        
        <attr format="reference" name="guidanceDescriptionStyle"/>
        
        <attr format="reference" name="guidanceBreadcrumbStyle"/>
        
        <attr format="reference" name="guidanceIconStyle"/>

        
        <attr format="reference" name="guidedActionsSelectorDrawable"/>

        
        <attr format="dimension|reference" name="guidedActionsElevation"/>

        
        <attr format="dimension|reference" name="guidedActionsShadowWidth"/>

        
        <attr format="reference" name="guidedActionsBackground"/>

        
        <attr format="reference" name="guidedActionsBackgroundDark"/>

        
        <attr format="reference" name="guidedActionsListStyle"/>

        
        <attr format="reference" name="guidedSubActionsListStyle"/>

        
        <attr format="reference" name="guidedButtonActionsListStyle"/>

        
        <attr format="reference" name="guidedActionItemContainerStyle"/>
        
        <attr format="reference" name="guidedActionItemCheckmarkStyle"/>
        
        <attr format="reference" name="guidedActionItemIconStyle"/>
        
        <attr format="reference" name="guidedActionItemContentStyle"/>
        
        <attr format="reference" name="guidedActionItemTitleStyle"/>
        
        <attr format="reference" name="guidedActionItemDescriptionStyle"/>
        
        <attr format="reference" name="guidedActionItemChevronStyle"/>

        
        <attr format="reference" name="guidedActionPressedAnimation"/>
        
        <attr format="reference" name="guidedActionUnpressedAnimation"/>
        
        <attr format="reference" name="guidedActionEnabledChevronAlpha"/>
        
        <attr format="reference" name="guidedActionDisabledChevronAlpha"/>
        
        <attr format="reference" name="guidedActionContentWidthWeight"/>
        
        <attr format="reference" name="guidedActionContentWidthWeightTwoPanels"/>
        
        <attr format="reference" name="guidedButtonActionsWidthWeight"/>
        
        <attr format="reference" name="guidedActionTitleMinLines"/>
        
        <attr format="reference" name="guidedActionTitleMaxLines"/>
        
        <attr format="reference" name="guidedActionDescriptionMinLines"/>
        
        <attr format="reference" name="guidedActionVerticalPadding"/>

        
        <attr format="reference" name="guidedActionsContainerStyle"/>
        
        <attr format="reference" name="guidedActionsSelectorStyle"/>
        
        <attr format="reference" name="guidedStepEntryAnimation"/>
        
        <attr format="reference" name="guidedStepExitAnimation"/>
        
        <attr format="reference" name="guidedStepReentryAnimation"/>
        
        <attr format="reference" name="guidedStepReturnAnimation"/>
        
        <attr format="reference" name="guidanceEntryAnimation"/>
        
        <attr format="reference" name="guidedActionsEntryAnimation"/>
        
        <attr format="reference" name="guidedActionsSelectorShowAnimation"/>
        
        <attr format="reference" name="guidedActionsSelectorHideAnimation"/>
        
        <attr format="reference" name="guidedActionCheckedAnimation"/>
        
        <attr format="reference" name="guidedActionUncheckedAnimation"/>
        
        <attr format="reference" name="guidedActionContentWidth"/>
        
        <attr format="reference" name="guidedActionContentWidthNoIcon"/>
    </declare-styleable>
    <declare-styleable name="LeanbackOnboardingTheme">
        
        <attr format="reference" name="onboardingTheme"/>

        
        <attr format="reference" name="onboardingHeaderStyle"/>
        
        <attr format="reference" name="onboardingTitleStyle"/>
        
        <attr format="reference" name="onboardingDescriptionStyle"/>

        
        <attr format="reference" name="onboardingNavigatorContainerStyle"/>
        
        <attr format="reference" name="onboardingPageIndicatorStyle"/>
        
        <attr format="reference" name="onboardingStartButtonStyle"/>

        
        <attr format="reference" name="onboardingLogoStyle"/>

        
        <attr format="reference" name="onboardingMainIconStyle"/>
    </declare-styleable>
    <declare-styleable name="LeanbackTheme">

        
        <attr format="dimension" name="browsePaddingStart"/>
        
        <attr format="dimension" name="browsePaddingEnd"/>
        
        <attr format="dimension" name="browsePaddingTop"/>
        
        <attr format="dimension" name="browsePaddingBottom"/>
        
        <attr format="dimension" name="browseRowsMarginStart"/>
        
        <attr format="dimension" name="browseRowsMarginTop"/>
        
        <attr format="dimension" name="browseRowsFadingEdgeLength"/>

        
        <attr format="reference" name="browseTitleTextStyle"/>

        
        <attr format="reference" name="browseTitleIconStyle"/>

        
        <attr format="reference" name="browseTitleViewStyle"/>

        
        <attr format="reference" name="browseTitleViewLayout"/>

        
        <attr format="reference" name="headersVerticalGridStyle"/>
        
        <attr format="reference" name="headerStyle"/>
        
        <attr format="reference" name="sectionHeaderStyle"/>

        
        <attr format="reference" name="rowsVerticalGridStyle"/>

        
        <attr format="reference" name="rowHorizontalGridStyle"/>
        
        <attr format="reference" name="rowHeaderStyle"/>

        
        <attr format="reference" name="rowHeaderDescriptionStyle"/>

        
        <attr format="reference" name="rowHeaderDockStyle"/>

        
        <attr format="reference" name="rowHoverCardTitleStyle"/>
        
        <attr format="reference" name="rowHoverCardDescriptionStyle"/>

        
        <attr format="reference" name="baseCardViewStyle"/>
        <attr format="reference" name="imageCardViewStyle"/>
        <attr format="reference" name="imageCardViewImageStyle"/>
        <attr format="reference" name="imageCardViewTitleStyle"/>
        <attr format="reference" name="imageCardViewContentStyle"/>
        <attr format="reference" name="imageCardViewBadgeStyle"/>
        <attr format="reference" name="imageCardViewInfoAreaStyle"/>

        
        <attr format="reference" name="detailsDescriptionTitleStyle"/>
        <attr format="reference" name="detailsDescriptionSubtitleStyle"/>
        <attr format="reference" name="detailsDescriptionBodyStyle"/>
        <attr format="reference" name="detailsActionButtonStyle"/>

        
        <attr format="dimension" name="playbackPaddingStart"/>
        <attr format="dimension" name="playbackPaddingEnd"/>
        <attr format="dimension" name="playbackMediaItemPaddingStart"/>

        <attr format="reference" name="playbackMediaListHeaderStyle"/>
        <attr format="reference" name="playbackMediaItemRowStyle"/>
        <attr format="reference" name="playbackMediaItemSeparatorStyle"/>

        <attr format="reference" name="playbackMediaListHeaderTitleStyle"/>
        <attr format="reference" name="playbackMediaItemDetailsStyle"/>
        <attr format="reference" name="playbackMediaItemNumberViewFlipperStyle"/>
        <attr format="reference" name="playbackMediaItemNumberViewFlipperLayout"/>
        <attr format="reference" name="playbackMediaItemNumberStyle"/>
        <attr format="reference" name="playbackMediaItemNameStyle"/>
        <attr format="reference" name="playbackMediaItemDurationStyle"/>

        <attr format="reference" name="playbackControlsButtonStyle"/>
        <attr format="reference" name="playbackControlButtonLabelStyle"/>
        <attr format="reference" name="playbackControlsTimeStyle"/>

        
        <attr format="reference" name="itemsVerticalGridStyle"/>

        
        <attr format="reference" name="errorMessageStyle"/>

        
        <attr format="reference|color" name="defaultBrandColor"/>
        
        <attr format="reference|color" name="defaultBrandColorDark"/>

        
        <attr format="reference|color" name="defaultSearchColor"/>
        
        <attr format="reference|color" name="defaultSearchIconColor"/>
        
        <attr format="reference|color" name="defaultSearchBrightColor"/>
        
        <attr format="reference|color" name="defaultSectionHeaderColor"/>

        
        <attr format="reference" name="searchOrbViewStyle"/>
        <attr format="reference" name="defaultSearchIcon"/>

        <attr format="reference|color" name="playbackProgressPrimaryColor"/>
        <attr format="reference|color" name="playbackProgressSecondaryColor"/>
        <attr format="reference|color" name="playbackControlsIconHighlightColor"/>
        <attr format="reference" name="playbackControlsActionIcons"/>
        
        <attr format="reference|integer" name="playbackControlsAutoHideTimeout"/>
        
        <attr format="reference|integer" name="playbackControlsAutoHideTickleTimeout"/>

        
        <attr format="color" name="overlayDimMaskColor"/>
        
        <attr format="fraction" name="overlayDimActiveLevel"/>
        
        <attr format="fraction" name="overlayDimDimmedLevel"/>

        
        <attr format="reference" name="pickerStyle"/>
        <attr format="reference" name="datePickerStyle"/>
        <attr format="reference" name="timePickerStyle"/>
        <attr format="reference" name="pinPickerStyle"/>
    </declare-styleable>
    <declare-styleable name="LinearLayoutCompat">
        
        <attr name="android:orientation"/>
        <attr name="android:gravity"/>
        
        <attr name="android:baselineAligned"/>
        
        <attr name="android:baselineAlignedChildIndex"/>
        
        <attr name="android:weightSum"/>
        
        <attr format="boolean" name="measureWithLargestChild"/>
        
        <attr name="divider"/>
        
        <attr name="showDividers">
            <flag name="none" value="0"/>
            <flag name="beginning" value="1"/>
            <flag name="middle" value="2"/>
            <flag name="end" value="4"/>
        </attr>
        
        <attr format="dimension" name="dividerPadding"/>
    </declare-styleable>
    <declare-styleable name="LinearLayoutCompat_Layout">
        <attr name="android:layout_width"/>
        <attr name="android:layout_height"/>
        <attr name="android:layout_weight"/>
        <attr name="android:layout_gravity"/>
    </declare-styleable>
    <declare-styleable name="ListPopupWindow">
        
        <attr name="android:dropDownVerticalOffset"/>
        
        <attr name="android:dropDownHorizontalOffset"/>
    </declare-styleable>
    <declare-styleable name="MenuGroup">

        
        <attr name="android:id"/>

        
        <attr name="android:menuCategory"/>

        
        <attr name="android:orderInCategory"/>

        
        <attr name="android:checkableBehavior"/>

        
        <attr name="android:visible"/>

        
        <attr name="android:enabled"/>

    </declare-styleable>
    <declare-styleable name="MenuItem">

        
        <attr name="android:id"/>

        
        <attr name="android:menuCategory"/>

        
        <attr name="android:orderInCategory"/>

        
        <attr name="android:title"/>

        
        <attr name="android:titleCondensed"/>

        
        <attr name="android:icon"/>

        
        <attr name="android:alphabeticShortcut"/>

        
        <attr name="alphabeticModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        
        <attr name="android:numericShortcut"/>

        
        <attr name="numericModifiers">
            <flag name="META" value="0x10000"/>
            <flag name="CTRL" value="0x1000"/>
            <flag name="ALT" value="0x02"/>
            <flag name="SHIFT" value="0x1"/>
            <flag name="SYM" value="0x4"/>
            <flag name="FUNCTION" value="0x8"/>
        </attr>

        
        <attr name="android:checkable"/>

        
        <attr name="android:checked"/>

        
        <attr name="android:visible"/>

        
        <attr name="android:enabled"/>

        
        <attr name="android:onClick"/>

        
        <attr name="showAsAction">
            <!-- Never show this item in an action bar, show it in the overflow menu instead.
                 Mutually exclusive with "ifRoom" and "always". -->
            <flag name="never" value="0"/>
            <!-- Show this item in an action bar if there is room for it as determined
                 by the system. Favor this option over "always" where possible.
                 Mutually exclusive with "never" and "always". -->
            <flag name="ifRoom" value="1"/>
            <!-- Always show this item in an actionbar, even if it would override
                 the system's limits of how much stuff to put there. This may make
                 your action bar look bad on some screens. In most cases you should
                 use "ifRoom" instead. Mutually exclusive with "ifRoom" and "never". -->
            <flag name="always" value="2"/>
            <!-- When this item is shown as an action in the action bar, show a text
                 label with it even if it has an icon representation. -->
            <flag name="withText" value="4"/>
            <!-- This item's action view collapses to a normal menu
                 item. When expanded, the action view takes over a
                 larger segment of its container. -->
            <flag name="collapseActionView" value="8"/>
        </attr>

        
        <attr format="reference" name="actionLayout"/>

        
        <attr format="string" name="actionViewClass"/>

        
        <attr format="string" name="actionProviderClass"/>

        
        <attr format="string" name="contentDescription"/>

        
        <attr format="string" name="tooltipText"/>

        
        <attr format="color" name="iconTint"/>

        
        <attr name="iconTintMode">
            <!-- The tint is drawn on top of the icon.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the icon. The icon’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the icon, but with the icon’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the icon with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>

    </declare-styleable>
    <declare-styleable name="MenuView">
        
        <attr name="android:itemTextAppearance"/>
        
        <attr name="android:horizontalDivider"/>
        
        <attr name="android:verticalDivider"/>
        
        <attr name="android:headerBackground"/>
        
        <attr name="android:itemBackground"/>
        
        <attr name="android:windowAnimationStyle"/>
        
        <attr name="android:itemIconDisabledAlpha"/>
        
        <attr format="boolean" name="preserveIconSpacing"/>
        
        <attr format="reference" name="subMenuArrow"/>
    </declare-styleable>
    <declare-styleable name="NavAction">
        <attr name="android:id"/>
        <attr format="reference" name="destination"/>
        <attr format="boolean" name="launchSingleTop"/>
        <attr format="boolean" name="restoreState"/>
        <attr format="reference" name="popUpTo"/>
        <attr format="boolean" name="popUpToInclusive"/>
        <attr format="boolean" name="popUpToSaveState"/>
        <attr format="reference" name="enterAnim"/>
        <attr format="reference" name="exitAnim"/>
        <attr format="reference" name="popEnterAnim"/>
        <attr format="reference" name="popExitAnim"/>
    </declare-styleable>
    <declare-styleable name="NavArgument">
        <attr name="android:name"/>
        <attr name="android:defaultValue"/>
        <attr format="boolean" name="nullable"/>
        <attr format="string" name="argType"/>
    </declare-styleable>
    <declare-styleable name="NavDeepLink">
        <attr format="string" name="uri"/>
        <attr format="string" name="action"/>
        <attr format="string" name="mimeType"/>
        <attr name="android:autoVerify"/>
    </declare-styleable>
    <declare-styleable name="NavGraphNavigator">
        <attr format="reference" name="startDestination"/>
    </declare-styleable>
    <declare-styleable name="NavHost">
        <attr format="reference" name="navGraph"/>
    </declare-styleable>
    <declare-styleable name="NavInclude">
        <attr format="reference" name="graph"/>
    </declare-styleable>
    <declare-styleable name="Navigator">
        <attr name="android:id"/>
        <attr format="string" name="route"/>
        <attr name="android:label"/>
    </declare-styleable>
    <declare-styleable name="PagingIndicator">
        
        <attr format="reference" name="lbDotRadius"/>
        
        <attr format="reference" name="arrowRadius"/>
        
        <attr format="reference" name="dotToDotGap"/>
        
        <attr format="reference" name="dotToArrowGap"/>
        
        <attr format="reference" name="dotBgColor"/>
        
        <attr format="reference" name="arrowColor"/>
        
        <attr format="reference" name="arrowBgColor"/>
    </declare-styleable>
    <declare-styleable name="PopupWindow">
        
        <attr format="boolean" name="overlapAnchor"/>
        <attr name="android:popupBackground"/>
        <attr name="android:popupAnimationStyle"/>
    </declare-styleable>
    <declare-styleable name="PopupWindowBackgroundState">
        
        <attr format="boolean" name="state_above_anchor"/>
    </declare-styleable>
    <declare-styleable name="RecycleListView">
        
        <attr format="dimension" name="paddingBottomNoButtons"/>
        
        <attr format="dimension" name="paddingTopNoTitle"/>
    </declare-styleable>
    <declare-styleable name="RecyclerView">
        
        <attr format="string" name="layoutManager"/>

        
        
        
        <eat-comment/>

        <attr name="android:orientation"/>
        <attr name="android:descendantFocusability"/>
        <attr name="android:clipToPadding"/>
        <attr format="integer" name="spanCount"/>
        <attr format="boolean" name="reverseLayout"/>
        <attr format="boolean" name="stackFromEnd"/>
        <attr format="boolean" name="fastScrollEnabled"/>
        <attr format="reference" name="fastScrollVerticalThumbDrawable"/>
        <attr format="reference" name="fastScrollVerticalTrackDrawable"/>
        <attr format="reference" name="fastScrollHorizontalThumbDrawable"/>
        <attr format="reference" name="fastScrollHorizontalTrackDrawable"/>
    </declare-styleable>
    <declare-styleable name="SearchView">
        
        <attr format="reference" name="layout"/>
        
        <attr format="boolean" name="iconifiedByDefault"/>
        
        <attr name="android:maxWidth"/>
        
        <attr format="string" name="queryHint"/>
        
        <attr format="string" name="defaultQueryHint"/>
        
        <attr name="android:imeOptions"/>
        
        <attr name="android:inputType"/>
        
        <attr format="reference" name="closeIcon"/>
        
        <attr format="reference" name="goIcon"/>
        
        <attr format="reference" name="searchIcon"/>
        
        <attr format="reference" name="searchHintIcon"/>
        
        <attr format="reference" name="voiceIcon"/>
        
        <attr format="reference" name="commitIcon"/>
        
        <attr format="reference" name="suggestionRowLayout"/>
        
        <attr format="reference" name="queryBackground"/>
        
        <attr format="reference" name="submitBackground"/>
        <attr name="android:focusable"/>
    </declare-styleable>
    <declare-styleable name="Spinner">
        
        <attr name="android:prompt"/>
        
        <attr name="popupTheme"/>
        
        <attr name="android:popupBackground"/>
        
        <attr name="android:dropDownWidth"/>
        
        <attr name="android:entries"/>
    </declare-styleable>
    <declare-styleable name="StateListDrawable">
        
        <attr name="android:visible"/>
        
        <attr name="android:variablePadding"/>
        
        <attr name="android:constantSize"/>
        
        <attr name="android:dither"/>
        
        <attr name="android:enterFadeDuration"/>
        
        <attr name="android:exitFadeDuration"/>
        
        
    </declare-styleable>
    <declare-styleable name="StateListDrawableItem">
        
        <attr name="android:drawable"/>
    </declare-styleable>
    <declare-styleable name="SwitchCompat">
        
        <attr name="android:thumb"/>
        
        <attr format="color" name="thumbTint"/>
        
        <attr name="thumbTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
        
        <attr format="reference" name="track"/>
        
        <attr format="color" name="trackTint"/>
        
        <attr name="trackTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and drawable color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
        
        <attr name="android:textOn"/>
        
        <attr name="android:textOff"/>
        
        <attr format="dimension" name="thumbTextPadding"/>
        
        <attr format="reference" name="switchTextAppearance"/>
        
        <attr format="dimension" name="switchMinWidth"/>
        
        <attr format="dimension" name="switchPadding"/>
        
        <attr format="boolean" name="splitTrack"/>
        
        <attr format="boolean" name="showText"/>
    </declare-styleable>
    <declare-styleable name="TextAppearance">
        <attr name="android:textSize"/>
        <attr name="android:textColor"/>
        <attr name="android:textColorHint"/>
        <attr name="android:textColorLink"/>
        <attr name="android:textStyle"/>
        <attr name="android:typeface"/>
        <attr name="android:fontFamily"/>
        <attr name="fontFamily"/>
        <attr name="textAllCaps"/>
        <attr name="android:shadowColor"/>
        <attr name="android:shadowDy"/>
        <attr name="android:shadowDx"/>
        <attr name="android:shadowRadius"/>
    </declare-styleable>
    <declare-styleable name="Toolbar">
        <attr format="reference" name="titleTextAppearance"/>
        <attr format="reference" name="subtitleTextAppearance"/>
        <attr name="title"/>
        <attr name="subtitle"/>
        <attr name="android:gravity"/>
        
        <attr format="dimension" name="titleMargin"/>
        
        <attr format="dimension" name="titleMarginStart"/>
        
        <attr format="dimension" name="titleMarginEnd"/>
        
        <attr format="dimension" name="titleMarginTop"/>
        
        <attr format="dimension" name="titleMarginBottom"/>
        
        <attr format="dimension" name="titleMargins"/>
        <attr name="contentInsetStart"/>
        <attr name="contentInsetEnd"/>
        <attr name="contentInsetLeft"/>
        <attr name="contentInsetRight"/>
        <attr name="contentInsetStartWithNavigation"/>
        <attr name="contentInsetEndWithActions"/>
        <attr format="dimension" name="maxButtonHeight"/>
        <attr name="buttonGravity">
            <!-- Push object to the top of its container, not changing its size. -->
            <flag name="top" value="0x30"/>
            <!-- Push object to the bottom of its container, not changing its size. -->
            <flag name="bottom" value="0x50"/>
        </attr>
        
        <attr format="reference" name="collapseIcon"/>
        
        <attr format="string" name="collapseContentDescription"/>
        
        <attr name="popupTheme"/>
        
        <attr format="reference" name="navigationIcon"/>
        
        <attr format="string" name="navigationContentDescription"/>
        
        <attr name="logo"/>
        
        <attr format="string" name="logoDescription"/>
        
        <attr format="color" name="titleTextColor"/>
        
        <attr format="color" name="subtitleTextColor"/>
        <attr name="android:minHeight"/>
    </declare-styleable>
    <declare-styleable name="View">
        
        <attr format="dimension" name="paddingStart"/>
        
        <attr format="dimension" name="paddingEnd"/>
        
        <attr name="android:focusable"/>
        
        <attr format="reference" name="theme"/>
        
        <attr name="android:theme"/>
    </declare-styleable>
    <declare-styleable name="ViewBackgroundHelper">
        <attr name="android:background"/>
        
        <attr format="color" name="backgroundTint"/>

        
        <attr name="backgroundTintMode">
            <!-- The tint is drawn on top of the drawable.
                 [Sa + (1 - Sa)*Da, Rc = Sc + (1 - Sa)*Dc] -->
            <enum name="src_over" value="3"/>
            <!-- The tint is masked by the alpha channel of the drawable. The drawable’s
                 color channels are thrown out. [Sa * Da, Sc * Da] -->
            <enum name="src_in" value="5"/>
            <!-- The tint is drawn above the drawable, but with the drawable’s alpha
                 channel masking the result. [Da, Sc * Da + (1 - Sa) * Dc] -->
            <enum name="src_atop" value="9"/>
            <!-- Multiplies the color and alpha channels of the drawable with those of
                 the tint. [Sa * Da, Sc * Dc] -->
            <enum name="multiply" value="14"/>
            <!-- [Sa + Da - Sa * Da, Sc + Dc - Sc * Dc] -->
            <enum name="screen" value="15"/>
            <!-- Combines the tint and icon color and alpha channels, clamping the
                 result to valid color values. Saturate(S + D) -->
            <enum name="add" value="16"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="ViewStubCompat">
        
        <attr name="android:layout"/>
        
        <attr name="android:inflatedId"/>
        <attr name="android:id"/>
    </declare-styleable>
    <declare-styleable name="lbBaseCardView">
        
        <attr format="reference|color" name="cardForeground"/>
        
        <attr format="reference|color" name="cardBackground"/>
        
        <attr format="enum" name="cardType">
            <!-- A simple card layout with a single layout region. -->
            <enum name="mainOnly" value="0"/>
            <!-- A card layout with two layout regions: a main area which is
                 always visible, and an info region that appears over the lower
                 area of the main region. -->
            <enum name="infoOver" value="1"/>
            <!-- A card layout with two layout regions: a main area which is
                 always visible, and an info region that appears below the main
                 region. -->
            <enum name="infoUnder" value="2"/>
            <!-- A card layout with three layout regions: a main area which is
                 always visible, an info region that appears below the main
                 region, and an extra region that appears below the info region
                 after a small delay. -->
            <enum name="infoUnderWithExtra" value="3"/>
        </attr>
        
        <attr format="enum" name="infoVisibility">
            <!-- Always display the info region. -->
            <enum name="always" value="0"/>
            <!-- Display the info region only when activated. -->
            <enum name="activated" value="1"/>
            <!-- Display the info region only when selected. -->
            <enum name="selected" value="2"/>
        </attr>
        
        <attr format="enum" name="extraVisibility">
            <!-- Always display the extra region. -->
            <enum name="always" value="0"/>
            <!-- Display the extra region only when activated. -->
            <enum name="activated" value="1"/>
            <!-- Display the extra region only when selected. -->
            <enum name="selected" value="2"/>
        </attr>
        
        <attr format="integer" name="selectedAnimationDelay"/>
        
        <attr format="integer" name="selectedAnimationDuration"/>
        
        <attr format="integer" name="activatedAnimationDuration"/>
    </declare-styleable>
    <declare-styleable name="lbBaseCardView_Layout">
        
        <attr format="enum" name="layout_viewType">
            <!-- The main region of the card. -->
            <enum name="main" value="0"/>
            <!-- The info region of the card. -->
            <enum name="info" value="1"/>
            <!-- The extra region of the card. -->
            <enum name="extra" value="2"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="lbBaseGridView">
        
        <attr format="boolean" name="focusOutFront"/>
        
        <attr format="boolean" name="focusOutEnd"/>
        
        <attr format="boolean" name="focusOutSideStart"/>
        
        <attr format="boolean" name="focusOutSideEnd"/>
        
        <attr format="dimension" name="horizontalMargin"/>
        
        <attr format="dimension" name="verticalMargin"/>
        
        <attr name="android:horizontalSpacing"/>
        
        <attr name="android:verticalSpacing"/>
        
        <attr name="android:gravity"/>
    </declare-styleable>
    <declare-styleable name="lbDatePicker">
        <attr name="android:minDate"/>
        <attr name="android:maxDate"/>
        
        <attr format="string" name="datePickerFormat"/>
        
        <attr name="pickerItemLayout"/>
        
        <attr name="pickerItemTextViewId"/>
    </declare-styleable>
    <declare-styleable name="lbHorizontalGridView">
        
        <attr format="dimension" name="rowHeight">
            <enum name="wrap_content" value="-2"/>
        </attr>
        
        <attr format="integer" name="numberOfRows"/>
    </declare-styleable>
    <declare-styleable name="lbImageCardView">
        
        <attr format="reference|color" name="infoAreaBackground"/>
        
        <attr name="lbImageCardViewType">
            <flag name="Title" value="1"/>
            <flag name="Content" value="2"/>
            <flag name="IconOnRight" value="4"/>
            <flag name="IconOnLeft" value="8"/>
            <!-- Only display the main image. -->
            <flag name="ImageOnly" value="0"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="lbPicker">
        
        <attr format="reference" name="pickerItemLayout"/>
        
        <attr format="reference" name="pickerItemTextViewId"/>
    </declare-styleable>
    <declare-styleable name="lbPinPicker">
        
        <attr format="integer" name="columnCount"/>
        
        <attr name="pickerItemLayout"/>
        
        <attr name="pickerItemTextViewId"/>
    </declare-styleable>
    <declare-styleable name="lbPlaybackControlsActionIcons">
        <attr format="reference" name="play"/>
        <attr format="reference" name="pause"/>
        <attr format="reference" name="fast_forward"/>
        <attr format="reference" name="rewind"/>
        <attr format="reference" name="skip_next"/>
        <attr format="reference" name="skip_previous"/>
        <attr format="reference" name="thumb_up_outline"/>
        <attr format="reference" name="thumb_up"/>
        <attr format="reference" name="thumb_down_outline"/>
        <attr format="reference" name="thumb_down"/>
        <attr format="reference" name="repeat"/>
        <attr format="reference" name="repeat_one"/>
        <attr format="reference" name="shuffle"/>
        <attr format="reference" name="high_quality"/>
        <attr format="reference" name="closed_captioning"/>
        <attr format="reference" name="picture_in_picture"/>
    </declare-styleable>
    <declare-styleable name="lbResizingTextView">
        
        <attr name="resizeTrigger">
            <!-- Resize text whenever it lays out into the maximum number of lines -->
            <flag name="maxLines" value="0x01"/>
        </attr>
        
        <attr format="dimension" name="resizedTextSize"/>
        
        <attr format="boolean" name="maintainLineSpacing"/>
        
        <attr format="dimension" name="resizedPaddingAdjustmentTop"/>
        
        <attr format="dimension" name="resizedPaddingAdjustmentBottom"/>
    </declare-styleable>
    <declare-styleable name="lbSearchOrbView">
        
        <attr format="reference" name="searchOrbIcon"/>
        
        <attr format="reference|color" name="searchOrbIconColor"/>
        
        <attr format="reference|color" name="searchOrbColor"/>
        
        <attr format="reference|color" name="searchOrbBrightColor"/>
    </declare-styleable>
    <declare-styleable name="lbSlide">
        
        <attr name="lb_slideEdge">
            <!-- Slide to and from the left edge of the Scene. -->
            <enum name="left" value="0x03"/>
            <!-- Slide to and from the top edge of the Scene. -->
            <enum name="top" value="0x30"/>
            <!-- Slide to and from the right edge of the Scene. -->
            <enum name="right" value="0x05"/>
            <!-- Slide to and from the bottom edge of the Scene. -->
            <enum name="bottom" value="0x50"/>
            <!-- Slide to and from the x-axis position at the start of the Scene root. -->
            <enum name="start" value="0x00800003"/>
            <!-- Slide to and from the x-axis position at the end of the Scene root. -->
            <enum name="end" value="0x00800005"/>
        </attr>
        <attr name="android:duration"/>
        <attr name="android:startDelay"/>
        <attr name="android:interpolator"/>
    </declare-styleable>
    <declare-styleable name="lbTimePicker">
        
        <attr format="boolean" name="is24HourFormat"/>
        
        <attr format="boolean" name="useCurrentTime"/>
        
        <attr name="pickerItemLayout"/>
        
        <attr name="pickerItemTextViewId"/>
    </declare-styleable>
    <declare-styleable name="lbVerticalGridView">
        
        <attr format="dimension" name="columnWidth">
            <enum name="wrap_content" value="-2"/>
        </attr>
        
        <attr format="integer" name="numberOfColumns"/>
    </declare-styleable>
</resources>