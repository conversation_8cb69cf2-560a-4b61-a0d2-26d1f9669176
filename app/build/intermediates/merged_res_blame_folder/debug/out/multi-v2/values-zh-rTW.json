{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,209", "endColumns": "70,82,78", "endOffsets": "121,204,283"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2675,14614,14697", "endColumns": "70,82,78", "endOffsets": "2741,14692,14771"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,254,330,417,508,586,660,737,815,890,963,1038,1106,1187,1260,1332,1403,1477,1545", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,80,72,71,70,73,67,115", "endOffsets": "249,325,412,503,581,655,732,810,885,958,1033,1101,1182,1255,1327,1398,1472,1540,1656"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3407,3484,3560,3647,3738,3816,3890,13430,13508,13664,13737,13890,13958,14039,14112,14184,14356,14430,14498", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,80,72,71,70,73,67,115", "endOffsets": "3479,3555,3642,3733,3811,3885,3962,13503,13578,13732,13807,13953,14034,14107,14179,14250,14425,14493,14609"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2746,2838,2937,3031,3125,3218,3311,14255", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "2833,2932,3026,3120,3213,3306,3402,14351"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "13345", "endColumns": "84", "endOffsets": "13425"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,404,494,607,708,802,891,1006,1120,1214,1326,1435,1543,1650,1747,1834,1941,2028,2127,2224,2322,2411,2517,2611,2713,2814,2911,3012,3112,3218,3315,3418,3505,3585,3676,3808,3951", "endColumns": "106,100,90,89,112,100,93,88,114,113,93,111,108,107,106,96,86,106,86,98,96,97,88,105,93,101,100,96,100,99,105,96,102,86,79,90,131,142,80", "endOffsets": "207,308,399,489,602,703,797,886,1001,1115,1209,1321,1430,1538,1645,1742,1829,1936,2023,2122,2219,2317,2406,2512,2606,2708,2809,2906,3007,3107,3213,3310,3413,3500,3580,3671,3803,3946,4027"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3967,4074,4175,4266,4356,4469,4570,4664,4753,4868,4982,5076,5188,5297,5405,5512,5609,5696,5803,5890,5989,6086,6184,6273,6379,6473,6575,6676,6773,6874,6974,7080,7177,7280,7367,7447,7538,7670,13583", "endColumns": "106,100,90,89,112,100,93,88,114,113,93,111,108,107,106,96,86,106,86,98,96,97,88,105,93,101,100,96,100,99,105,96,102,86,79,90,131,142,80", "endOffsets": "4069,4170,4261,4351,4464,4565,4659,4748,4863,4977,5071,5183,5292,5400,5507,5604,5691,5798,5885,5984,6081,6179,6268,6374,6468,6570,6671,6768,6869,6969,7075,7172,7275,7362,7442,7533,7665,7808,13659"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,2675", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,2748"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1116,1212,1308,1402,1498,1590,1682,1774,1852,1948,2044,2139,2236,2331,2431,2581,13812", "endColumns": "94,92,99,81,96,107,75,75,91,93,97,95,95,93,95,91,91,91,77,95,95,94,96,94,99,149,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1111,1207,1303,1397,1493,1585,1677,1769,1847,1943,2039,2134,2231,2326,2426,2576,2670,13885"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-zh-rTW/values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4048,4131,4213,4302,4382,4464,4561,4655,4748,4841,4925,5022,5118,5213,5321,5401,5495", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4043,4126,4208,4297,4377,4459,4556,4650,4743,4836,4920,5017,5113,5208,5316,5396,5490,5582"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7813,7917,8020,8124,8226,8318,8406,8510,8615,8720,8836,8918,9014,9098,9186,9291,9404,9505,9614,9721,9829,9946,10051,10152,10256,10361,10446,10541,10646,10755,10845,10945,11043,11154,11270,11370,11461,11535,11625,11714,11806,11889,11971,12060,12140,12222,12319,12413,12506,12599,12683,12780,12876,12971,13079,13159,13253", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "7912,8015,8119,8221,8313,8401,8505,8610,8715,8831,8913,9009,9093,9181,9286,9399,9500,9609,9716,9824,9941,10046,10147,10251,10356,10441,10536,10641,10750,10840,10940,11038,11149,11265,11365,11456,11530,11620,11709,11801,11884,11966,12055,12135,12217,12314,12408,12501,12594,12678,12775,12871,12966,13074,13154,13248,13340"}}]}]}