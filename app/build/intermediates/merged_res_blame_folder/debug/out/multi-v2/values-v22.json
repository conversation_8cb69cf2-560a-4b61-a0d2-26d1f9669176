{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-v22/values-v22.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-v22/values-v22.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,107", "endColumns": "51,53", "endOffsets": "102,156"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-v22/values-v22.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,553", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,548,896"}, "to": {"startLines": "4,5,6,11", "startColumns": "4,4,4,4", "startOffsets": "161,236,323,593", "endLines": "4,5,10,15", "endColumns": "74,86,12,12", "endOffsets": "231,318,588,870"}}]}]}