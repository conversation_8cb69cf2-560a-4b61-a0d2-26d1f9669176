{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-or/values-or.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,407,526,620,720,837,980,1106,1257,1342,1447,1543,1638,1754,1884,1994,2137,2275,2406,2598,2724,2853,2988,3118,3215,3311,3428,3550,3655,3760,3863,4005,4155,4262,4371,4446,4550,4652,4763,4857,4948,5053,5133,5218,5319,5425,5518,5619,5706,5814,5913,6016,6140,6220,6323", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,110,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "172,289,402,521,615,715,832,975,1101,1252,1337,1442,1538,1633,1749,1879,1989,2132,2270,2401,2593,2719,2848,2983,3113,3210,3306,3423,3545,3650,3755,3858,4000,4150,4257,4366,4441,4545,4647,4758,4852,4943,5048,5128,5213,5314,5420,5513,5614,5701,5809,5908,6011,6135,6215,6318,6412"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8656,8778,8895,9008,9127,9221,9321,9438,9581,9707,9858,9943,10048,10144,10239,10355,10485,10595,10738,10876,11007,11199,11325,11454,11589,11719,11816,11912,12029,12151,12256,12361,12464,12606,12756,12863,12972,13047,13151,13253,13364,13458,13549,13654,13734,13819,13920,14026,14119,14220,14307,14415,14514,14617,14741,14821,14924", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,110,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "8773,8890,9003,9122,9216,9316,9433,9576,9702,9853,9938,10043,10139,10234,10350,10480,10590,10733,10871,11002,11194,11320,11449,11584,11714,11811,11907,12024,12146,12251,12356,12459,12601,12751,12858,12967,13042,13146,13248,13359,13453,13544,13649,13729,13814,13915,14021,14114,14215,14302,14410,14509,14612,14736,14816,14919,15013"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-or/values-or.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,87", "endOffsets": "123,208,296"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2847,16371,16456", "endColumns": "72,84,87", "endOffsets": "2915,16451,16539"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2920,3023,3125,3228,3333,3434,3536,16001", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3018,3120,3223,3328,3429,3531,3650,16097"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-or/values-or.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15018", "endColumns": "87", "endOffsets": "15101"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-or/values-or.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,276,363,455,555,641,718,816,904,991,1069,1151,1221,1305,1380,1457,1533,1616,1683", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,83,74,76,75,82,66,118", "endOffsets": "271,358,450,550,636,713,811,899,986,1064,1146,1216,1300,1375,1452,1528,1611,1678,1797"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3655,3752,3839,3931,4031,4117,4194,15106,15194,15370,15448,15619,15689,15773,15848,15925,16102,16185,16252", "endColumns": "96,86,91,99,85,76,97,87,86,77,81,69,83,74,76,75,82,66,118", "endOffsets": "3747,3834,3926,4026,4112,4189,4287,15189,15276,15443,15525,15684,15768,15843,15920,15996,16180,16247,16366"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,828,905,996,1089,1186,1281,1382,1475,1570,1666,1757,1847,1929,2039,2144,2250,2361,2464,2582,2745,2847", "endColumns": "118,109,106,85,103,119,76,76,90,92,96,94,100,92,94,95,90,89,81,109,104,105,110,102,117,162,101,88", "endOffsets": "219,329,436,522,626,746,823,900,991,1084,1181,1276,1377,1470,1565,1661,1752,1842,1924,2034,2139,2245,2356,2459,2577,2740,2842,2931"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,828,905,996,1089,1186,1281,1382,1475,1570,1666,1757,1847,1929,2039,2144,2250,2361,2464,2582,2745,15530", "endColumns": "118,109,106,85,103,119,76,76,90,92,96,94,100,92,94,95,90,89,81,109,104,105,110,102,117,162,101,88", "endOffsets": "219,329,436,522,626,746,823,900,991,1084,1181,1276,1377,1470,1565,1661,1752,1842,1924,2034,2139,2245,2356,2459,2577,2740,2842,15614"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,514,638,749,850,947,1089,1230,1337,1466,1608,1737,1865,1970,2066,2200,2292,2403,2518,2623,2718,2843,2954,3071,3187,3296,3412,3530,3650,3763,3878,3965,4056,4166,4308,4469", "endColumns": "106,100,99,100,123,110,100,96,141,140,106,128,141,128,127,104,95,133,91,110,114,104,94,124,110,116,115,108,115,117,119,112,114,86,90,109,141,160,88", "endOffsets": "207,308,408,509,633,744,845,942,1084,1225,1332,1461,1603,1732,1860,1965,2061,2195,2287,2398,2513,2618,2713,2838,2949,3066,3182,3291,3407,3525,3645,3758,3873,3960,4051,4161,4303,4464,4553"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4292,4399,4500,4600,4701,4825,4936,5037,5134,5276,5417,5524,5653,5795,5924,6052,6157,6253,6387,6479,6590,6705,6810,6905,7030,7141,7258,7374,7483,7599,7717,7837,7950,8065,8152,8243,8353,8495,15281", "endColumns": "106,100,99,100,123,110,100,96,141,140,106,128,141,128,127,104,95,133,91,110,114,104,94,124,110,116,115,108,115,117,119,112,114,86,90,109,141,160,88", "endOffsets": "4394,4495,4595,4696,4820,4931,5032,5129,5271,5412,5519,5648,5790,5919,6047,6152,6248,6382,6474,6585,6700,6805,6900,7025,7136,7253,7369,7478,7594,7712,7832,7945,8060,8147,8238,8348,8490,8651,15365"}}]}]}