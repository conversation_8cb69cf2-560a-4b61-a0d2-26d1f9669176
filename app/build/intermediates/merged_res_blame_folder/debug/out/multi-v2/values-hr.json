{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-hr/values-hr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,228", "endColumns": "89,82,84", "endOffsets": "140,223,308"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2837,16273,16356", "endColumns": "89,82,84", "endOffsets": "2922,16351,16436"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-hr/values-hr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,301,388,482,581,671,750,843,938,1023,1104,1190,1263,1352,1429,1508,1585,1664,1734", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,88,76,78,76,78,69,117", "endOffsets": "296,383,477,576,666,745,838,933,1018,1099,1185,1258,1347,1424,1503,1580,1659,1729,1847"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3653,3758,3845,3939,4038,4128,4207,14977,15072,15254,15335,15510,15583,15672,15749,15828,16006,16085,16155", "endColumns": "104,86,93,98,89,78,92,94,84,80,85,72,88,76,78,76,78,69,117", "endOffsets": "3753,3840,3934,4033,4123,4202,4295,15067,15152,15330,15416,15578,15667,15744,15823,15900,16080,16150,16268"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,296,416,537,637,731,842,983,1102,1247,1332,1432,1527,1625,1744,1870,1975,2111,2246,2380,2548,2674,2798,2926,3050,3146,3244,3374,3508,3605,3707,3816,3957,4104,4213,4313,4398,4491,4586,4699,4793,4879,4988,5076,5159,5256,5357,5450,5547,5635,5743,5840,5942,6080,6170,6270", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,112,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "170,291,411,532,632,726,837,978,1097,1242,1327,1427,1522,1620,1739,1865,1970,2106,2241,2375,2543,2669,2793,2921,3045,3141,3239,3369,3503,3600,3702,3811,3952,4099,4208,4308,4393,4486,4581,4694,4788,4874,4983,5071,5154,5251,5352,5445,5542,5630,5738,5835,5937,6075,6165,6265,6357"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8577,8697,8818,8938,9059,9159,9253,9364,9505,9624,9769,9854,9954,10049,10147,10266,10392,10497,10633,10768,10902,11070,11196,11320,11448,11572,11668,11766,11896,12030,12127,12229,12338,12479,12626,12735,12835,12920,13013,13108,13221,13315,13401,13510,13598,13681,13778,13879,13972,14069,14157,14265,14362,14464,14602,14692,14792", "endColumns": "119,120,119,120,99,93,110,140,118,144,84,99,94,97,118,125,104,135,134,133,167,125,123,127,123,95,97,129,133,96,101,108,140,146,108,99,84,92,94,112,93,85,108,87,82,96,100,92,96,87,107,96,101,137,89,99,91", "endOffsets": "8692,8813,8933,9054,9154,9248,9359,9500,9619,9764,9849,9949,10044,10142,10261,10387,10492,10628,10763,10897,11065,11191,11315,11443,11567,11663,11761,11891,12025,12122,12224,12333,12474,12621,12730,12830,12915,13008,13103,13216,13310,13396,13505,13593,13676,13773,13874,13967,14064,14152,14260,14357,14459,14597,14687,14787,14879"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2927,3025,3132,3229,3328,3432,3536,15905", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3020,3127,3224,3323,3427,3531,3648,16001"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,503,629,747,844,936,1059,1179,1285,1409,1553,1680,1804,1908,1998,2128,2220,2321,2428,2530,2624,2735,2848,2976,3101,3211,3326,3443,3561,3675,3790,3877,3965,4082,4218,4382", "endColumns": "106,100,95,93,125,117,96,91,122,119,105,123,143,126,123,103,89,129,91,100,106,101,93,110,112,127,124,109,114,116,117,113,114,86,87,116,135,163,96", "endOffsets": "207,308,404,498,624,742,839,931,1054,1174,1280,1404,1548,1675,1799,1903,1993,2123,2215,2316,2423,2525,2619,2730,2843,2971,3096,3206,3321,3438,3556,3670,3785,3872,3960,4077,4213,4377,4474"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4300,4407,4508,4604,4698,4824,4942,5039,5131,5254,5374,5480,5604,5748,5875,5999,6103,6193,6323,6415,6516,6623,6725,6819,6930,7043,7171,7296,7406,7521,7638,7756,7870,7985,8072,8160,8277,8413,15157", "endColumns": "106,100,95,93,125,117,96,91,122,119,105,123,143,126,123,103,89,129,91,100,106,101,93,110,112,127,124,109,114,116,117,113,114,86,87,116,135,163,96", "endOffsets": "4402,4503,4599,4693,4819,4937,5034,5126,5249,5369,5475,5599,5743,5870,5994,6098,6188,6318,6410,6511,6618,6720,6814,6925,7038,7166,7291,7401,7516,7633,7751,7865,7980,8067,8155,8272,8408,8572,15249"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,307,417,503,607,726,810,893,984,1077,1173,1267,1368,1461,1556,1655,1746,1837,1923,2027,2140,2246,2351,2464,2571,2740,2837", "endColumns": "104,96,109,85,103,118,83,82,90,92,95,93,100,92,94,98,90,90,85,103,112,105,104,112,106,168,96,88", "endOffsets": "205,302,412,498,602,721,805,888,979,1072,1168,1262,1363,1456,1551,1650,1741,1832,1918,2022,2135,2241,2346,2459,2566,2735,2832,2921"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,307,417,503,607,726,810,893,984,1077,1173,1267,1368,1461,1556,1655,1746,1837,1923,2027,2140,2246,2351,2464,2571,2740,15421", "endColumns": "104,96,109,85,103,118,83,82,90,92,95,93,100,92,94,98,90,90,85,103,112,105,104,112,106,168,96,88", "endOffsets": "205,302,412,498,602,721,805,888,979,1072,1168,1262,1363,1456,1551,1650,1741,1832,1918,2022,2135,2241,2346,2459,2566,2735,2832,15505"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-hr/values-hr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14884", "endColumns": "92", "endOffsets": "14972"}}]}]}