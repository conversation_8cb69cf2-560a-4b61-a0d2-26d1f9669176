{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-ky/values-ky.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2890,2990,3092,3195,3302,3404,3508,15928", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "2985,3087,3190,3297,3399,3503,3614,16024"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-ky/values-ky.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14940", "endColumns": "90", "endOffsets": "15026"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,502,634,746,845,936,1067,1198,1302,1424,1588,1711,1834,1942,2032,2161,2250,2356,2467,2573,2671,2786,2904,3020,3136,3251,3370,3490,3607,3721,3836,3923,4006,4110,4244,4399", "endColumns": "106,100,95,92,131,111,98,90,130,130,103,121,163,122,122,107,89,128,88,105,110,105,97,114,117,115,115,114,118,119,116,113,114,86,82,103,133,154,89", "endOffsets": "207,308,404,497,629,741,840,931,1062,1193,1297,1419,1583,1706,1829,1937,2027,2156,2245,2351,2462,2568,2666,2781,2899,3015,3131,3246,3365,3485,3602,3716,3831,3918,4001,4105,4239,4394,4484"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4271,4378,4479,4575,4668,4800,4912,5011,5102,5233,5364,5468,5590,5754,5877,6000,6108,6198,6327,6416,6522,6633,6739,6837,6952,7070,7186,7302,7417,7536,7656,7773,7887,8002,8089,8172,8276,8410,15205", "endColumns": "106,100,95,92,131,111,98,90,130,130,103,121,163,122,122,107,89,128,88,105,110,105,97,114,117,115,115,114,118,119,116,113,114,86,82,103,133,154,89", "endOffsets": "4373,4474,4570,4663,4795,4907,5006,5097,5228,5359,5463,5585,5749,5872,5995,6103,6193,6322,6411,6517,6628,6734,6832,6947,7065,7181,7297,7412,7531,7651,7768,7882,7997,8084,8167,8271,8405,8560,15290"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,304,423,507,614,731,809,888,979,1072,1168,1262,1363,1456,1551,1646,1737,1828,1909,2019,2126,2224,2330,2437,2538,2699,2802", "endColumns": "103,94,118,83,106,116,77,78,90,92,95,93,100,92,94,94,90,90,80,109,106,97,105,106,100,160,102,80", "endOffsets": "204,299,418,502,609,726,804,883,974,1067,1163,1257,1358,1451,1546,1641,1732,1823,1904,2014,2121,2219,2325,2432,2533,2694,2797,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,304,423,507,614,731,809,888,979,1072,1168,1262,1363,1456,1551,1646,1737,1828,1909,2019,2126,2224,2330,2437,2538,2699,15467", "endColumns": "103,94,118,83,106,116,77,78,90,92,95,93,100,92,94,94,90,90,80,109,106,97,105,106,100,160,102,80", "endOffsets": "204,299,418,502,609,726,804,883,974,1067,1163,1257,1358,1451,1546,1641,1732,1823,1904,2014,2121,2219,2325,2432,2533,2694,2797,15543"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,242", "endColumns": "87,98,103", "endOffsets": "138,237,341"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2802,16294,16393", "endColumns": "87,98,103", "endOffsets": "2885,16388,16492"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-ky/values-ky.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,287,371,481,581,666,748,846,935,1020,1105,1192,1265,1352,1426,1499,1572,1651,1719", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,86,73,72,72,78,67,117", "endOffsets": "282,366,476,576,661,743,841,930,1015,1100,1187,1260,1347,1421,1494,1567,1646,1714,1832"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3619,3712,3796,3906,4006,4091,4173,15031,15120,15295,15380,15548,15621,15708,15782,15855,16029,16108,16176", "endColumns": "92,83,109,99,84,81,97,88,84,84,86,72,86,73,72,72,78,67,117", "endOffsets": "3707,3791,3901,4001,4086,4168,4266,15115,15200,15375,15462,15616,15703,15777,15850,15923,16103,16171,16289"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-ky/values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,295,409,534,634,732,847,983,1124,1280,1364,1462,1554,1651,1767,1886,1989,2125,2259,2396,2571,2700,2817,2937,3058,3151,3249,3371,3508,3611,3736,3841,3975,4114,4223,4325,4401,4500,4604,4718,4804,4889,5001,5090,5174,5274,5375,5471,5568,5655,5766,5865,5965,6113,6203,6322", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,113,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "173,290,404,529,629,727,842,978,1119,1275,1359,1457,1549,1646,1762,1881,1984,2120,2254,2391,2566,2695,2812,2932,3053,3146,3244,3366,3503,3606,3731,3836,3970,4109,4218,4320,4396,4495,4599,4713,4799,4884,4996,5085,5169,5269,5370,5466,5563,5650,5761,5860,5960,6108,6198,6317,6425"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8565,8688,8805,8919,9044,9144,9242,9357,9493,9634,9790,9874,9972,10064,10161,10277,10396,10499,10635,10769,10906,11081,11210,11327,11447,11568,11661,11759,11881,12018,12121,12246,12351,12485,12624,12733,12835,12911,13010,13114,13228,13314,13399,13511,13600,13684,13784,13885,13981,14078,14165,14276,14375,14475,14623,14713,14832", "endColumns": "122,116,113,124,99,97,114,135,140,155,83,97,91,96,115,118,102,135,133,136,174,128,116,119,120,92,97,121,136,102,124,104,133,138,108,101,75,98,103,113,85,84,111,88,83,99,100,95,96,86,110,98,99,147,89,118,107", "endOffsets": "8683,8800,8914,9039,9139,9237,9352,9488,9629,9785,9869,9967,10059,10156,10272,10391,10494,10630,10764,10901,11076,11205,11322,11442,11563,11656,11754,11876,12013,12116,12241,12346,12480,12619,12728,12830,12906,13005,13109,13223,13309,13394,13506,13595,13679,13779,13880,13976,14073,14160,14271,14370,14470,14618,14708,14827,14935"}}]}]}