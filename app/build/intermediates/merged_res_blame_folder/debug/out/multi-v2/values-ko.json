{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-ko/values-ko.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,209", "endColumns": "71,81,78", "endOffsets": "122,204,283"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2678,14802,14884", "endColumns": "71,81,78", "endOffsets": "2745,14879,14958"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-ko/values-ko.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "178,257,333,421,511,591,666,745,824,903,976,1052,1120,1201,1277,1351,1421,1495,1559", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,80,75,73,69,73,63,113", "endOffsets": "252,328,416,506,586,661,740,819,898,971,1047,1115,1196,1272,1346,1416,1490,1554,1668"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3427,3506,3582,3670,3760,3840,3915,13613,13692,13853,13926,14080,14148,14229,14305,14379,14550,14624,14688", "endColumns": "78,75,87,89,79,74,78,78,78,72,75,67,80,75,73,69,73,63,113", "endOffsets": "3501,3577,3665,3755,3835,3910,3989,13687,13766,13921,13997,14143,14224,14300,14374,14444,14619,14683,14797"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,294,396,478,576,682,761,837,928,1021,1113,1204,1305,1398,1493,1587,1678,1769,1849,1947,2042,2137,2237,2333,2432,2584,2678", "endColumns": "94,93,101,81,97,105,78,75,90,92,91,90,100,92,94,93,90,90,79,97,94,94,99,95,98,151,93,77", "endOffsets": "195,289,391,473,571,677,756,832,923,1016,1108,1199,1300,1393,1488,1582,1673,1764,1844,1942,2037,2132,2232,2328,2427,2579,2673,2751"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,294,396,478,576,682,761,837,928,1021,1113,1204,1305,1398,1493,1587,1678,1769,1849,1947,2042,2137,2237,2333,2432,2584,14002", "endColumns": "94,93,101,81,97,105,78,75,90,92,91,90,100,92,94,93,90,90,79,97,94,94,99,95,98,151,93,77", "endOffsets": "195,289,391,473,571,677,756,832,923,1016,1108,1199,1300,1393,1488,1582,1673,1764,1844,1942,2037,2132,2232,2328,2427,2579,2673,14075"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,315,406,496,610,712,806,895,1009,1122,1219,1335,1457,1567,1676,1774,1863,1971,2058,2154,2251,2347,2437,2545,2648,2752,2855,2954,3057,3157,3262,3360,3463,3550,3630,3720,3851,3992", "endColumns": "107,101,90,89,113,101,93,88,113,112,96,115,121,109,108,97,88,107,86,95,96,95,89,107,102,103,102,98,102,99,104,97,102,86,79,89,130,140,81", "endOffsets": "208,310,401,491,605,707,801,890,1004,1117,1214,1330,1452,1562,1671,1769,1858,1966,2053,2149,2246,2342,2432,2540,2643,2747,2850,2949,3052,3152,3257,3355,3458,3545,3625,3715,3846,3987,4069"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3994,4102,4204,4295,4385,4499,4601,4695,4784,4898,5011,5108,5224,5346,5456,5565,5663,5752,5860,5947,6043,6140,6236,6326,6434,6537,6641,6744,6843,6946,7046,7151,7249,7352,7439,7519,7609,7740,13771", "endColumns": "107,101,90,89,113,101,93,88,113,112,96,115,121,109,108,97,88,107,86,95,96,95,89,107,102,103,102,98,102,99,104,97,102,86,79,89,130,140,81", "endOffsets": "4097,4199,4290,4380,4494,4596,4690,4779,4893,5006,5103,5219,5341,5451,5560,5658,5747,5855,5942,6038,6135,6231,6321,6429,6532,6636,6739,6838,6941,7041,7146,7244,7347,7434,7514,7604,7735,7876,13848"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-ko/values-ko.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "13528", "endColumns": "84", "endOffsets": "13608"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2750,2842,2942,3036,3133,3229,3327,14449", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "2837,2937,3031,3128,3224,3322,3422,14545"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-ko/values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,160,264,370,474,566,655,761,880,990,1112,1194,1291,1376,1466,1575,1689,1791,1904,2015,2127,2260,2369,2473,2580,2689,2775,2870,2979,3088,3179,3277,3374,3488,3607,3706,3798,3872,3961,4049,4143,4226,4308,4403,4483,4565,4662,4757,4852,4949,5032,5128,5222,5320,5437,5517,5611", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,93,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "155,259,365,469,561,650,756,875,985,1107,1189,1286,1371,1461,1570,1684,1786,1899,2010,2122,2255,2364,2468,2575,2684,2770,2865,2974,3083,3174,3272,3369,3483,3602,3701,3793,3867,3956,4044,4138,4221,4303,4398,4478,4560,4657,4752,4847,4944,5027,5123,5217,5315,5432,5512,5606,5697"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7881,7986,8090,8196,8300,8392,8481,8587,8706,8816,8938,9020,9117,9202,9292,9401,9515,9617,9730,9841,9953,10086,10195,10299,10406,10515,10601,10696,10805,10914,11005,11103,11200,11314,11433,11532,11624,11698,11787,11875,11969,12052,12134,12229,12309,12391,12488,12583,12678,12775,12858,12954,13048,13146,13263,13343,13437", "endColumns": "104,103,105,103,91,88,105,118,109,121,81,96,84,89,108,113,101,112,110,111,132,108,103,106,108,85,94,108,108,90,97,96,113,118,98,91,73,88,87,93,82,81,94,79,81,96,94,94,96,82,95,93,97,116,79,93,90", "endOffsets": "7981,8085,8191,8295,8387,8476,8582,8701,8811,8933,9015,9112,9197,9287,9396,9510,9612,9725,9836,9948,10081,10190,10294,10401,10510,10596,10691,10800,10909,11000,11098,11195,11309,11428,11527,11619,11693,11782,11870,11964,12047,12129,12224,12304,12386,12483,12578,12673,12770,12853,12949,13043,13141,13258,13338,13432,13523"}}]}]}