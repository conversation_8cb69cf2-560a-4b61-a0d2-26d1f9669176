{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-v21/values-v21.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,3,4,5,263,264,271,275,373,376", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,18647,18763,19220,19514,26506,26678", "endLines": "2,3,4,5,263,264,271,275,375,380", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,18758,18884,19341,19637,26673,27025"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,7,13,17,20,25,29,50,54,60,64,67,72,76,96,99,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,438,973,1219,1447,1891,2205,3560,3834,4349,4585,4793,5217,5511,6752,6950,7141", "endLines": "2,6,12,16,19,24,28,49,53,59,63,66,71,75,95,98,102,103", "endColumns": "88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,72", "endOffsets": "139,433,968,1214,1442,1886,2200,3555,3829,4344,4580,4788,5212,5506,6747,6945,7136,7209"}, "to": {"startLines": "279,280,284,290,294,297,302,306,327,331,337,341,344,349,353,381,384,388", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19809,19898,20192,20727,20973,21201,21645,21959,23314,23588,24103,24339,24547,24971,25265,27030,27228,27419", "endLines": "279,283,289,293,296,301,305,326,330,336,340,343,348,352,372,383,387,388", "endColumns": "88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,72", "endOffsets": "19893,20187,20722,20968,21196,21640,21954,23309,23583,24098,24334,24542,24966,25260,26501,27223,27414,27487"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,107,110,154,157,160,162,164,166,169,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,221,222,223,233,234,235,247", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4234,4383,4532,4644,4791,4944,5091,5166,5255,5342,5443,5546,8614,8799,11879,12076,12275,12398,12521,12634,12817,12948,13149,13238,13349,13582,13683,13778,13901,14030,14147,14324,14423,14558,14701,14836,14955,15156,15275,15368,15479,15535,15642,15837,15948,16081,16176,16267,16358,16475,16614,16685,16768,17448,17505,17563,18257", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,106,109,153,156,159,161,163,165,168,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,220,221,222,232,233,234,246,258", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4229,4378,4527,4639,4786,4939,5086,5161,5250,5337,5438,5541,8609,8794,11874,12071,12270,12393,12516,12629,12812,12943,13144,13233,13344,13577,13678,13773,13896,14025,14142,14319,14418,14553,14696,14831,14950,15151,15270,15363,15474,15530,15637,15832,15943,16076,16171,16262,16353,16470,16609,16680,16763,17443,17500,17558,18252,18958"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,23,24,25,26,28,30,31,32,33,34,36,38,40,42,44,46,47,52,54,56,57,58,60,62,63,64,65,66,67,111,114,158,161,164,166,168,170,173,175,178,179,180,183,184,185,186,187,188,191,192,194,196,198,200,204,206,207,208,209,211,215,217,219,220,221,222,223,225,226,227,237,238,239,251", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "368,459,562,665,770,877,986,1095,1204,1313,1422,1529,1632,1751,1906,2061,2166,2287,2388,2535,2676,2779,2898,3005,3108,3263,3434,3583,3748,3905,4056,4175,4547,4696,4845,4957,5104,5257,5404,5479,5568,5655,5756,5859,8711,8896,11760,11957,12156,12279,12402,12515,12698,12829,13030,13119,13230,13463,13564,13659,13782,13911,14028,14205,14304,14439,14582,14717,14836,15037,15156,15249,15360,15416,15523,15718,15829,15962,16057,16148,16239,16356,16495,16566,16649,17272,17329,17387,18011", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,20,22,23,24,25,27,29,30,31,32,33,35,37,39,41,43,45,46,51,53,55,56,57,59,61,62,63,64,65,66,110,113,157,160,163,165,167,169,172,174,177,178,179,182,183,184,185,186,187,190,191,193,195,197,199,203,205,206,207,208,210,214,216,218,219,220,221,222,224,225,226,236,237,238,250,262", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,116,12,70,82,12,56,57,12,12", "endOffsets": "454,557,660,765,872,981,1090,1199,1308,1417,1524,1627,1746,1901,2056,2161,2282,2383,2530,2671,2774,2893,3000,3103,3258,3429,3578,3743,3900,4051,4170,4542,4691,4840,4952,5099,5252,5399,5474,5563,5650,5751,5854,8706,8891,11755,11952,12151,12274,12397,12510,12693,12824,13025,13114,13225,13458,13559,13654,13777,13906,14023,14200,14299,14434,14577,14712,14831,15032,15151,15244,15355,15411,15518,15713,15824,15957,16052,16143,16234,16351,16490,16561,16644,17267,17324,17382,18006,18642"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/f3bd731085bd142d79a0e2417707c1ed/transformed/media-1.0.0/res/values-v21/values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "265,268,272,276", "startColumns": "4,4,4,4", "startOffsets": "18889,19057,19346,19642", "endLines": "267,270,274,278", "endColumns": "12,12,12,12", "endOffsets": "19052,19215,19509,19804"}}]}]}