{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-lt/values-lt.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,411,507,628,738,835,927,1051,1174,1278,1411,1569,1692,1814,1922,2018,2145,2236,2340,2447,2550,2648,2774,2885,2997,3108,3212,3325,3461,3591,3723,3849,3936,4021,4133,4271,4435", "endColumns": "108,102,93,95,120,109,96,91,123,122,103,132,157,122,121,107,95,126,90,103,106,102,97,125,110,111,110,103,112,135,129,131,125,86,84,111,137,163,93", "endOffsets": "209,312,406,502,623,733,830,922,1046,1169,1273,1406,1564,1687,1809,1917,2013,2140,2231,2335,2442,2545,2643,2769,2880,2992,3103,3207,3320,3456,3586,3718,3844,3931,4016,4128,4266,4430,4524"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4376,4485,4588,4682,4778,4899,5009,5106,5198,5322,5445,5549,5682,5840,5963,6085,6193,6289,6416,6507,6611,6718,6821,6919,7045,7156,7268,7379,7483,7596,7732,7862,7994,8120,8207,8292,8404,8542,15300", "endColumns": "108,102,93,95,120,109,96,91,123,122,103,132,157,122,121,107,95,126,90,103,106,102,97,125,110,111,110,103,112,135,129,131,125,86,84,111,137,163,93", "endOffsets": "4480,4583,4677,4773,4894,5004,5101,5193,5317,5440,5544,5677,5835,5958,6080,6188,6284,6411,6502,6606,6713,6816,6914,7040,7151,7263,7374,7478,7591,7727,7857,7989,8115,8202,8287,8399,8537,8701,15389"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-lt/values-lt.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,286,370,468,573,668,745,836,923,1007,1093,1181,1256,1344,1421,1498,1573,1653,1736", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,87,76,76,74,79,82,121", "endOffsets": "281,365,463,568,663,740,831,918,1002,1088,1176,1251,1339,1416,1493,1568,1648,1731,1853"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3733,3826,3910,4008,4113,4208,4285,15129,15216,15394,15480,15651,15726,15814,15891,15968,16144,16224,16307", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,87,76,76,74,79,82,121", "endOffsets": "3821,3905,4003,4108,4203,4280,4371,15211,15295,15475,15563,15721,15809,15886,15963,16038,16219,16302,16424"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2982,3080,3190,3289,3392,3503,3613,16043", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3075,3185,3284,3387,3498,3608,3728,16139"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,411,529,630,724,835,967,1083,1227,1311,1410,1506,1605,1730,1848,1952,2091,2226,2365,2561,2691,2809,2935,3062,3159,3260,3382,3511,3609,3712,3819,3957,4105,4214,4318,4402,4498,4594,4709,4797,4887,4998,5078,5165,5265,5374,5470,5569,5657,5768,5864,5964,6102,6186,6289", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,114,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "169,289,406,524,625,719,830,962,1078,1222,1306,1405,1501,1600,1725,1843,1947,2086,2221,2360,2556,2686,2804,2930,3057,3154,3255,3377,3506,3604,3707,3814,3952,4100,4209,4313,4397,4493,4589,4704,4792,4882,4993,5073,5160,5260,5369,5465,5564,5652,5763,5859,5959,6097,6181,6284,6381"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8706,8825,8945,9062,9180,9281,9375,9486,9618,9734,9878,9962,10061,10157,10256,10381,10499,10603,10742,10877,11016,11212,11342,11460,11586,11713,11810,11911,12033,12162,12260,12363,12470,12608,12756,12865,12969,13053,13149,13245,13360,13448,13538,13649,13729,13816,13916,14025,14121,14220,14308,14419,14515,14615,14753,14837,14940", "endColumns": "118,119,116,117,100,93,110,131,115,143,83,98,95,98,124,117,103,138,134,138,195,129,117,125,126,96,100,121,128,97,102,106,137,147,108,103,83,95,95,114,87,89,110,79,86,99,108,95,98,87,110,95,99,137,83,102,96", "endOffsets": "8820,8940,9057,9175,9276,9370,9481,9613,9729,9873,9957,10056,10152,10251,10376,10494,10598,10737,10872,11011,11207,11337,11455,11581,11708,11805,11906,12028,12157,12255,12358,12465,12603,12751,12860,12964,13048,13144,13240,13355,13443,13533,13644,13724,13811,13911,14020,14116,14215,14303,14414,14510,14610,14748,14832,14935,15032"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,322,435,522,631,752,834,915,1009,1105,1203,1299,1403,1499,1597,1700,1794,1888,1973,2082,2191,2291,2401,2505,2618,2794,2895", "endColumns": "115,100,112,86,108,120,81,80,93,95,97,95,103,95,97,102,93,93,84,108,108,99,109,103,112,175,100,82", "endOffsets": "216,317,430,517,626,747,829,910,1004,1100,1198,1294,1398,1494,1592,1695,1789,1883,1968,2077,2186,2286,2396,2500,2613,2789,2890,2973"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,322,435,522,631,752,834,915,1009,1105,1203,1299,1403,1499,1597,1700,1794,1888,1973,2082,2191,2291,2401,2505,2618,2794,15568", "endColumns": "115,100,112,86,108,120,81,80,93,95,97,95,103,95,97,102,93,93,84,108,108,99,109,103,112,175,100,82", "endOffsets": "216,317,430,517,626,747,829,910,1004,1100,1198,1294,1398,1494,1592,1695,1789,1883,1968,2077,2186,2286,2396,2500,2613,2789,2890,15646"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-lt/values-lt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,230", "endColumns": "86,87,87", "endOffsets": "137,225,313"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2895,16429,16517", "endColumns": "86,87,87", "endOffsets": "2977,16512,16600"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-lt/values-lt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15037", "endColumns": "91", "endOffsets": "15124"}}]}]}