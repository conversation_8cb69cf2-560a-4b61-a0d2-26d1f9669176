{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-af/values-af.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-af/values-af.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14588", "endColumns": "92", "endOffsets": "14676"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-af/values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,415,500,603,721,797,874,965,1058,1154,1248,1348,1441,1536,1635,1730,1824,1905,2012,2115,2212,2320,2422,2524,2678,2776", "endColumns": "103,99,105,84,102,117,75,76,90,92,95,93,99,92,94,98,94,93,80,106,102,96,107,101,101,153,97,79", "endOffsets": "204,304,410,495,598,716,792,869,960,1053,1149,1243,1343,1436,1531,1630,1725,1819,1900,2007,2110,2207,2315,2417,2519,2673,2771,2851"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,415,500,603,721,797,874,965,1058,1154,1248,1348,1441,1536,1635,1730,1824,1905,2012,2115,2212,2320,2422,2524,2678,15106", "endColumns": "103,99,105,84,102,117,75,76,90,92,95,93,99,92,94,98,94,93,80,106,102,96,107,101,101,153,97,79", "endOffsets": "204,304,410,495,598,716,792,869,960,1053,1149,1243,1343,1436,1531,1630,1725,1819,1900,2007,2110,2207,2315,2417,2519,2673,2771,15181"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-af/values-af.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,216", "endColumns": "73,86,84", "endOffsets": "124,211,296"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2776,15935,16022", "endColumns": "73,86,84", "endOffsets": "2845,16017,16102"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-af/values-af.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "180,276,363,460,561,647,723,814,904,990,1068,1149,1220,1309,1384,1455,1526,1607,1677", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,88,74,70,70,80,69,119", "endOffsets": "271,358,455,556,642,718,809,899,985,1063,1144,1215,1304,1379,1450,1521,1602,1672,1792"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3582,3678,3765,3862,3963,4049,4125,14681,14771,14947,15025,15186,15257,15346,15421,15492,15664,15745,15815", "endColumns": "95,86,96,100,85,75,90,89,85,77,80,70,88,74,70,70,80,69,119", "endOffsets": "3673,3760,3857,3958,4044,4120,4211,14766,14852,15020,15101,15252,15341,15416,15487,15558,15740,15810,15930"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-af/values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,507,625,738,838,933,1062,1188,1294,1418,1558,1681,1801,1909,2004,2130,2220,2324,2428,2529,2627,2742,2853,2967,3078,3186,3296,3405,3519,3626,3738,3825,3907,4008,4145,4301", "endColumns": "106,100,98,94,117,112,99,94,128,125,105,123,139,122,119,107,94,125,89,103,103,100,97,114,110,113,110,107,109,108,113,106,111,86,81,100,136,155,89", "endOffsets": "207,308,407,502,620,733,833,928,1057,1183,1289,1413,1553,1676,1796,1904,1999,2125,2215,2319,2423,2524,2622,2737,2848,2962,3073,3181,3291,3400,3514,3621,3733,3820,3902,4003,4140,4296,4386"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4216,4323,4424,4523,4618,4736,4849,4949,5044,5173,5299,5405,5529,5669,5792,5912,6020,6115,6241,6331,6435,6539,6640,6738,6853,6964,7078,7189,7297,7407,7516,7630,7737,7849,7936,8018,8119,8256,14857", "endColumns": "106,100,98,94,117,112,99,94,128,125,105,123,139,122,119,107,94,125,89,103,103,100,97,114,110,113,110,107,109,108,113,106,111,86,81,100,136,155,89", "endOffsets": "4318,4419,4518,4613,4731,4844,4944,5039,5168,5294,5400,5524,5664,5787,5907,6015,6110,6236,6326,6430,6534,6635,6733,6848,6959,7073,7184,7292,7402,7511,7625,7732,7844,7931,8013,8114,8251,8407,14942"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-af/values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2850,2948,3050,3148,3246,3353,3462,15563", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "2943,3045,3143,3241,3348,3457,3577,15659"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-af/values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,290,405,521,621,725,846,987,1115,1257,1342,1441,1531,1627,1742,1863,1967,2095,2220,2352,2518,2643,2765,2888,3017,3108,3207,3323,3449,3549,3659,3762,3899,4039,4145,4243,4320,4414,4508,4612,4697,4785,4890,4971,5054,5153,5251,5346,5444,5532,5635,5735,5838,5954,6035,6135", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,103,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "167,285,400,516,616,720,841,982,1110,1252,1337,1436,1526,1622,1737,1858,1962,2090,2215,2347,2513,2638,2760,2883,3012,3103,3202,3318,3444,3544,3654,3757,3894,4034,4140,4238,4315,4409,4503,4607,4692,4780,4885,4966,5049,5148,5246,5341,5439,5527,5630,5730,5833,5949,6030,6130,6226"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8412,8529,8647,8762,8878,8978,9082,9203,9344,9472,9614,9699,9798,9888,9984,10099,10220,10324,10452,10577,10709,10875,11000,11122,11245,11374,11465,11564,11680,11806,11906,12016,12119,12256,12396,12502,12600,12677,12771,12865,12969,13054,13142,13247,13328,13411,13510,13608,13703,13801,13889,13992,14092,14195,14311,14392,14492", "endColumns": "116,117,114,115,99,103,120,140,127,141,84,98,89,95,114,120,103,127,124,131,165,124,121,122,128,90,98,115,125,99,109,102,136,139,105,97,76,93,93,103,84,87,104,80,82,98,97,94,97,87,102,99,102,115,80,99,95", "endOffsets": "8524,8642,8757,8873,8973,9077,9198,9339,9467,9609,9694,9793,9883,9979,10094,10215,10319,10447,10572,10704,10870,10995,11117,11240,11369,11460,11559,11675,11801,11901,12011,12114,12251,12391,12497,12595,12672,12766,12860,12964,13049,13137,13242,13323,13406,13505,13603,13698,13796,13884,13987,14087,14190,14306,14387,14487,14583"}}]}]}