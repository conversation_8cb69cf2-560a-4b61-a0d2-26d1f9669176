{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-lo/values-lo.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,228", "endColumns": "86,85,84", "endOffsets": "137,223,308"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2755,15782,15868", "endColumns": "86,85,84", "endOffsets": "2837,15863,15948"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-lo/values-lo.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,283,360,469,567,656,745,835,921,1004,1084,1168,1242,1330,1411,1486,1561,1639,1705", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,87,80,74,74,77,65,120", "endOffsets": "278,355,464,562,651,740,830,916,999,1079,1163,1237,1325,1406,1481,1556,1634,1700,1821"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3548,3638,3715,3824,3922,4011,4100,14521,14607,14778,14858,15023,15097,15185,15266,15341,15517,15595,15661", "endColumns": "89,76,108,97,88,88,89,85,82,79,83,73,87,80,74,74,77,65,120", "endOffsets": "3633,3710,3819,3917,4006,4095,4185,14602,14685,14853,14937,15092,15180,15261,15336,15411,15590,15656,15777"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,412,497,602,714,791,869,960,1053,1149,1243,1344,1437,1532,1626,1717,1808,1888,1995,2092,2190,2295,2398,2502,2659,2755", "endColumns": "102,96,106,84,104,111,76,77,90,92,95,93,100,92,94,93,90,90,79,106,96,97,104,102,103,156,95,80", "endOffsets": "203,300,407,492,597,709,786,864,955,1048,1144,1238,1339,1432,1527,1621,1712,1803,1883,1990,2087,2185,2290,2393,2497,2654,2750,2831"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,412,497,602,714,791,869,960,1053,1149,1243,1344,1437,1532,1626,1717,1808,1888,1995,2092,2190,2295,2398,2502,2659,14942", "endColumns": "102,96,106,84,104,111,76,77,90,92,95,93,100,92,94,93,90,90,79,106,96,97,104,102,103,156,95,80", "endOffsets": "203,300,407,492,597,709,786,864,955,1048,1144,1238,1339,1432,1527,1621,1712,1803,1883,1990,2087,2185,2290,2393,2497,2654,2750,15018"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,501,626,732,829,923,1051,1179,1282,1403,1539,1654,1769,1877,1972,2092,2182,2287,2390,2496,2596,2713,2822,2935,3048,3150,3257,3364,3481,3588,3700,3787,3870,3970,4104,4255", "endColumns": "106,100,94,92,124,105,96,93,127,127,102,120,135,114,114,107,94,119,89,104,102,105,99,116,108,112,112,101,106,106,116,106,111,86,82,99,133,150,87", "endOffsets": "207,308,403,496,621,727,824,918,1046,1174,1277,1398,1534,1649,1764,1872,1967,2087,2177,2282,2385,2491,2591,2708,2817,2930,3043,3145,3252,3359,3476,3583,3695,3782,3865,3965,4099,4250,4338"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4190,4297,4398,4493,4586,4711,4817,4914,5008,5136,5264,5367,5488,5624,5739,5854,5962,6057,6177,6267,6372,6475,6581,6681,6798,6907,7020,7133,7235,7342,7449,7566,7673,7785,7872,7955,8055,8189,14690", "endColumns": "106,100,94,92,124,105,96,93,127,127,102,120,135,114,114,107,94,119,89,104,102,105,99,116,108,112,112,101,106,106,116,106,111,86,82,99,133,150,87", "endOffsets": "4292,4393,4488,4581,4706,4812,4909,5003,5131,5259,5362,5483,5619,5734,5849,5957,6052,6172,6262,6367,6470,6576,6676,6793,6902,7015,7128,7230,7337,7444,7561,7668,7780,7867,7950,8050,8184,8335,14773"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-lo/values-lo.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14429", "endColumns": "91", "endOffsets": "14516"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,391,502,599,695,808,937,1058,1189,1274,1374,1464,1564,1682,1802,1907,2034,2159,2289,2437,2558,2672,2791,2903,2994,3093,3206,3331,3425,3541,3647,3774,3908,4018,4115,4195,4293,4389,4496,4582,4668,4773,4859,4946,5049,5151,5246,5349,5435,5536,5634,5736,5863,5949,6049", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,106,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "164,276,386,497,594,690,803,932,1053,1184,1269,1369,1459,1559,1677,1797,1902,2029,2154,2284,2432,2553,2667,2786,2898,2989,3088,3201,3326,3420,3536,3642,3769,3903,4013,4110,4190,4288,4384,4491,4577,4663,4768,4854,4941,5044,5146,5241,5344,5430,5531,5629,5731,5858,5944,6044,6139"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8340,8454,8566,8676,8787,8884,8980,9093,9222,9343,9474,9559,9659,9749,9849,9967,10087,10192,10319,10444,10574,10722,10843,10957,11076,11188,11279,11378,11491,11616,11710,11826,11932,12059,12193,12303,12400,12480,12578,12674,12781,12867,12953,13058,13144,13231,13334,13436,13531,13634,13720,13821,13919,14021,14148,14234,14334", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,106,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "8449,8561,8671,8782,8879,8975,9088,9217,9338,9469,9554,9654,9744,9844,9962,10082,10187,10314,10439,10569,10717,10838,10952,11071,11183,11274,11373,11486,11611,11705,11821,11927,12054,12188,12298,12395,12475,12573,12669,12776,12862,12948,13053,13139,13226,13329,13431,13526,13629,13715,13816,13914,14016,14143,14229,14329,14424"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2842,2938,3041,3140,3238,3339,3437,15416", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "2933,3036,3135,3233,3334,3432,3543,15512"}}]}]}