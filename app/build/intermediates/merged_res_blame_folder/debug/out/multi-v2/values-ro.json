{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-ro/values-ro.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-ro/values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,312,429,555,665,762,876,1013,1133,1276,1360,1462,1557,1655,1775,1902,2009,2147,2283,2424,2600,2737,2856,2979,3105,3201,3297,3424,3565,3665,3770,3881,4021,4167,4279,4383,4459,4554,4646,4753,4839,4926,5027,5109,5192,5291,5395,5490,5591,5678,5789,5889,5995,6116,6198,6313", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "180,307,424,550,660,757,871,1008,1128,1271,1355,1457,1552,1650,1770,1897,2004,2142,2278,2419,2595,2732,2851,2974,3100,3196,3292,3419,3560,3660,3765,3876,4016,4162,4274,4378,4454,4549,4641,4748,4834,4921,5022,5104,5187,5286,5390,5485,5586,5673,5784,5884,5990,6111,6193,6308,6412"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8684,8814,8941,9058,9184,9294,9391,9505,9642,9762,9905,9989,10091,10186,10284,10404,10531,10638,10776,10912,11053,11229,11366,11485,11608,11734,11830,11926,12053,12194,12294,12399,12510,12650,12796,12908,13012,13088,13183,13275,13382,13468,13555,13656,13738,13821,13920,14024,14119,14220,14307,14418,14518,14624,14745,14827,14942", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "8809,8936,9053,9179,9289,9386,9500,9637,9757,9900,9984,10086,10181,10279,10399,10526,10633,10771,10907,11048,11224,11361,11480,11603,11729,11825,11921,12048,12189,12289,12394,12505,12645,12791,12903,13007,13083,13178,13270,13377,13463,13550,13651,13733,13816,13915,14019,14114,14215,14302,14413,14513,14619,14740,14822,14937,15041"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-ro/values-ro.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15046", "endColumns": "88", "endOffsets": "15130"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-ro/values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,531,643,764,849,930,1021,1114,1210,1304,1404,1497,1592,1687,1778,1870,1953,2065,2178,2278,2392,2497,2603,2767,2870", "endColumns": "120,103,112,87,111,120,84,80,90,92,95,93,99,92,94,94,90,91,82,111,112,99,113,104,105,163,102,82", "endOffsets": "221,325,438,526,638,759,844,925,1016,1109,1205,1299,1399,1492,1587,1682,1773,1865,1948,2060,2173,2273,2387,2492,2598,2762,2865,2948"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,531,643,764,849,930,1021,1114,1210,1304,1404,1497,1592,1687,1778,1870,1953,2065,2178,2278,2392,2497,2603,2767,15582", "endColumns": "120,103,112,87,111,120,84,80,90,92,95,93,99,92,94,94,90,91,82,111,112,99,113,104,105,163,102,82", "endOffsets": "221,325,438,526,638,759,844,925,1016,1109,1205,1299,1399,1492,1587,1682,1773,1865,1948,2060,2173,2273,2387,2492,2598,2762,2865,15660"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-ro/values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,509,637,751,850,941,1072,1199,1314,1450,1614,1744,1870,1980,2074,2207,2296,2405,2506,2607,2710,2833,2947,3073,3195,3309,3428,3546,3670,3783,3902,3989,4072,4180,4314,4473", "endColumns": "106,100,96,98,127,113,98,90,130,126,114,135,163,129,125,109,93,132,88,108,100,100,102,122,113,125,121,113,118,117,123,112,118,86,82,107,133,158,95", "endOffsets": "207,308,405,504,632,746,845,936,1067,1194,1309,1445,1609,1739,1865,1975,2069,2202,2291,2400,2501,2602,2705,2828,2942,3068,3190,3304,3423,3541,3665,3778,3897,3984,4067,4175,4309,4468,4564"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4316,4423,4524,4621,4720,4848,4962,5061,5152,5283,5410,5525,5661,5825,5955,6081,6191,6285,6418,6507,6616,6717,6818,6921,7044,7158,7284,7406,7520,7639,7757,7881,7994,8113,8200,8283,8391,8525,15308", "endColumns": "106,100,96,98,127,113,98,90,130,126,114,135,163,129,125,109,93,132,88,108,100,100,102,122,113,125,121,113,118,117,123,112,118,86,82,107,133,158,95", "endOffsets": "4418,4519,4616,4715,4843,4957,5056,5147,5278,5405,5520,5656,5820,5950,6076,6186,6280,6413,6502,6611,6712,6813,6916,7039,7153,7279,7401,7515,7634,7752,7876,7989,8108,8195,8278,8386,8520,8679,15399"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-ro/values-ro.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,289,373,470,572,660,738,825,916,998,1086,1176,1249,1334,1408,1487,1562,1639,1706", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,84,73,78,74,76,66,114", "endOffsets": "284,368,465,567,655,733,820,911,993,1081,1171,1244,1329,1403,1482,1557,1634,1701,1816"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3683,3780,3864,3961,4063,4151,4229,15135,15226,15404,15492,15665,15738,15823,15897,15976,16152,16229,16296", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,84,73,78,74,76,66,114", "endOffsets": "3775,3859,3956,4058,4146,4224,4311,15221,15303,15487,15577,15733,15818,15892,15971,16046,16224,16291,16406"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-ro/values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2956,3054,3156,3256,3355,3457,3566,16051", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3049,3151,3251,3350,3452,3561,3678,16147"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-ro/values-ro.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,236", "endColumns": "85,94,99", "endOffsets": "136,231,331"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2870,16411,16506", "endColumns": "85,94,99", "endOffsets": "2951,16501,16601"}}]}]}