{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-is/values-is.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-is/values-is.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14596", "endColumns": "90", "endOffsets": "14682"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2870,2965,3072,3169,3269,3372,3476,15570", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "2960,3067,3164,3264,3367,3471,3582,15666"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,407,500,621,733,830,928,1054,1179,1282,1403,1551,1674,1796,1904,1992,2113,2203,2308,2416,2520,2621,2739,2849,2962,3074,3176,3282,3394,3509,3617,3728,3815,3898,4002,4138,4295", "endColumns": "106,100,93,92,120,111,96,97,125,124,102,120,147,122,121,107,87,120,89,104,107,103,100,117,109,112,111,101,105,111,114,107,110,86,82,103,135,156,88", "endOffsets": "207,308,402,495,616,728,825,923,1049,1174,1277,1398,1546,1669,1791,1899,1987,2108,2198,2303,2411,2515,2616,2734,2844,2957,3069,3171,3277,3389,3504,3612,3723,3810,3893,3997,4133,4290,4379"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4217,4324,4425,4519,4612,4733,4845,4942,5040,5166,5291,5394,5515,5663,5786,5908,6016,6104,6225,6315,6420,6528,6632,6733,6851,6961,7074,7186,7288,7394,7506,7621,7729,7840,7927,8010,8114,8250,14858", "endColumns": "106,100,93,92,120,111,96,97,125,124,102,120,147,122,121,107,87,120,89,104,107,103,100,117,109,112,111,101,105,111,114,107,110,86,82,103,135,156,88", "endOffsets": "4319,4420,4514,4607,4728,4840,4937,5035,5161,5286,5389,5510,5658,5781,5903,6011,6099,6220,6310,6415,6523,6627,6728,6846,6956,7069,7181,7283,7389,7501,7616,7724,7835,7922,8005,8109,8245,8402,14942"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,794,874,965,1058,1154,1248,1355,1448,1543,1638,1729,1823,1904,2014,2122,2220,2329,2428,2531,2686,2784", "endColumns": "99,96,111,84,100,113,79,79,90,92,95,93,106,92,94,94,90,93,80,109,107,97,108,98,102,154,97,80", "endOffsets": "200,297,409,494,595,709,789,869,960,1053,1149,1243,1350,1443,1538,1633,1724,1818,1899,2009,2117,2215,2324,2423,2526,2681,2779,2860"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,794,874,965,1058,1154,1248,1355,1448,1543,1638,1729,1823,1904,2014,2122,2220,2329,2428,2531,2686,15108", "endColumns": "99,96,111,84,100,113,79,79,90,92,95,93,106,92,94,94,90,93,80,109,107,97,108,98,102,154,97,80", "endOffsets": "200,297,409,494,595,709,789,869,960,1053,1149,1243,1350,1443,1538,1633,1724,1818,1899,2009,2117,2215,2324,2423,2526,2681,2779,15184"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-is/values-is.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,228", "endColumns": "85,86,86", "endOffsets": "136,223,310"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2784,15936,16023", "endColumns": "85,86,86", "endOffsets": "2865,16018,16105"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-is/values-is.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,283,364,463,562,647,727,822,911,993,1071,1154,1224,1311,1386,1461,1535,1612,1680", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,86,74,74,73,76,67,119", "endOffsets": "278,359,458,557,642,722,817,906,988,1066,1149,1219,1306,1381,1456,1530,1607,1675,1795"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3587,3678,3759,3858,3957,4042,4122,14687,14776,14947,15025,15189,15259,15346,15421,15496,15671,15748,15816", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,86,74,74,73,76,67,119", "endOffsets": "3673,3754,3853,3952,4037,4117,4212,14771,14853,15020,15103,15254,15341,15416,15491,15565,15743,15811,15931"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-is/values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,500,597,696,812,953,1080,1215,1305,1406,1503,1603,1718,1844,1950,2075,2199,2341,2512,2635,2751,2870,2992,3090,3188,3297,3419,3525,3633,3736,3866,4001,4109,4214,4290,4384,4477,4591,4676,4761,4870,4950,5041,5142,5243,5338,5446,5534,5639,5740,5846,5966,6046,6148", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "164,276,383,495,592,691,807,948,1075,1210,1300,1401,1498,1598,1713,1839,1945,2070,2194,2336,2507,2630,2746,2865,2987,3085,3183,3292,3414,3520,3628,3731,3861,3996,4104,4209,4285,4379,4472,4586,4671,4756,4865,4945,5036,5137,5238,5333,5441,5529,5634,5735,5841,5961,6041,6143,6239"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8407,8521,8633,8740,8852,8949,9048,9164,9305,9432,9567,9657,9758,9855,9955,10070,10196,10302,10427,10551,10693,10864,10987,11103,11222,11344,11442,11540,11649,11771,11877,11985,12088,12218,12353,12461,12566,12642,12736,12829,12943,13028,13113,13222,13302,13393,13494,13595,13690,13798,13886,13991,14092,14198,14318,14398,14500", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "8516,8628,8735,8847,8944,9043,9159,9300,9427,9562,9652,9753,9850,9950,10065,10191,10297,10422,10546,10688,10859,10982,11098,11217,11339,11437,11535,11644,11766,11872,11980,12083,12213,12348,12456,12561,12637,12731,12824,12938,13023,13108,13217,13297,13388,13489,13590,13685,13793,13881,13986,14087,14193,14313,14393,14495,14591"}}]}]}