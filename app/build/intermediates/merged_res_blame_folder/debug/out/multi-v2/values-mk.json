{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-mk/values-mk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-mk/values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,636,751,849,943,1077,1208,1315,1440,1631,1757,1880,1987,2080,2212,2302,2408,2520,2622,2725,2845,2982,3095,3205,3314,3430,3545,3666,3779,3898,3985,4073,4186,4327,4488", "endColumns": "106,100,96,93,131,114,97,93,133,130,106,124,190,125,122,106,92,131,89,105,111,101,102,119,136,112,109,108,115,114,120,112,118,86,87,112,140,160,98", "endOffsets": "207,308,405,499,631,746,844,938,1072,1203,1310,1435,1626,1752,1875,1982,2075,2207,2297,2403,2515,2617,2720,2840,2977,3090,3200,3309,3425,3540,3661,3774,3893,3980,4068,4181,4322,4483,4582"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4314,4421,4522,4619,4713,4845,4960,5058,5152,5286,5417,5524,5649,5840,5966,6089,6196,6289,6421,6511,6617,6729,6831,6934,7054,7191,7304,7414,7523,7639,7754,7875,7988,8107,8194,8282,8395,8536,15316", "endColumns": "106,100,96,93,131,114,97,93,133,130,106,124,190,125,122,106,92,131,89,105,111,101,102,119,136,112,109,108,115,114,120,112,118,86,87,112,140,160,98", "endOffsets": "4416,4517,4614,4708,4840,4955,5053,5147,5281,5412,5519,5644,5835,5961,6084,6191,6284,6416,6506,6612,6724,6826,6929,7049,7186,7299,7409,7518,7634,7749,7870,7983,8102,8189,8277,8390,8531,8692,15410"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-mk/values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,616,735,818,900,997,1096,1193,1293,1400,1499,1600,1696,1793,1884,1971,2077,2184,2285,2392,2503,2607,2763,2861", "endColumns": "107,103,107,85,104,118,82,81,96,98,96,99,106,98,100,95,96,90,86,105,106,100,106,110,103,155,97,83", "endOffsets": "208,312,420,506,611,730,813,895,992,1091,1188,1288,1395,1494,1595,1691,1788,1879,1966,2072,2179,2280,2387,2498,2602,2758,2856,2940"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,616,735,818,900,997,1096,1193,1293,1400,1499,1600,1696,1793,1884,1971,2077,2184,2285,2392,2503,2607,2763,15588", "endColumns": "107,103,107,85,104,118,82,81,96,98,96,99,106,98,100,95,96,90,86,105,106,100,106,110,103,155,97,83", "endOffsets": "208,312,420,506,611,730,813,895,992,1091,1188,1288,1395,1494,1595,1691,1788,1879,1966,2072,2179,2280,2387,2498,2602,2758,2856,15667"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-mk/values-mk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,144,238", "endColumns": "88,93,95", "endOffsets": "139,233,329"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2861,16442,16536", "endColumns": "88,93,95", "endOffsets": "2945,16531,16627"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-mk/values-mk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "96", "endOffsets": "147"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15043", "endColumns": "96", "endOffsets": "15135"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-mk/values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,292,408,526,623,718,830,963,1084,1232,1317,1416,1510,1606,1721,1845,1949,2094,2238,2380,2554,2685,2806,2933,3058,3153,3251,3377,3512,3612,3714,3827,3968,4117,4233,4335,4412,4506,4601,4720,4812,4898,5012,5095,5178,5278,5380,5477,5574,5662,5769,5869,5971,6104,6187,6298", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "169,287,403,521,618,713,825,958,1079,1227,1312,1411,1505,1601,1716,1840,1944,2089,2233,2375,2549,2680,2801,2928,3053,3148,3246,3372,3507,3607,3709,3822,3963,4112,4228,4330,4407,4501,4596,4715,4807,4893,5007,5090,5173,5273,5375,5472,5569,5657,5764,5864,5966,6099,6182,6293,6396"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8697,8816,8934,9050,9168,9265,9360,9472,9605,9726,9874,9959,10058,10152,10248,10363,10487,10591,10736,10880,11022,11196,11327,11448,11575,11700,11795,11893,12019,12154,12254,12356,12469,12610,12759,12875,12977,13054,13148,13243,13362,13454,13540,13654,13737,13820,13920,14022,14119,14216,14304,14411,14511,14613,14746,14829,14940", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,118,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "8811,8929,9045,9163,9260,9355,9467,9600,9721,9869,9954,10053,10147,10243,10358,10482,10586,10731,10875,11017,11191,11322,11443,11570,11695,11790,11888,12014,12149,12249,12351,12464,12605,12754,12870,12972,13049,13143,13238,13357,13449,13535,13649,13732,13815,13915,14017,14114,14211,14299,14406,14506,14608,14741,14824,14935,15038"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-mk/values-mk.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "195,299,389,485,588,673,750,840,932,1016,1100,1189,1261,1354,1431,1509,1585,1666,1737", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,92,76,77,75,80,70,120", "endOffsets": "294,384,480,583,668,745,835,927,1011,1095,1184,1256,1349,1426,1504,1580,1661,1732,1853"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3669,3773,3863,3959,4062,4147,4224,15140,15232,15415,15499,15672,15744,15837,15914,15992,16169,16250,16321", "endColumns": "103,89,95,102,84,76,89,91,83,83,88,71,92,76,77,75,80,70,120", "endOffsets": "3768,3858,3954,4057,4142,4219,4309,15227,15311,15494,15583,15739,15832,15909,15987,16063,16245,16316,16437"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-mk/values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2950,3048,3150,3247,3345,3450,3553,16068", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "3043,3145,3242,3340,3445,3548,3664,16164"}}]}]}