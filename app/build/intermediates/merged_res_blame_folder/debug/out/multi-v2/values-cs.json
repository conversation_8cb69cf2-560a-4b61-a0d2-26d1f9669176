{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-cs/values-cs.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,409,527,628,723,835,969,1085,1224,1309,1409,1502,1599,1715,1837,1942,2075,2205,2347,2510,2638,2755,2879,3000,3091,3188,3308,3423,3521,3624,3732,3864,4005,4115,4214,4298,4392,4487,4599,4691,4777,4890,4970,5056,5157,5260,5357,5458,5546,5652,5751,5854,5973,6053,6157", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,111,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "168,285,404,522,623,718,830,964,1080,1219,1304,1404,1497,1594,1710,1832,1937,2070,2200,2342,2505,2633,2750,2874,2995,3086,3183,3303,3418,3516,3619,3727,3859,4000,4110,4209,4293,4387,4482,4594,4686,4772,4885,4965,5051,5152,5255,5352,5453,5541,5647,5746,5849,5968,6048,6152,6247"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8596,8714,8831,8950,9068,9169,9264,9376,9510,9626,9765,9850,9950,10043,10140,10256,10378,10483,10616,10746,10888,11051,11179,11296,11420,11541,11632,11729,11849,11964,12062,12165,12273,12405,12546,12656,12755,12839,12933,13028,13140,13232,13318,13431,13511,13597,13698,13801,13898,13999,14087,14193,14292,14395,14514,14594,14698", "endColumns": "117,116,118,117,100,94,111,133,115,138,84,99,92,96,115,121,104,132,129,141,162,127,116,123,120,90,96,119,114,97,102,107,131,140,109,98,83,93,94,111,91,85,112,79,85,100,102,96,100,87,105,98,102,118,79,103,94", "endOffsets": "8709,8826,8945,9063,9164,9259,9371,9505,9621,9760,9845,9945,10038,10135,10251,10373,10478,10611,10741,10883,11046,11174,11291,11415,11536,11627,11724,11844,11959,12057,12160,12268,12400,12541,12651,12750,12834,12928,13023,13135,13227,13313,13426,13506,13592,13693,13796,13893,13994,14082,14188,14287,14390,14509,14589,14693,14788"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,144,230", "endColumns": "88,85,88", "endOffsets": "139,225,314"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2809,16159,16245", "endColumns": "88,85,88", "endOffsets": "2893,16240,16329"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-cs/values-cs.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14793", "endColumns": "92", "endOffsets": "14881"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,812,893,984,1077,1173,1267,1362,1455,1550,1647,1738,1829,1913,2017,2126,2225,2331,2441,2548,2711,2809", "endColumns": "106,101,108,85,104,116,80,80,90,92,95,93,94,92,94,96,90,90,83,103,108,98,105,109,106,162,97,81", "endOffsets": "207,309,418,504,609,726,807,888,979,1072,1168,1262,1357,1450,1545,1642,1733,1824,1908,2012,2121,2220,2326,2436,2543,2706,2804,2886"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,509,614,731,812,893,984,1077,1173,1267,1362,1455,1550,1647,1738,1829,1913,2017,2126,2225,2331,2441,2548,2711,15319", "endColumns": "106,101,108,85,104,116,80,80,90,92,95,93,94,92,94,96,90,90,83,103,108,98,105,109,106,162,97,81", "endOffsets": "207,309,418,504,609,726,807,888,979,1072,1168,1262,1357,1450,1545,1642,1733,1824,1908,2012,2121,2220,2326,2436,2543,2706,2804,15396"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,638,752,849,939,1060,1180,1286,1410,1573,1697,1820,1923,2018,2148,2240,2343,2445,2557,2657,2774,2895,3017,3138,3246,3362,3486,3612,3736,3862,3949,4033,4137,4272,4438", "endColumns": "106,100,98,95,129,113,96,89,120,119,105,123,162,123,122,102,94,129,91,102,101,111,99,116,120,121,120,107,115,123,125,123,125,86,83,103,134,165,92", "endOffsets": "207,308,407,503,633,747,844,934,1055,1175,1281,1405,1568,1692,1815,1918,2013,2143,2235,2338,2440,2552,2652,2769,2890,3012,3133,3241,3357,3481,3607,3731,3857,3944,4028,4132,4267,4433,4526"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4263,4370,4471,4570,4666,4796,4910,5007,5097,5218,5338,5444,5568,5731,5855,5978,6081,6176,6306,6398,6501,6603,6715,6815,6932,7053,7175,7296,7404,7520,7644,7770,7894,8020,8107,8191,8295,8430,15058", "endColumns": "106,100,98,95,129,113,96,89,120,119,105,123,162,123,122,102,94,129,91,102,101,111,99,116,120,121,120,107,115,123,125,123,125,86,83,103,134,165,92", "endOffsets": "4365,4466,4565,4661,4791,4905,5002,5092,5213,5333,5439,5563,5726,5850,5973,6076,6171,6301,6393,6496,6598,6710,6810,6927,7048,7170,7291,7399,7515,7639,7765,7889,8015,8102,8186,8290,8425,8591,15146"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-cs/values-cs.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "195,288,371,465,567,659,737,829,920,1001,1083,1169,1241,1330,1408,1484,1559,1638,1706", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,88,77,75,74,78,67,119", "endOffsets": "283,366,460,562,654,732,824,915,996,1078,1164,1236,1325,1403,1479,1554,1633,1701,1821"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3629,3722,3805,3899,4001,4093,4171,14886,14977,15151,15233,15401,15473,15562,15640,15716,15892,15971,16039", "endColumns": "92,82,93,101,91,77,91,90,80,81,85,71,88,77,75,74,78,67,119", "endOffsets": "3717,3800,3894,3996,4088,4166,4258,14972,15053,15228,15314,15468,15557,15635,15711,15786,15966,16034,16154"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-cs/values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2898,2996,3098,3199,3298,3403,3510,15791", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "2991,3093,3194,3293,3398,3505,3624,15887"}}]}]}