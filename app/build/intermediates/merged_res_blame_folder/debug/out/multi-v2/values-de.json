{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-de/values-de.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-de/values-de.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15048", "endColumns": "93", "endOffsets": "15137"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-de/values-de.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,230", "endColumns": "87,86,89", "endOffsets": "138,225,315"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2843,16402,16489", "endColumns": "87,86,89", "endOffsets": "2926,16484,16574"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-de/values-de.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,290,378,476,576,663,748,840,929,1017,1098,1182,1257,1347,1422,1494,1564,1643,1709", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,89,74,71,69,78,65,119", "endOffsets": "285,373,471,571,658,743,835,924,1012,1093,1177,1252,1342,1417,1489,1559,1638,1704,1824"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3662,3758,3846,3944,4044,4131,4216,15142,15231,15407,15488,15654,15729,15819,15894,15966,16137,16216,16282", "endColumns": "95,87,97,99,86,84,91,88,87,80,83,74,89,74,71,69,78,65,119", "endOffsets": "3753,3841,3939,4039,4126,4211,4303,15226,15314,15483,15567,15724,15814,15889,15961,16031,16211,16277,16397"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,611,726,803,879,971,1065,1162,1263,1371,1471,1575,1675,1773,1870,1952,2063,2166,2265,2376,2478,2585,2741,2843", "endColumns": "104,97,111,85,104,114,76,75,91,93,96,100,107,99,103,99,97,96,81,110,102,98,110,101,106,155,101,81", "endOffsets": "205,303,415,501,606,721,798,874,966,1060,1157,1258,1366,1466,1570,1670,1768,1865,1947,2058,2161,2260,2371,2473,2580,2736,2838,2920"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,420,506,611,726,803,879,971,1065,1162,1263,1371,1471,1575,1675,1773,1870,1952,2063,2166,2265,2376,2478,2585,2741,15572", "endColumns": "104,97,111,85,104,114,76,75,91,93,96,100,107,99,103,99,97,96,81,110,102,98,110,101,106,155,101,81", "endOffsets": "205,303,415,501,606,721,798,874,966,1060,1157,1258,1366,1466,1570,1670,1768,1865,1947,2058,2161,2260,2371,2473,2580,2736,2838,15649"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,502,621,733,831,929,1058,1184,1285,1404,1591,1718,1842,1951,2045,2173,2269,2376,2492,2605,2704,2820,2939,3058,3174,3293,3418,3539,3666,3779,3898,3985,4068,4172,4310,4468", "endColumns": "106,100,94,93,118,111,97,97,128,125,100,118,186,126,123,108,93,127,95,106,115,112,98,115,118,118,115,118,124,120,126,112,118,86,82,103,137,157,87", "endOffsets": "207,308,403,497,616,728,826,924,1053,1179,1280,1399,1586,1713,1837,1946,2040,2168,2264,2371,2487,2600,2699,2815,2934,3053,3169,3288,3413,3534,3661,3774,3893,3980,4063,4167,4305,4463,4551"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4308,4415,4516,4611,4705,4824,4936,5034,5132,5261,5387,5488,5607,5794,5921,6045,6154,6248,6376,6472,6579,6695,6808,6907,7023,7142,7261,7377,7496,7621,7742,7869,7982,8101,8188,8271,8375,8513,15319", "endColumns": "106,100,94,93,118,111,97,97,128,125,100,118,186,126,123,108,93,127,95,106,115,112,98,115,118,118,115,118,124,120,126,112,118,86,82,103,137,157,87", "endOffsets": "4410,4511,4606,4700,4819,4931,5029,5127,5256,5382,5483,5602,5789,5916,6040,6149,6243,6371,6467,6574,6690,6803,6902,7018,7137,7256,7372,7491,7616,7737,7864,7977,8096,8183,8266,8370,8508,8666,15402"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,186,315,424,553,663,758,870,1014,1132,1288,1373,1478,1573,1675,1793,1919,2029,2165,2302,2437,2616,2744,2867,2995,3120,3216,3314,3434,3563,3663,3768,3870,4011,4159,4265,4367,4447,4543,4638,4758,4844,4933,5034,5114,5200,5300,5406,5501,5602,5690,5799,5900,6004,6142,6231,6336", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "181,310,419,548,658,753,865,1009,1127,1283,1368,1473,1568,1670,1788,1914,2024,2160,2297,2432,2611,2739,2862,2990,3115,3211,3309,3429,3558,3658,3763,3865,4006,4154,4260,4362,4442,4538,4633,4753,4839,4928,5029,5109,5195,5295,5401,5496,5597,5685,5794,5895,5999,6137,6226,6331,6427"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8671,8802,8931,9040,9169,9279,9374,9486,9630,9748,9904,9989,10094,10189,10291,10409,10535,10645,10781,10918,11053,11232,11360,11483,11611,11736,11832,11930,12050,12179,12279,12384,12486,12627,12775,12881,12983,13063,13159,13254,13374,13460,13549,13650,13730,13816,13916,14022,14117,14218,14306,14415,14516,14620,14758,14847,14952", "endColumns": "130,128,108,128,109,94,111,143,117,155,84,104,94,101,117,125,109,135,136,134,178,127,122,127,124,95,97,119,128,99,104,101,140,147,105,101,79,95,94,119,85,88,100,79,85,99,105,94,100,87,108,100,103,137,88,104,95", "endOffsets": "8797,8926,9035,9164,9274,9369,9481,9625,9743,9899,9984,10089,10184,10286,10404,10530,10640,10776,10913,11048,11227,11355,11478,11606,11731,11827,11925,12045,12174,12274,12379,12481,12622,12770,12876,12978,13058,13154,13249,13369,13455,13544,13645,13725,13811,13911,14017,14112,14213,14301,14410,14511,14615,14753,14842,14947,15043"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-de/values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2931,3029,3131,3231,3331,3439,3544,16036", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "3024,3126,3226,3326,3434,3539,3657,16132"}}]}]}