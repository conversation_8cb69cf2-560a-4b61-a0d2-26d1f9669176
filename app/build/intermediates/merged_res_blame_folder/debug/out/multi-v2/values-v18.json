{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-v18/values-v18.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-v18/values-v18.xml", "from": {"startLines": "2,12", "startColumns": "4,4", "startOffsets": "55,765", "endLines": "11,20", "endColumns": "12,12", "endOffsets": "760,1352"}, "to": {"startLines": "3,13", "startColumns": "4,4", "startOffsets": "104,814", "endLines": "12,21", "endColumns": "12,12", "endOffsets": "809,1401"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-v18/values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "48", "endOffsets": "99"}}]}]}