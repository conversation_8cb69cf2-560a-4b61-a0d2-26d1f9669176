{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2969,3067,3169,3268,3370,3474,3578,16374", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "3062,3164,3263,3365,3469,3573,3687,16470"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,507,630,745,844,938,1070,1198,1303,1428,1607,1743,1875,1982,2072,2208,2297,2407,2516,2624,2725,2844,2970,3097,3220,3337,3463,3598,3739,3861,3989,4076,4164,4272,4416,4578", "endColumns": "106,100,97,95,122,114,98,93,131,127,104,124,178,135,131,106,89,135,88,109,108,107,100,118,125,126,122,116,125,134,140,121,127,86,87,107,143,161,95", "endOffsets": "207,308,406,502,625,740,839,933,1065,1193,1298,1423,1602,1738,1870,1977,2067,2203,2292,2402,2511,2619,2720,2839,2965,3092,3215,3332,3458,3593,3734,3856,3984,4071,4159,4267,4411,4573,4669"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4344,4451,4552,4650,4746,4869,4984,5083,5177,5309,5437,5542,5667,5846,5982,6114,6221,6311,6447,6536,6646,6755,6863,6964,7083,7209,7336,7459,7576,7702,7837,7978,8100,8228,8315,8403,8511,8655,15638", "endColumns": "106,100,97,95,122,114,98,93,131,127,104,124,178,135,131,106,89,135,88,109,108,107,100,118,125,126,122,116,125,134,140,121,127,86,87,107,143,161,95", "endOffsets": "4446,4547,4645,4741,4864,4979,5078,5172,5304,5432,5537,5662,5841,5977,6109,6216,6306,6442,6531,6641,6750,6858,6959,7078,7204,7331,7454,7571,7697,7832,7973,8095,8223,8310,8398,8506,8650,8812,15729"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,347,469,617,743,837,949,1091,1210,1369,1453,1554,1655,1756,1877,2012,2118,2268,2414,2550,2752,2881,2999,3122,3255,3357,3462,3586,3714,3816,3928,4033,4178,4330,4439,4548,4626,4719,4814,4932,5022,5108,5215,5295,5380,5477,5588,5681,5785,5873,5989,6090,6199,6321,6401,6511", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,117,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "197,342,464,612,738,832,944,1086,1205,1364,1448,1549,1650,1751,1872,2007,2113,2263,2409,2545,2747,2876,2994,3117,3250,3352,3457,3581,3709,3811,3923,4028,4173,4325,4434,4543,4621,4714,4809,4927,5017,5103,5210,5290,5375,5472,5583,5676,5780,5868,5984,6085,6194,6316,6396,6506,6603"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8817,8964,9109,9231,9379,9505,9599,9711,9853,9972,10131,10215,10316,10417,10518,10639,10774,10880,11030,11176,11312,11514,11643,11761,11884,12017,12119,12224,12348,12476,12578,12690,12795,12940,13092,13201,13310,13388,13481,13576,13694,13784,13870,13977,14057,14142,14239,14350,14443,14547,14635,14751,14852,14961,15083,15163,15273", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,117,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "8959,9104,9226,9374,9500,9594,9706,9848,9967,10126,10210,10311,10412,10513,10634,10769,10875,11025,11171,11307,11509,11638,11756,11879,12012,12114,12219,12343,12471,12573,12685,12790,12935,13087,13196,13305,13383,13476,13571,13689,13779,13865,13972,14052,14137,14234,14345,14438,14542,14630,14746,14847,14956,15078,15158,15268,15365"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,442,529,645,775,858,938,1029,1122,1221,1316,1417,1510,1603,1698,1789,1880,1976,2086,2198,2301,2412,2519,2621,2780,2879", "endColumns": "110,114,110,86,115,129,82,79,90,92,98,94,100,92,92,94,90,90,95,109,111,102,110,106,101,158,98,85", "endOffsets": "211,326,437,524,640,770,853,933,1024,1117,1216,1311,1412,1505,1598,1693,1784,1875,1971,2081,2193,2296,2407,2514,2616,2775,2874,2960"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,442,529,645,775,858,938,1029,1122,1221,1316,1417,1510,1603,1698,1789,1880,1976,2086,2198,2301,2412,2519,2621,2780,15900", "endColumns": "110,114,110,86,115,129,82,79,90,92,98,94,100,92,92,94,90,90,95,109,111,102,110,106,101,158,98,85", "endOffsets": "211,326,437,524,640,770,853,933,1024,1117,1216,1311,1412,1505,1598,1693,1784,1875,1971,2081,2193,2296,2407,2514,2616,2775,2874,15981"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,233", "endColumns": "89,87,94", "endOffsets": "140,228,323"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2879,16745,16833", "endColumns": "89,87,94", "endOffsets": "2964,16828,16923"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15370", "endColumns": "88", "endOffsets": "15454"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-fr-rCA/values-fr-rCA.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,295,383,481,587,674,754,848,940,1027,1108,1193,1269,1354,1429,1507,1581,1660,1729", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,84,74,77,73,78,68,121", "endOffsets": "290,378,476,582,669,749,843,935,1022,1103,1188,1264,1349,1424,1502,1576,1655,1724,1846"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3692,3791,3879,3977,4083,4170,4250,15459,15551,15734,15815,15986,16062,16147,16222,16300,16475,16554,16623", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,84,74,77,73,78,68,121", "endOffsets": "3786,3874,3972,4078,4165,4245,4339,15546,15633,15810,15895,16057,16142,16217,16295,16369,16549,16618,16740"}}]}]}