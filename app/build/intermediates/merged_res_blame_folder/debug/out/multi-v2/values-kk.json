{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-kk/values-kk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-kk/values-kk.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "184,277,360,465,567,654,735,828,918,1000,1083,1168,1241,1327,1401,1477,1551,1627,1697", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,85,73,75,73,75,69,117", "endOffsets": "272,355,460,562,649,730,823,913,995,1078,1163,1236,1322,1396,1472,1546,1622,1692,1810"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3609,3702,3785,3890,3992,4079,4160,14897,14987,15159,15242,15408,15481,15567,15641,15717,15892,15968,16038", "endColumns": "92,82,104,101,86,80,92,89,81,82,84,72,85,73,75,73,75,69,117", "endOffsets": "3697,3780,3885,3987,4074,4155,4248,14982,15064,15237,15322,15476,15562,15636,15712,15786,15963,16033,16151"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,320,430,515,621,740,821,901,992,1085,1181,1275,1376,1469,1564,1661,1752,1844,1925,2028,2133,2231,2338,2447,2547,2713,2812", "endColumns": "111,102,109,84,105,118,80,79,90,92,95,93,100,92,94,96,90,91,80,102,104,97,106,108,99,165,98,80", "endOffsets": "212,315,425,510,616,735,816,896,987,1080,1176,1270,1371,1464,1559,1656,1747,1839,1920,2023,2128,2226,2333,2442,2542,2708,2807,2888"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,320,430,515,621,740,821,901,992,1085,1181,1275,1376,1469,1564,1661,1752,1844,1925,2028,2133,2231,2338,2447,2547,2713,15327", "endColumns": "111,102,109,84,105,118,80,79,90,92,95,93,100,92,94,96,90,91,80,102,104,97,106,108,99,165,98,80", "endOffsets": "212,315,425,510,616,735,816,896,987,1080,1176,1270,1371,1464,1559,1656,1747,1839,1920,2023,2128,2226,2333,2442,2542,2708,2807,15403"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,506,629,742,840,931,1055,1177,1283,1407,1582,1702,1820,1930,2022,2159,2250,2358,2471,2577,2679,2798,2929,3045,3159,3261,3368,3484,3608,3720,3840,3927,4010,4114,4248,4403", "endColumns": "106,100,98,93,122,112,97,90,123,121,105,123,174,119,117,109,91,136,90,107,112,105,101,118,130,115,113,101,106,115,123,111,119,86,82,103,133,154,89", "endOffsets": "207,308,407,501,624,737,835,926,1050,1172,1278,1402,1577,1697,1815,1925,2017,2154,2245,2353,2466,2572,2674,2793,2924,3040,3154,3256,3363,3479,3603,3715,3835,3922,4005,4109,4243,4398,4488"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4253,4360,4461,4560,4654,4777,4890,4988,5079,5203,5325,5431,5555,5730,5850,5968,6078,6170,6307,6398,6506,6619,6725,6827,6946,7077,7193,7307,7409,7516,7632,7756,7868,7988,8075,8158,8262,8396,15069", "endColumns": "106,100,98,93,122,112,97,90,123,121,105,123,174,119,117,109,91,136,90,107,112,105,101,118,130,115,113,101,106,115,123,111,119,86,82,103,133,154,89", "endOffsets": "4355,4456,4555,4649,4772,4885,4983,5074,5198,5320,5426,5550,5725,5845,5963,6073,6165,6302,6393,6501,6614,6720,6822,6941,7072,7188,7302,7404,7511,7627,7751,7863,7983,8070,8153,8257,8391,8546,15154"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,359,462,566,663,774", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "150,252,354,457,561,658,769,870"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2890,2990,3092,3194,3297,3401,3498,15791", "endColumns": "99,101,101,102,103,96,110,100", "endOffsets": "2985,3087,3189,3292,3396,3493,3604,15887"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-kk/values-kk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14807", "endColumns": "89", "endOffsets": "14892"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,406,522,624,725,843,981,1106,1231,1315,1418,1508,1605,1721,1845,1953,2095,2235,2367,2526,2649,2764,2883,2998,3089,3187,3310,3445,3549,3660,3766,3905,4050,4158,4258,4344,4437,4530,4638,4724,4808,4912,5001,5086,5187,5291,5388,5484,5571,5675,5774,5872,6009,6099,6210", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,107,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "168,286,401,517,619,720,838,976,1101,1226,1310,1413,1503,1600,1716,1840,1948,2090,2230,2362,2521,2644,2759,2878,2993,3084,3182,3305,3440,3544,3655,3761,3900,4045,4153,4253,4339,4432,4525,4633,4719,4803,4907,4996,5081,5182,5286,5383,5479,5566,5670,5769,5867,6004,6094,6205,6306"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8551,8669,8787,8902,9018,9120,9221,9339,9477,9602,9727,9811,9914,10004,10101,10217,10341,10449,10591,10731,10863,11022,11145,11260,11379,11494,11585,11683,11806,11941,12045,12156,12262,12401,12546,12654,12754,12840,12933,13026,13134,13220,13304,13408,13497,13582,13683,13787,13884,13980,14067,14171,14270,14368,14505,14595,14706", "endColumns": "117,117,114,115,101,100,117,137,124,124,83,102,89,96,115,123,107,141,139,131,158,122,114,118,114,90,97,122,134,103,110,105,138,144,107,99,85,92,92,107,85,83,103,88,84,100,103,96,95,86,103,98,97,136,89,110,100", "endOffsets": "8664,8782,8897,9013,9115,9216,9334,9472,9597,9722,9806,9909,9999,10096,10212,10336,10444,10586,10726,10858,11017,11140,11255,11374,11489,11580,11678,11801,11936,12040,12151,12257,12396,12541,12649,12749,12835,12928,13021,13129,13215,13299,13403,13492,13577,13678,13782,13879,13975,14062,14166,14265,14363,14500,14590,14701,14802"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-kk/values-kk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,225", "endColumns": "77,91,95", "endOffsets": "128,220,316"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2812,16156,16248", "endColumns": "77,91,95", "endOffsets": "2885,16243,16339"}}]}]}