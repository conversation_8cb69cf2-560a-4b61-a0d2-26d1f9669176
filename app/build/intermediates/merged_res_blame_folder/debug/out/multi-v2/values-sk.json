{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-sk/values-sk.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,227", "endColumns": "87,83,86", "endOffsets": "138,222,309"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2830,16215,16299", "endColumns": "87,83,86", "endOffsets": "2913,16294,16381"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-sk/values-sk.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,289,373,468,571,663,742,836,926,1007,1090,1177,1249,1339,1417,1493,1568,1646,1714", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,89,77,75,74,77,67,113", "endOffsets": "284,368,463,566,658,737,831,921,1002,1085,1172,1244,1334,1412,1488,1563,1641,1709,1823"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3655,3750,3834,3929,4032,4124,4203,14939,15029,15205,15288,15463,15535,15625,15703,15779,15955,16033,16101", "endColumns": "94,83,94,102,91,78,93,89,80,82,86,71,89,77,75,74,77,67,113", "endOffsets": "3745,3829,3924,4027,4119,4198,4292,15024,15105,15283,15370,15530,15620,15698,15774,15849,16028,16096,16210"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,312,423,509,617,735,816,897,988,1081,1180,1274,1375,1468,1563,1661,1752,1843,1927,2032,2141,2240,2346,2457,2566,2732,2830", "endColumns": "106,99,110,85,107,117,80,80,90,92,98,93,100,92,94,97,90,90,83,104,108,98,105,110,108,165,97,87", "endOffsets": "207,307,418,504,612,730,811,892,983,1076,1175,1269,1370,1463,1558,1656,1747,1838,1922,2027,2136,2235,2341,2452,2561,2727,2825,2913"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,312,423,509,617,735,816,897,988,1081,1180,1274,1375,1468,1563,1661,1752,1843,1927,2032,2141,2240,2346,2457,2566,2732,15375", "endColumns": "106,99,110,85,107,117,80,80,90,92,98,93,100,92,94,97,90,90,83,104,108,98,105,110,108,165,97,87", "endOffsets": "207,307,418,504,612,730,811,892,983,1076,1175,1269,1370,1463,1558,1656,1747,1838,1922,2027,2136,2235,2341,2452,2561,2727,2825,15458"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,626,739,836,929,1056,1182,1290,1416,1578,1711,1843,1948,2043,2173,2265,2371,2473,2585,2685,2802,2922,3044,3165,3279,3405,3515,3633,3739,3853,3940,4024,4140,4283,4458", "endColumns": "106,100,98,95,117,112,96,92,126,125,107,125,161,132,131,104,94,129,91,105,101,111,99,116,119,121,120,113,125,109,117,105,113,86,83,115,142,174,94", "endOffsets": "207,308,407,503,621,734,831,924,1051,1177,1285,1411,1573,1706,1838,1943,2038,2168,2260,2366,2468,2580,2680,2797,2917,3039,3160,3274,3400,3510,3628,3734,3848,3935,4019,4135,4278,4453,4548"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4297,4404,4505,4604,4700,4818,4931,5028,5121,5248,5374,5482,5608,5770,5903,6035,6140,6235,6365,6457,6563,6665,6777,6877,6994,7114,7236,7357,7471,7597,7707,7825,7931,8045,8132,8216,8332,8475,15110", "endColumns": "106,100,98,95,117,112,96,92,126,125,107,125,161,132,131,104,94,129,91,105,101,111,99,116,119,121,120,113,125,109,117,105,113,86,83,115,142,174,94", "endOffsets": "4399,4500,4599,4695,4813,4926,5023,5116,5243,5369,5477,5603,5765,5898,6030,6135,6230,6360,6452,6558,6660,6772,6872,6989,7109,7231,7352,7466,7592,7702,7820,7926,8040,8127,8211,8327,8470,8645,15200"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-sk/values-sk.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14845", "endColumns": "93", "endOffsets": "14934"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2918,3014,3116,3217,3315,3425,3533,15854", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "3009,3111,3212,3310,3420,3528,3650,15950"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-sk/values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,285,395,510,608,703,815,950,1066,1218,1303,1404,1496,1593,1709,1831,1937,2070,2203,2337,2501,2629,2753,2883,3003,3096,3193,3314,3437,3535,3638,3747,3888,4037,4146,4246,4330,4424,4519,4635,4722,4809,4910,4990,5076,5173,5276,5369,5466,5554,5659,5756,5855,5975,6055,6157", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "165,280,390,505,603,698,810,945,1061,1213,1298,1399,1491,1588,1704,1826,1932,2065,2198,2332,2496,2624,2748,2878,2998,3091,3188,3309,3432,3530,3633,3742,3883,4032,4141,4241,4325,4419,4514,4630,4717,4804,4905,4985,5071,5168,5271,5364,5461,5549,5654,5751,5850,5970,6050,6152,6245"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8650,8765,8880,8990,9105,9203,9298,9410,9545,9661,9813,9898,9999,10091,10188,10304,10426,10532,10665,10798,10932,11096,11224,11348,11478,11598,11691,11788,11909,12032,12130,12233,12342,12483,12632,12741,12841,12925,13019,13114,13230,13317,13404,13505,13585,13671,13768,13871,13964,14061,14149,14254,14351,14450,14570,14650,14752", "endColumns": "114,114,109,114,97,94,111,134,115,151,84,100,91,96,115,121,105,132,132,133,163,127,123,129,119,92,96,120,122,97,102,108,140,148,108,99,83,93,94,115,86,86,100,79,85,96,102,92,96,87,104,96,98,119,79,101,92", "endOffsets": "8760,8875,8985,9100,9198,9293,9405,9540,9656,9808,9893,9994,10086,10183,10299,10421,10527,10660,10793,10927,11091,11219,11343,11473,11593,11686,11783,11904,12027,12125,12228,12337,12478,12627,12736,12836,12920,13014,13109,13225,13312,13399,13500,13580,13666,13763,13866,13959,14056,14144,14249,14346,14445,14565,14645,14747,14840"}}]}]}