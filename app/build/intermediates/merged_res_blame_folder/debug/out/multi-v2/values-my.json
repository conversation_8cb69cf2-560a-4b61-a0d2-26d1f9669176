{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-my/values-my.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,192,326,437,571,686,786,903,1052,1176,1339,1425,1524,1617,1719,1839,1966,2070,2196,2327,2471,2639,2761,2878,2997,3124,3218,3315,3446,3583,3685,3797,3902,4028,4157,4260,4363,4444,4542,4638,4746,4833,4919,5038,5118,5202,5302,5404,5500,5598,5685,5792,5891,5992,6113,6193,6316", "endColumns": "136,133,110,133,114,99,116,148,123,162,85,98,92,101,119,126,103,125,130,143,167,121,116,118,126,93,96,130,136,101,111,104,125,128,102,102,80,97,95,107,86,85,118,79,83,99,101,95,97,86,106,98,100,120,79,122,117", "endOffsets": "187,321,432,566,681,781,898,1047,1171,1334,1420,1519,1612,1714,1834,1961,2065,2191,2322,2466,2634,2756,2873,2992,3119,3213,3310,3441,3578,3680,3792,3897,4023,4152,4255,4358,4439,4537,4633,4741,4828,4914,5033,5113,5197,5297,5399,5495,5593,5680,5787,5886,5987,6108,6188,6311,6429"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8772,8909,9043,9154,9288,9403,9503,9620,9769,9893,10056,10142,10241,10334,10436,10556,10683,10787,10913,11044,11188,11356,11478,11595,11714,11841,11935,12032,12163,12300,12402,12514,12619,12745,12874,12977,13080,13161,13259,13355,13463,13550,13636,13755,13835,13919,14019,14121,14217,14315,14402,14509,14608,14709,14830,14910,15033", "endColumns": "136,133,110,133,114,99,116,148,123,162,85,98,92,101,119,126,103,125,130,143,167,121,116,118,126,93,96,130,136,101,111,104,125,128,102,102,80,97,95,107,86,85,118,79,83,99,101,95,97,86,106,98,100,120,79,122,117", "endOffsets": "8904,9038,9149,9283,9398,9498,9615,9764,9888,10051,10137,10236,10329,10431,10551,10678,10782,10908,11039,11183,11351,11473,11590,11709,11836,11930,12027,12158,12295,12397,12509,12614,12740,12869,12972,13075,13156,13254,13350,13458,13545,13631,13750,13830,13914,14014,14116,14212,14310,14397,14504,14603,14704,14825,14905,15028,15146"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-my/values-my.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15151", "endColumns": "92", "endOffsets": "15239"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-my/values-my.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,243", "endColumns": "78,108,107", "endOffsets": "129,238,346"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2895,16533,16642", "endColumns": "78,108,107", "endOffsets": "2969,16637,16745"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,511,641,753,853,945,1069,1193,1306,1437,1586,1716,1843,1962,2055,2194,2287,2398,2516,2627,2730,2850,2966,3084,3200,3315,3432,3557,3689,3807,3932,4019,4106,4207,4349,4505", "endColumns": "106,100,98,98,129,111,99,91,123,123,112,130,148,129,126,118,92,138,92,110,117,110,102,119,115,117,115,114,116,124,131,117,124,86,86,100,141,155,99", "endOffsets": "207,308,407,506,636,748,848,940,1064,1188,1301,1432,1581,1711,1838,1957,2050,2189,2282,2393,2511,2622,2725,2845,2961,3079,3195,3310,3427,3552,3684,3802,3927,4014,4101,4202,4344,4500,4600"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4372,4479,4580,4679,4778,4908,5020,5120,5212,5336,5460,5573,5704,5853,5983,6110,6229,6322,6461,6554,6665,6783,6894,6997,7117,7233,7351,7467,7582,7699,7824,7956,8074,8199,8286,8373,8474,8616,15415", "endColumns": "106,100,98,98,129,111,99,91,123,123,112,130,148,129,126,118,92,138,92,110,117,110,102,119,115,117,115,114,116,124,131,117,124,86,86,100,141,155,99", "endOffsets": "4474,4575,4674,4773,4903,5015,5115,5207,5331,5455,5568,5699,5848,5978,6105,6224,6317,6456,6549,6660,6778,6889,6992,7112,7228,7346,7462,7577,7694,7819,7951,8069,8194,8281,8368,8469,8611,8767,15510"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,319,436,529,641,769,847,926,1017,1110,1206,1300,1401,1494,1589,1683,1774,1865,1951,2074,2186,2288,2414,2525,2635,2795,2895", "endColumns": "108,104,116,92,111,127,77,78,90,92,95,93,100,92,94,93,90,90,85,122,111,101,125,110,109,159,99,83", "endOffsets": "209,314,431,524,636,764,842,921,1012,1105,1201,1295,1396,1489,1584,1678,1769,1860,1946,2069,2181,2283,2409,2520,2630,2790,2890,2974"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,319,436,529,641,769,847,926,1017,1110,1206,1300,1401,1494,1589,1683,1774,1865,1951,2074,2186,2288,2414,2525,2635,2795,15688", "endColumns": "108,104,116,92,111,127,77,78,90,92,95,93,100,92,94,93,90,90,85,122,111,101,125,110,109,159,99,83", "endOffsets": "209,314,431,524,636,764,842,921,1012,1105,1201,1295,1396,1489,1584,1678,1769,1860,1946,2069,2181,2283,2409,2520,2630,2790,2890,15767"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2974,3077,3181,3284,3386,3491,3597,16159", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3072,3176,3279,3381,3486,3592,3711,16255"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-my/values-my.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,279,367,471,575,658,742,841,930,1012,1099,1185,1260,1349,1426,1499,1572,1653,1719", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,88,76,72,72,80,65,125", "endOffsets": "274,362,466,570,653,737,836,925,1007,1094,1180,1255,1344,1421,1494,1567,1648,1714,1840"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3716,3810,3898,4002,4106,4189,4273,15244,15333,15515,15602,15772,15847,15936,16013,16086,16260,16341,16407", "endColumns": "93,87,103,103,82,83,98,88,81,86,85,74,88,76,72,72,80,65,125", "endOffsets": "3805,3893,3997,4101,4184,4268,4367,15328,15410,15597,15683,15842,15931,16008,16081,16154,16336,16402,16528"}}]}]}