{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-tl/values-tl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-tl/values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,301,419,540,639,739,856,1003,1130,1280,1365,1464,1559,1657,1778,1916,2020,2167,2315,2462,2632,2770,2893,3018,3143,3239,3338,3463,3598,3705,3809,3922,4067,4216,4332,4438,4514,4614,4711,4821,4910,4999,5106,5186,5270,5370,5474,5574,5680,5768,5880,5985,6095,6214,6294,6401", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,109,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "174,296,414,535,634,734,851,998,1125,1275,1360,1459,1554,1652,1773,1911,2015,2162,2310,2457,2627,2765,2888,3013,3138,3234,3333,3458,3593,3700,3804,3917,4062,4211,4327,4433,4509,4609,4706,4816,4905,4994,5101,5181,5265,5365,5469,5569,5675,5763,5875,5980,6090,6209,6289,6396,6491"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8710,8834,8956,9074,9195,9294,9394,9511,9658,9785,9935,10020,10119,10214,10312,10433,10571,10675,10822,10970,11117,11287,11425,11548,11673,11798,11894,11993,12118,12253,12360,12464,12577,12722,12871,12987,13093,13169,13269,13366,13476,13565,13654,13761,13841,13925,14025,14129,14229,14335,14423,14535,14640,14750,14869,14949,15056", "endColumns": "123,121,117,120,98,99,116,146,126,149,84,98,94,97,120,137,103,146,147,146,169,137,122,124,124,95,98,124,134,106,103,112,144,148,115,105,75,99,96,109,88,88,106,79,83,99,103,99,105,87,111,104,109,118,79,106,94", "endOffsets": "8829,8951,9069,9190,9289,9389,9506,9653,9780,9930,10015,10114,10209,10307,10428,10566,10670,10817,10965,11112,11282,11420,11543,11668,11793,11889,11988,12113,12248,12355,12459,12572,12717,12866,12982,13088,13164,13264,13361,13471,13560,13649,13756,13836,13920,14020,14124,14224,14330,14418,14530,14635,14745,14864,14944,15051,15146"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-tl/values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15151", "endColumns": "88", "endOffsets": "15235"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-tl/values-tl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,216", "endColumns": "74,85,91", "endOffsets": "125,211,303"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2865,16535,16621", "endColumns": "74,85,91", "endOffsets": "2935,16616,16708"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-tl/values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,332,449,537,643,764,843,921,1012,1105,1201,1295,1396,1489,1584,1678,1769,1860,1944,2053,2164,2265,2375,2492,2600,2763,2865", "endColumns": "118,107,116,87,105,120,78,77,90,92,95,93,100,92,94,93,90,90,83,108,110,100,109,116,107,162,101,83", "endOffsets": "219,327,444,532,638,759,838,916,1007,1100,1196,1290,1391,1484,1579,1673,1764,1855,1939,2048,2159,2260,2370,2487,2595,2758,2860,2944"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,332,449,537,643,764,843,921,1012,1105,1201,1295,1396,1489,1584,1678,1769,1860,1944,2053,2164,2265,2375,2492,2600,2763,15688", "endColumns": "118,107,116,87,105,120,78,77,90,92,95,93,100,92,94,93,90,90,83,108,110,100,109,116,107,162,101,83", "endOffsets": "219,327,444,532,638,759,838,916,1007,1100,1196,1290,1391,1484,1579,1673,1764,1855,1939,2048,2159,2260,2370,2487,2595,2758,2860,15767"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-tl/values-tl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,280,366,463,565,655,737,829,921,1005,1092,1178,1249,1334,1417,1494,1569,1647,1713", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,84,82,76,74,77,65,126", "endOffsets": "275,361,458,560,650,732,824,916,1000,1087,1173,1244,1329,1412,1489,1564,1642,1708,1835"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3674,3773,3859,3956,4058,4148,4230,15240,15332,15515,15602,15772,15843,15928,16011,16088,16264,16342,16408", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,84,82,76,74,77,65,126", "endOffsets": "3768,3854,3951,4053,4143,4225,4317,15327,15411,15597,15683,15838,15923,16006,16083,16158,16337,16403,16530"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-tl/values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2940,3037,3139,3240,3337,3444,3552,16163", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3032,3134,3235,3332,3439,3547,3669,16259"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-tl/values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,507,645,762,861,955,1092,1227,1333,1462,1616,1748,1878,1995,2087,2220,2311,2414,2520,2624,2719,2836,2958,3079,3198,3308,3423,3550,3670,3793,3909,3996,4082,4191,4331,4493", "endColumns": "106,100,98,94,137,116,98,93,136,134,105,128,153,131,129,116,91,132,90,102,105,103,94,116,121,120,118,109,114,126,119,122,115,86,85,108,139,161,98", "endOffsets": "207,308,407,502,640,757,856,950,1087,1222,1328,1457,1611,1743,1873,1990,2082,2215,2306,2409,2515,2619,2714,2831,2953,3074,3193,3303,3418,3545,3665,3788,3904,3991,4077,4186,4326,4488,4587"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4322,4429,4530,4629,4724,4862,4979,5078,5172,5309,5444,5550,5679,5833,5965,6095,6212,6304,6437,6528,6631,6737,6841,6936,7053,7175,7296,7415,7525,7640,7767,7887,8010,8126,8213,8299,8408,8548,15416", "endColumns": "106,100,98,94,137,116,98,93,136,134,105,128,153,131,129,116,91,132,90,102,105,103,94,116,121,120,118,109,114,126,119,122,115,86,85,108,139,161,98", "endOffsets": "4424,4525,4624,4719,4857,4974,5073,5167,5304,5439,5545,5674,5828,5960,6090,6207,6299,6432,6523,6626,6732,6836,6931,7048,7170,7291,7410,7520,7635,7762,7882,8005,8121,8208,8294,8403,8543,8705,15510"}}]}]}