{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-lv/values-lv.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-lv/values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,505,626,742,840,935,1059,1182,1291,1418,1594,1742,1889,1996,2087,2218,2311,2415,2525,2629,2721,2838,2956,3086,3215,3319,3432,3546,3662,3772,3884,3971,4055,4158,4297,4454", "endColumns": "106,100,96,94,120,115,97,94,123,122,108,126,175,147,146,106,90,130,92,103,109,103,91,116,117,129,128,103,112,113,115,109,111,86,83,102,138,156,94", "endOffsets": "207,308,405,500,621,737,835,930,1054,1177,1286,1413,1589,1737,1884,1991,2082,2213,2306,2410,2520,2624,2716,2833,2951,3081,3210,3314,3427,3541,3657,3767,3879,3966,4050,4153,4292,4449,4544"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4462,4569,4670,4767,4862,4983,5099,5197,5292,5416,5539,5648,5775,5951,6099,6246,6353,6444,6575,6668,6772,6882,6986,7078,7195,7313,7443,7572,7676,7789,7903,8019,8129,8241,8328,8412,8515,8654,15441", "endColumns": "106,100,96,94,120,115,97,94,123,122,108,126,175,147,146,106,90,130,92,103,109,103,91,116,117,129,128,103,112,113,115,109,111,86,83,102,138,156,94", "endOffsets": "4564,4665,4762,4857,4978,5094,5192,5287,5411,5534,5643,5770,5946,6094,6241,6348,6439,6570,6663,6767,6877,6981,7073,7190,7308,7438,7567,7671,7784,7898,8014,8124,8236,8323,8407,8510,8649,8806,15531"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-lv/values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,333,442,528,632,754,836,918,1028,1136,1243,1352,1464,1567,1679,1786,1891,1991,2076,2185,2297,2396,2507,2616,2721,2895,2994", "endColumns": "119,107,108,85,103,121,81,81,109,107,106,108,111,102,111,106,104,99,84,108,111,98,110,108,104,173,98,81", "endOffsets": "220,328,437,523,627,749,831,913,1023,1131,1238,1347,1459,1562,1674,1781,1886,1986,2071,2180,2292,2391,2502,2611,2716,2890,2989,3071"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,333,442,528,632,754,836,918,1028,1136,1243,1352,1464,1567,1679,1786,1891,1991,2076,2185,2297,2396,2507,2616,2721,2895,15708", "endColumns": "119,107,108,85,103,121,81,81,109,107,106,108,111,102,111,106,104,99,84,108,111,98,110,108,104,173,98,81", "endOffsets": "220,328,437,523,627,749,831,913,1023,1131,1238,1347,1459,1562,1674,1781,1886,1986,2071,2180,2292,2391,2502,2611,2716,2890,2989,15785"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-lv/values-lv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,232", "endColumns": "86,89,89", "endOffsets": "137,227,317"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2994,16551,16641", "endColumns": "86,89,89", "endOffsets": "3076,16636,16726"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-lv/values-lv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15170", "endColumns": "93", "endOffsets": "15259"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-lv/values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,309,424,549,658,758,875,1013,1131,1278,1364,1462,1556,1657,1776,1900,2003,2141,2272,2410,2593,2725,2844,2971,3091,3186,3285,3406,3541,3643,3757,3863,3998,4143,4252,4355,4438,4533,4627,4737,4827,4914,5025,5105,5191,5286,5390,5481,5579,5668,5775,5877,5977,6130,6210,6315", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,109,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "177,304,419,544,653,753,870,1008,1126,1273,1359,1457,1551,1652,1771,1895,1998,2136,2267,2405,2588,2720,2839,2966,3086,3181,3280,3401,3536,3638,3752,3858,3993,4138,4247,4350,4433,4528,4622,4732,4822,4909,5020,5100,5186,5281,5385,5476,5574,5663,5770,5872,5972,6125,6205,6310,6409"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8811,8938,9065,9180,9305,9414,9514,9631,9769,9887,10034,10120,10218,10312,10413,10532,10656,10759,10897,11028,11166,11349,11481,11600,11727,11847,11942,12041,12162,12297,12399,12513,12619,12754,12899,13008,13111,13194,13289,13383,13493,13583,13670,13781,13861,13947,14042,14146,14237,14335,14424,14531,14633,14733,14886,14966,15071", "endColumns": "126,126,114,124,108,99,116,137,117,146,85,97,93,100,118,123,102,137,130,137,182,131,118,126,119,94,98,120,134,101,113,105,134,144,108,102,82,94,93,109,89,86,110,79,85,94,103,90,97,88,106,101,99,152,79,104,98", "endOffsets": "8933,9060,9175,9300,9409,9509,9626,9764,9882,10029,10115,10213,10307,10408,10527,10651,10754,10892,11023,11161,11344,11476,11595,11722,11842,11937,12036,12157,12292,12394,12508,12614,12749,12894,13003,13106,13189,13284,13378,13488,13578,13665,13776,13856,13942,14037,14141,14232,14330,14419,14526,14628,14728,14881,14961,15066,15165"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-lv/values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3081,3179,3281,3381,3482,3589,3697,16182", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3174,3276,3376,3477,3584,3692,3807,16278"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-lv/values-lv.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,291,380,476,579,669,755,843,936,1020,1105,1192,1265,1355,1431,1508,1584,1662,1730", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,89,75,76,75,77,67,121", "endOffsets": "286,375,471,574,664,750,838,931,1015,1100,1187,1260,1350,1426,1503,1579,1657,1725,1847"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3812,3910,3999,4095,4198,4288,4374,15264,15357,15536,15621,15790,15863,15953,16029,16106,16283,16361,16429", "endColumns": "97,88,95,102,89,85,87,92,83,84,86,72,89,75,76,75,77,67,121", "endOffsets": "3905,3994,4090,4193,4283,4369,4457,15352,15436,15616,15703,15858,15948,16024,16101,16177,16356,16424,16546"}}]}]}