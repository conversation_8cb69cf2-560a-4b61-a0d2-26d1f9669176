{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-tr/values-tr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,407,498,617,728,827,917,1051,1179,1280,1399,1552,1695,1832,1939,2032,2164,2254,2360,2472,2577,2672,2784,2900,3025,3144,3248,3355,3471,3585,3697,3807,3894,3983,4093,4235,4398", "endColumns": "106,100,93,90,118,110,98,89,133,127,100,118,152,142,136,106,92,131,89,105,111,104,94,111,115,124,118,103,106,115,113,111,109,86,88,109,141,162,88", "endOffsets": "207,308,402,493,612,723,822,912,1046,1174,1275,1394,1547,1690,1827,1934,2027,2159,2249,2355,2467,2572,2667,2779,2895,3020,3139,3243,3350,3466,3580,3692,3802,3889,3978,4088,4230,4393,4482"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4222,4329,4430,4524,4615,4734,4845,4944,5034,5168,5296,5397,5516,5669,5812,5949,6056,6149,6281,6371,6477,6589,6694,6789,6901,7017,7142,7261,7365,7472,7588,7702,7814,7924,8011,8100,8210,8352,14935", "endColumns": "106,100,93,90,118,110,98,89,133,127,100,118,152,142,136,106,92,131,89,105,111,104,94,111,115,124,118,103,106,115,113,111,109,86,88,109,141,162,88", "endOffsets": "4324,4425,4519,4610,4729,4840,4939,5029,5163,5291,5392,5511,5664,5807,5944,6051,6144,6276,6366,6472,6584,6689,6784,6896,7012,7137,7256,7360,7467,7583,7697,7809,7919,8006,8095,8205,8347,8510,15019"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,309,421,506,612,732,811,887,978,1071,1164,1258,1356,1449,1551,1646,1737,1828,1907,2014,2119,2215,2322,2424,2532,2688,2786", "endColumns": "104,98,111,84,105,119,78,75,90,92,92,93,97,92,101,94,90,90,78,106,104,95,106,101,107,155,97,78", "endOffsets": "205,304,416,501,607,727,806,882,973,1066,1159,1253,1351,1444,1546,1641,1732,1823,1902,2009,2114,2210,2317,2419,2527,2683,2781,2860"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,309,421,506,612,732,811,887,978,1071,1164,1258,1356,1449,1551,1646,1737,1828,1907,2014,2119,2215,2322,2424,2532,2688,15192", "endColumns": "104,98,111,84,105,119,78,75,90,92,92,93,97,92,101,94,90,90,78,106,104,95,106,101,107,155,97,78", "endOffsets": "205,304,416,501,607,727,806,882,973,1066,1159,1253,1351,1444,1546,1641,1732,1823,1902,2009,2114,2210,2317,2419,2527,2683,2781,15266"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-tr/values-tr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14675", "endColumns": "87", "endOffsets": "14758"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,139,223", "endColumns": "83,83,86", "endOffsets": "134,218,305"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2786,16012,16096", "endColumns": "83,83,86", "endOffsets": "2865,16091,16178"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-tr/values-tr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "190,283,367,462,562,646,729,829,917,1001,1081,1169,1240,1324,1398,1473,1545,1623,1691", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,83,73,74,71,77,67,117", "endOffsets": "278,362,457,557,641,724,824,912,996,1076,1164,1235,1319,1393,1468,1540,1618,1686,1804"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3583,3676,3760,3855,3955,4039,4122,14763,14851,15024,15104,15271,15342,15426,15500,15575,15748,15826,15894", "endColumns": "92,83,94,99,83,82,99,87,83,79,87,70,83,73,74,71,77,67,117", "endOffsets": "3671,3755,3850,3950,4034,4117,4217,14846,14930,15099,15187,15337,15421,15495,15570,15642,15821,15889,16007"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2870,2967,3069,3167,3264,3366,3472,15647", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "2962,3064,3162,3259,3361,3467,3578,15743"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-tr/values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,401,516,612,708,821,954,1076,1216,1301,1399,1488,1585,1700,1821,1924,2061,2197,2319,2490,2608,2724,2842,2957,3047,3145,3269,3398,3499,3601,3707,3843,3983,4095,4197,4273,4370,4468,4578,4664,4749,4866,4946,5030,5130,5230,5326,5421,5509,5615,5715,5814,5935,6015,6122", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "165,278,396,511,607,703,816,949,1071,1211,1296,1394,1483,1580,1695,1816,1919,2056,2192,2314,2485,2603,2719,2837,2952,3042,3140,3264,3393,3494,3596,3702,3838,3978,4090,4192,4268,4365,4463,4573,4659,4744,4861,4941,5025,5125,5225,5321,5416,5504,5610,5710,5809,5930,6010,6117,6210"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8515,8630,8743,8861,8976,9072,9168,9281,9414,9536,9676,9761,9859,9948,10045,10160,10281,10384,10521,10657,10779,10950,11068,11184,11302,11417,11507,11605,11729,11858,11959,12061,12167,12303,12443,12555,12657,12733,12830,12928,13038,13124,13209,13326,13406,13490,13590,13690,13786,13881,13969,14075,14175,14274,14395,14475,14582", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,109,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "8625,8738,8856,8971,9067,9163,9276,9409,9531,9671,9756,9854,9943,10040,10155,10276,10379,10516,10652,10774,10945,11063,11179,11297,11412,11502,11600,11724,11853,11954,12056,12162,12298,12438,12550,12652,12728,12825,12923,13033,13119,13204,13321,13401,13485,13585,13685,13781,13876,13964,14070,14170,14269,14390,14470,14577,14670"}}]}]}