{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-sv/values-sv.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-sv/values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,314,425,510,612,725,801,877,970,1065,1161,1255,1358,1453,1550,1648,1744,1837,1917,2023,2123,2219,2324,2426,2528,2682,2784", "endColumns": "105,102,110,84,101,112,75,75,92,94,95,93,102,94,96,97,95,92,79,105,99,95,104,101,101,153,101,78", "endOffsets": "206,309,420,505,607,720,796,872,965,1060,1156,1250,1353,1448,1545,1643,1739,1832,1912,2018,2118,2214,2319,2421,2523,2677,2779,2858"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,314,425,510,612,725,801,877,970,1065,1161,1255,1358,1453,1550,1648,1744,1837,1917,2023,2123,2219,2324,2426,2528,2682,15211", "endColumns": "105,102,110,84,101,112,75,75,92,94,95,93,102,94,96,97,95,92,79,105,99,95,104,101,101,153,101,78", "endOffsets": "206,309,420,505,607,720,796,872,965,1060,1156,1250,1353,1448,1545,1643,1739,1832,1912,2018,2118,2214,2319,2421,2523,2677,2779,15285"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-sv/values-sv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,220", "endColumns": "74,89,88", "endOffsets": "125,215,304"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2784,16026,16116", "endColumns": "74,89,88", "endOffsets": "2854,16111,16200"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-sv/values-sv.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,274,362,458,557,645,721,809,898,979,1065,1155,1224,1310,1384,1455,1525,1603,1670", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,85,73,70,69,77,66,119", "endOffsets": "269,357,453,552,640,716,804,893,974,1060,1150,1219,1305,1379,1450,1520,1598,1665,1785"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3587,3680,3768,3864,3963,4051,4127,14779,14868,15035,15121,15290,15359,15445,15519,15590,15761,15839,15906", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,85,73,70,69,77,66,119", "endOffsets": "3675,3763,3859,3958,4046,4122,4210,14863,14944,15116,15206,15354,15440,15514,15585,15655,15834,15901,16021"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-sv/values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,505,625,737,834,928,1053,1175,1284,1406,1557,1681,1802,1908,1998,2123,2217,2320,2424,2524,2625,2743,2852,2964,3070,3176,3291,3402,3519,3628,3743,3830,3911,4018,4151,4305", "endColumns": "106,100,96,94,119,111,96,93,124,121,108,121,150,123,120,105,89,124,93,102,103,99,100,117,108,111,105,105,114,110,116,108,114,86,80,106,132,153,85", "endOffsets": "207,308,405,500,620,732,829,923,1048,1170,1279,1401,1552,1676,1797,1903,1993,2118,2212,2315,2419,2519,2620,2738,2847,2959,3065,3171,3286,3397,3514,3623,3738,3825,3906,4013,4146,4300,4386"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4215,4322,4423,4520,4615,4735,4847,4944,5038,5163,5285,5394,5516,5667,5791,5912,6018,6108,6233,6327,6430,6534,6634,6735,6853,6962,7074,7180,7286,7401,7512,7629,7738,7853,7940,8021,8128,8261,14949", "endColumns": "106,100,96,94,119,111,96,93,124,121,108,121,150,123,120,105,89,124,93,102,103,99,100,117,108,111,105,105,114,110,116,108,114,86,80,106,132,153,85", "endOffsets": "4317,4418,4515,4610,4730,4842,4939,5033,5158,5280,5389,5511,5662,5786,5907,6013,6103,6228,6322,6425,6529,6629,6730,6848,6957,7069,7175,7281,7396,7507,7624,7733,7848,7935,8016,8123,8256,8410,15030"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-sv/values-sv.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14689", "endColumns": "89", "endOffsets": "14774"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-sv/values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,327,434,566,682,778,891,1035,1159,1314,1399,1498,1588,1682,1796,1918,2022,2155,2282,2417,2589,2717,2835,2961,3081,3172,3270,3388,3527,3623,3731,3834,3967,4110,4216,4313,4393,4491,4583,4699,4783,4868,4969,5049,5134,5233,5333,5428,5528,5615,5719,5820,5924,6046,6126,6230", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "189,322,429,561,677,773,886,1030,1154,1309,1394,1493,1583,1677,1791,1913,2017,2150,2277,2412,2584,2712,2830,2956,3076,3167,3265,3383,3522,3618,3726,3829,3962,4105,4211,4308,4388,4486,4578,4694,4778,4863,4964,5044,5129,5228,5328,5423,5523,5610,5714,5815,5919,6041,6121,6225,6324"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8415,8554,8687,8794,8926,9042,9138,9251,9395,9519,9674,9759,9858,9948,10042,10156,10278,10382,10515,10642,10777,10949,11077,11195,11321,11441,11532,11630,11748,11887,11983,12091,12194,12327,12470,12576,12673,12753,12851,12943,13059,13143,13228,13329,13409,13494,13593,13693,13788,13888,13975,14079,14180,14284,14406,14486,14590", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "8549,8682,8789,8921,9037,9133,9246,9390,9514,9669,9754,9853,9943,10037,10151,10273,10377,10510,10637,10772,10944,11072,11190,11316,11436,11527,11625,11743,11882,11978,12086,12189,12322,12465,12571,12668,12748,12846,12938,13054,13138,13223,13324,13404,13489,13588,13688,13783,13883,13970,14074,14175,14279,14401,14481,14585,14684"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-sv/values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2859,2954,3056,3154,3253,3361,3466,15660", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "2949,3051,3149,3248,3356,3461,3582,15756"}}]}]}