{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-hy/values-hy.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,309,419,508,614,729,811,892,983,1076,1172,1266,1367,1460,1555,1649,1740,1831,1916,2023,2130,2229,2339,2446,2546,2703,2802", "endColumns": "102,100,109,88,105,114,81,80,90,92,95,93,100,92,94,93,90,90,84,106,106,98,109,106,99,156,98,81", "endOffsets": "203,304,414,503,609,724,806,887,978,1071,1167,1261,1362,1455,1550,1644,1735,1826,1911,2018,2125,2224,2334,2441,2541,2698,2797,2879"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,309,419,508,614,729,811,892,983,1076,1172,1266,1367,1460,1555,1649,1740,1831,1916,2023,2130,2229,2339,2446,2546,2703,15304", "endColumns": "102,100,109,88,105,114,81,80,90,92,95,93,100,92,94,93,90,90,84,106,106,98,109,106,99,156,98,81", "endOffsets": "203,304,414,503,609,724,806,887,978,1071,1167,1261,1362,1455,1550,1644,1735,1826,1911,2018,2125,2224,2334,2441,2541,2698,2797,15381"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-hy/values-hy.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,284,366,460,560,643,725,811,906,988,1073,1161,1235,1323,1400,1479,1556,1637,1706", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,87,76,78,76,80,68,117", "endOffsets": "279,361,455,555,638,720,806,901,983,1068,1156,1230,1318,1395,1474,1551,1632,1701,1819"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3601,3700,3782,3876,3976,4059,4141,14870,14965,15131,15216,15386,15460,15548,15625,15704,15882,15963,16032", "endColumns": "98,81,93,99,82,81,85,94,81,84,87,73,87,76,78,76,80,68,117", "endOffsets": "3695,3777,3871,3971,4054,4136,4222,14960,15042,15211,15299,15455,15543,15620,15699,15776,15958,16027,16145"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,219", "endColumns": "78,84,88", "endOffsets": "129,214,303"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2802,16150,16235", "endColumns": "78,84,88", "endOffsets": "2876,16230,16319"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,507,636,754,853,943,1066,1188,1289,1409,1568,1688,1807,1920,2013,2143,2237,2341,2440,2541,2636,2749,2875,2995,3114,3219,3328,3444,3564,3677,3794,3881,3966,4072,4207,4364", "endColumns": "106,100,98,94,128,117,98,89,122,121,100,119,158,119,118,112,92,129,93,103,98,100,94,112,125,119,118,104,108,115,119,112,116,86,84,105,134,156,83", "endOffsets": "207,308,407,502,631,749,848,938,1061,1183,1284,1404,1563,1683,1802,1915,2008,2138,2232,2336,2435,2536,2631,2744,2870,2990,3109,3214,3323,3439,3559,3672,3789,3876,3961,4067,4202,4359,4443"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4227,4334,4435,4534,4629,4758,4876,4975,5065,5188,5310,5411,5531,5690,5810,5929,6042,6135,6265,6359,6463,6562,6663,6758,6871,6997,7117,7236,7341,7450,7566,7686,7799,7916,8003,8088,8194,8329,15047", "endColumns": "106,100,98,94,128,117,98,89,122,121,100,119,158,119,118,112,92,129,93,103,98,100,94,112,125,119,118,104,108,115,119,112,116,86,84,105,134,156,83", "endOffsets": "4329,4430,4529,4624,4753,4871,4970,5060,5183,5305,5406,5526,5685,5805,5924,6037,6130,6260,6354,6458,6557,6658,6753,6866,6992,7112,7231,7336,7445,7561,7681,7794,7911,7998,8083,8189,8324,8481,15126"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2881,2981,3086,3184,3283,3388,3490,15781", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "2976,3081,3179,3278,3383,3485,3596,15877"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-hy/values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,401,517,616,718,837,983,1106,1262,1349,1447,1542,1641,1763,1885,1988,2128,2266,2399,2576,2705,2821,2940,3063,3159,3257,3380,3521,3627,3732,3840,3979,4123,4232,4334,4425,4520,4616,4723,4811,4896,5010,5090,5173,5272,5373,5464,5560,5649,5753,5851,5951,6068,6148,6253", "endColumns": "115,114,114,115,98,101,118,145,122,155,86,97,94,98,121,121,102,139,137,132,176,128,115,118,122,95,97,122,140,105,104,107,138,143,108,101,90,94,95,106,87,84,113,79,82,98,100,90,95,88,103,97,99,116,79,104,93", "endOffsets": "166,281,396,512,611,713,832,978,1101,1257,1344,1442,1537,1636,1758,1880,1983,2123,2261,2394,2571,2700,2816,2935,3058,3154,3252,3375,3516,3622,3727,3835,3974,4118,4227,4329,4420,4515,4611,4718,4806,4891,5005,5085,5168,5267,5368,5459,5555,5644,5748,5846,5946,6063,6143,6248,6342"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8486,8602,8717,8832,8948,9047,9149,9268,9414,9537,9693,9780,9878,9973,10072,10194,10316,10419,10559,10697,10830,11007,11136,11252,11371,11494,11590,11688,11811,11952,12058,12163,12271,12410,12554,12663,12765,12856,12951,13047,13154,13242,13327,13441,13521,13604,13703,13804,13895,13991,14080,14184,14282,14382,14499,14579,14684", "endColumns": "115,114,114,115,98,101,118,145,122,155,86,97,94,98,121,121,102,139,137,132,176,128,115,118,122,95,97,122,140,105,104,107,138,143,108,101,90,94,95,106,87,84,113,79,82,98,100,90,95,88,103,97,99,116,79,104,93", "endOffsets": "8597,8712,8827,8943,9042,9144,9263,9409,9532,9688,9775,9873,9968,10067,10189,10311,10414,10554,10692,10825,11002,11131,11247,11366,11489,11585,11683,11806,11947,12053,12158,12266,12405,12549,12658,12760,12851,12946,13042,13149,13237,13322,13436,13516,13599,13698,13799,13890,13986,14075,14179,14277,14377,14494,14574,14679,14773"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-hy/values-hy.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14778", "endColumns": "91", "endOffsets": "14865"}}]}]}