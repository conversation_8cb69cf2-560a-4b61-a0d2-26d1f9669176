{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/9ff06e8333e2cde809d82d2ca13a1ebd/transformed/material-release/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14989", "endColumns": "88", "endOffsets": "15073"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a737f244602a4a8d0f27fa3e00ab447e/transformed/appcompat-1.0.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,752,836,917,1009,1103,1201,1295,1395,1489,1585,1680,1772,1864,1951,2058,2170,2272,2380,2487,2594,2765,2864", "endColumns": "119,105,106,88,100,123,83,80,91,93,97,93,99,93,95,94,91,91,86,106,111,101,107,106,106,170,98,84", "endOffsets": "220,326,433,522,623,747,831,912,1004,1098,1196,1290,1390,1484,1580,1675,1767,1859,1946,2053,2165,2267,2375,2482,2589,2760,2859,2944"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,752,836,917,1009,1103,1201,1295,1395,1489,1585,1680,1772,1864,1951,2058,2170,2272,2380,2487,2594,2765,15524", "endColumns": "119,105,106,88,100,123,83,80,91,93,97,93,99,93,95,94,91,91,86,106,111,101,107,106,106,170,98,84", "endOffsets": "220,326,433,522,623,747,831,912,1004,1098,1196,1290,1390,1484,1580,1675,1767,1859,1946,2053,2165,2267,2375,2482,2589,2760,2859,15604"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4f48fe7bc18bc9eb12fcd8d5ad33334c/transformed/ui-release/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "198,293,376,473,572,658,737,834,925,1012,1097,1187,1263,1348,1424,1503,1578,1654,1726", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,84,75,78,74,75,71,121", "endOffsets": "288,371,468,567,653,732,829,920,1007,1092,1182,1258,1343,1419,1498,1573,1649,1721,1843"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3688,3783,3866,3963,4062,4148,4227,15078,15169,15349,15434,15609,15685,15770,15846,15925,16101,16177,16249", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,84,75,78,74,75,71,121", "endOffsets": "3778,3861,3958,4057,4143,4222,4319,15164,15251,15429,15519,15680,15765,15841,15920,15995,16172,16244,16366"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/722a175955568d51d74d245de5f8553e/transformed/material3-release/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,296,419,539,641,740,856,997,1115,1260,1344,1446,1544,1644,1759,1886,1993,2138,2282,2428,2620,2758,2879,3003,3129,3228,3325,3450,3588,3692,3805,3910,4056,4207,4317,4422,4508,4603,4698,4812,4902,4989,5090,5170,5254,5355,5460,5553,5653,5741,5851,5952,6057,6176,6256,6360", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,113,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "171,291,414,534,636,735,851,992,1110,1255,1339,1441,1539,1639,1754,1881,1988,2133,2277,2423,2615,2753,2874,2998,3124,3223,3320,3445,3583,3687,3800,3905,4051,4202,4312,4417,4503,4598,4693,4807,4897,4984,5085,5165,5249,5350,5455,5548,5648,5736,5846,5947,6052,6171,6251,6355,6451"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8588,8709,8829,8952,9072,9174,9273,9389,9530,9648,9793,9877,9979,10077,10177,10292,10419,10526,10671,10815,10961,11153,11291,11412,11536,11662,11761,11858,11983,12121,12225,12338,12443,12589,12740,12850,12955,13041,13136,13231,13345,13435,13522,13623,13703,13787,13888,13993,14086,14186,14274,14384,14485,14590,14709,14789,14893", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,113,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "8704,8824,8947,9067,9169,9268,9384,9525,9643,9788,9872,9974,10072,10172,10287,10414,10521,10666,10810,10956,11148,11286,11407,11531,11657,11756,11853,11978,12116,12220,12333,12438,12584,12735,12845,12950,13036,13131,13226,13340,13430,13517,13618,13698,13782,13883,13988,14081,14181,14269,14379,14480,14585,14704,14784,14888,14984"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/a533a7154ce730f894113908e7076680/transformed/core-1.16.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2956,3053,3155,3254,3354,3461,3567,16000", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3048,3150,3249,3349,3456,3562,3683,16096"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b79e35403c81ffb8d75ef6dd71f02173/transformed/leanback-1.2.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,507,633,747,847,950,1074,1194,1293,1410,1572,1697,1818,1921,2017,2146,2241,2344,2447,2547,2640,2750,2872,2998,3120,3233,3350,3460,3579,3683,3796,3883,3970,4075,4213,4369", "endColumns": "106,100,97,95,125,113,99,102,123,119,98,116,161,124,120,102,95,128,94,102,102,99,92,109,121,125,121,112,116,109,118,103,112,86,86,104,137,155,92", "endOffsets": "207,308,406,502,628,742,842,945,1069,1189,1288,1405,1567,1692,1813,1916,2012,2141,2236,2339,2442,2542,2635,2745,2867,2993,3115,3228,3345,3455,3574,3678,3791,3878,3965,4070,4208,4364,4457"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4324,4431,4532,4630,4726,4852,4966,5066,5169,5293,5413,5512,5629,5791,5916,6037,6140,6236,6365,6460,6563,6666,6766,6859,6969,7091,7217,7339,7452,7569,7679,7798,7902,8015,8102,8189,8294,8432,15256", "endColumns": "106,100,97,95,125,113,99,102,123,119,98,116,161,124,120,102,95,128,94,102,102,99,92,109,121,125,121,112,116,109,118,103,112,86,86,104,137,155,92", "endOffsets": "4426,4527,4625,4721,4847,4961,5061,5164,5288,5408,5507,5624,5786,5911,6032,6135,6231,6360,6455,6558,6661,6761,6854,6964,7086,7212,7334,7447,7564,7674,7793,7897,8010,8097,8184,8289,8427,8583,15344"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/c26acf0d66bf9607e2b4b0334ea982d7/transformed/foundation-release/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,147,234", "endColumns": "91,86,88", "endOffsets": "142,229,318"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2864,16371,16458", "endColumns": "91,86,88", "endOffsets": "2951,16453,16542"}}]}]}