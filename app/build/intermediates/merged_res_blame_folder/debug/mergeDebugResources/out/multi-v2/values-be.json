{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-be/values-be.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,509,637,748,845,936,1060,1182,1290,1416,1575,1700,1823,1929,2019,2157,2251,2355,2466,2579,2676,2795,2920,3038,3154,3275,3401,3523,3657,3774,3903,3990,4073,4181,4321,4487", "endColumns": "106,100,98,96,127,110,96,90,123,121,107,125,158,124,122,105,89,137,93,103,110,112,96,118,124,117,115,120,125,121,133,116,128,86,82,107,139,165,81", "endOffsets": "207,308,407,504,632,743,840,931,1055,1177,1285,1411,1570,1695,1818,1924,2014,2152,2246,2350,2461,2574,2671,2790,2915,3033,3149,3270,3396,3518,3652,3769,3898,3985,4068,4176,4316,4482,4564"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4275,4382,4483,4582,4679,4807,4918,5015,5106,5230,5352,5460,5586,5745,5870,5993,6099,6189,6327,6421,6525,6636,6749,6846,6965,7090,7208,7324,7445,7571,7693,7827,7944,8073,8160,8243,8351,8491,15297", "endColumns": "106,100,98,96,127,110,96,90,123,121,107,125,158,124,122,105,89,137,93,103,110,112,96,118,124,117,115,120,125,121,133,116,128,86,82,107,139,165,81", "endOffsets": "4377,4478,4577,4674,4802,4913,5010,5101,5225,5347,5455,5581,5740,5865,5988,6094,6184,6322,6416,6520,6631,6744,6841,6960,7085,7203,7319,7440,7566,7688,7822,7939,8068,8155,8238,8346,8486,8652,15374"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,406,523,621,718,832,955,1070,1215,1299,1410,1503,1600,1714,1837,1953,2100,2246,2384,2561,2693,2818,2947,3069,3163,3261,3387,3520,3619,3730,3839,3989,4142,4250,4350,4435,4530,4626,4744,4830,4917,5017,5104,5191,5291,5397,5493,5591,5680,5788,5884,5984,6130,6220,6338", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "167,282,401,518,616,713,827,950,1065,1210,1294,1405,1498,1595,1709,1832,1948,2095,2241,2379,2556,2688,2813,2942,3064,3158,3256,3382,3515,3614,3725,3834,3984,4137,4245,4345,4430,4525,4621,4739,4825,4912,5012,5099,5186,5286,5392,5488,5586,5675,5783,5879,5979,6125,6215,6333,6429"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8657,8774,8889,9008,9125,9223,9320,9434,9557,9672,9817,9901,10012,10105,10202,10316,10439,10555,10702,10848,10986,11163,11295,11420,11549,11671,11765,11863,11989,12122,12221,12332,12441,12591,12744,12852,12952,13037,13132,13228,13346,13432,13519,13619,13706,13793,13893,13999,14095,14193,14282,14390,14486,14586,14732,14822,14940", "endColumns": "116,114,118,116,97,96,113,122,114,144,83,110,92,96,113,122,115,146,145,137,176,131,124,128,121,93,97,125,132,98,110,108,149,152,107,99,84,94,95,117,85,86,99,86,86,99,105,95,97,88,107,95,99,145,89,117,95", "endOffsets": "8769,8884,9003,9120,9218,9315,9429,9552,9667,9812,9896,10007,10100,10197,10311,10434,10550,10697,10843,10981,11158,11290,11415,11544,11666,11760,11858,11984,12117,12216,12327,12436,12586,12739,12847,12947,13032,13127,13223,13341,13427,13514,13614,13701,13788,13888,13994,14090,14188,14277,14385,14481,14581,14727,14817,14935,15031"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2915,3013,3115,3215,3316,3422,3525,16027", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3008,3110,3210,3311,3417,3520,3641,16123"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-be/values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15036", "endColumns": "90", "endOffsets": "15122"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-be/values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,638,756,835,913,1005,1099,1195,1289,1385,1479,1575,1670,1762,1854,1937,2043,2149,2247,2355,2460,2565,2734,2834", "endColumns": "119,102,115,85,107,117,78,77,91,93,95,93,95,93,95,94,91,91,82,105,105,97,107,104,104,168,99,80", "endOffsets": "220,323,439,525,633,751,830,908,1000,1094,1190,1284,1380,1474,1570,1665,1757,1849,1932,2038,2144,2242,2350,2455,2560,2729,2829,2910"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,638,756,835,913,1005,1099,1195,1289,1385,1479,1575,1670,1762,1854,1937,2043,2149,2247,2355,2460,2565,2734,15549", "endColumns": "119,102,115,85,107,117,78,77,91,93,95,93,95,93,95,94,91,91,82,105,105,97,107,104,104,168,99,80", "endOffsets": "220,323,439,525,633,751,830,908,1000,1094,1190,1284,1380,1474,1570,1665,1757,1849,1932,2038,2144,2242,2350,2455,2560,2729,2829,15625"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-be/values-be.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "187,280,364,458,561,647,727,816,904,986,1069,1156,1228,1315,1399,1477,1553,1638,1708", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "275,359,453,556,642,722,811,899,981,1064,1151,1223,1310,1394,1472,1548,1633,1703,1826"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3646,3739,3823,3917,4020,4106,4186,15127,15215,15379,15462,15630,15702,15789,15873,15951,16128,16213,16283", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "3734,3818,3912,4015,4101,4181,4270,15210,15292,15457,15544,15697,15784,15868,15946,16022,16208,16278,16401"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-be/values-be.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,223", "endColumns": "80,86,102", "endOffsets": "131,218,321"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2834,16406,16493", "endColumns": "80,86,102", "endOffsets": "2910,16488,16591"}}]}]}