{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-da/values-da.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,419,502,602,715,792,869,960,1053,1149,1243,1338,1431,1526,1624,1715,1806,1885,1994,2102,2198,2312,2414,2515,2668,2765", "endColumns": "102,98,111,82,99,112,76,76,90,92,95,93,94,92,94,97,90,90,78,108,107,95,113,101,100,152,96,78", "endOffsets": "203,302,414,497,597,710,787,864,955,1048,1144,1238,1333,1426,1521,1619,1710,1801,1880,1989,2097,2193,2307,2409,2510,2663,2760,2839"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,419,502,602,715,792,869,960,1053,1149,1243,1338,1431,1526,1624,1715,1806,1885,1994,2102,2198,2312,2414,2515,2668,15069", "endColumns": "102,98,111,82,99,112,76,76,90,92,95,93,94,92,94,97,90,90,78,108,107,95,113,101,100,152,96,78", "endOffsets": "203,302,414,497,597,710,787,864,955,1048,1144,1238,1333,1426,1521,1619,1710,1801,1880,1989,2097,2193,2307,2409,2510,2663,2760,15143"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-da/values-da.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "94", "endOffsets": "145"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14549", "endColumns": "94", "endOffsets": "14639"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2840,2936,3038,3135,3233,3340,3449,15526", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "2931,3033,3130,3228,3335,3444,3562,15622"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,391,505,605,700,812,956,1078,1227,1311,1411,1500,1594,1708,1826,1931,2056,2176,2312,2485,2615,2732,2854,2973,3063,3161,3280,3416,3514,3632,3734,3860,3993,4098,4196,4276,4369,4462,4576,4660,4745,4846,4926,5010,5111,5210,5305,5405,5492,5597,5699,5804,5921,6001,6103", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "166,279,386,500,600,695,807,951,1073,1222,1306,1406,1495,1589,1703,1821,1926,2051,2171,2307,2480,2610,2727,2849,2968,3058,3156,3275,3411,3509,3627,3729,3855,3988,4093,4191,4271,4364,4457,4571,4655,4740,4841,4921,5005,5106,5205,5300,5400,5487,5592,5694,5799,5916,5996,6098,6197"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8402,8518,8631,8738,8852,8952,9047,9159,9303,9425,9574,9658,9758,9847,9941,10055,10173,10278,10403,10523,10659,10832,10962,11079,11201,11320,11410,11508,11627,11763,11861,11979,12081,12207,12340,12445,12543,12623,12716,12809,12923,13007,13092,13193,13273,13357,13458,13557,13652,13752,13839,13944,14046,14151,14268,14348,14450", "endColumns": "115,112,106,113,99,94,111,143,121,148,83,99,88,93,113,117,104,124,119,135,172,129,116,121,118,89,97,118,135,97,117,101,125,132,104,97,79,92,92,113,83,84,100,79,83,100,98,94,99,86,104,101,104,116,79,101,98", "endOffsets": "8513,8626,8733,8847,8947,9042,9154,9298,9420,9569,9653,9753,9842,9936,10050,10168,10273,10398,10518,10654,10827,10957,11074,11196,11315,11405,11503,11622,11758,11856,11974,12076,12202,12335,12440,12538,12618,12711,12804,12918,13002,13087,13188,13268,13352,13453,13552,13647,13747,13834,13939,14041,14146,14263,14343,14445,14544"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-da/values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,503,621,733,830,930,1056,1181,1282,1401,1555,1676,1796,1905,2002,2132,2223,2325,2429,2528,2627,2743,2864,2973,3081,3187,3299,3414,3534,3646,3763,3850,3931,4031,4169,4326", "endColumns": "106,100,95,93,117,111,96,99,125,124,100,118,153,120,119,108,96,129,90,101,103,98,98,115,120,108,107,105,111,114,119,111,116,86,80,99,137,156,88", "endOffsets": "207,308,404,498,616,728,825,925,1051,1176,1277,1396,1550,1671,1791,1900,1997,2127,2218,2320,2424,2523,2622,2738,2859,2968,3076,3182,3294,3409,3529,3641,3758,3845,3926,4026,4164,4321,4410"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4181,4288,4389,4485,4579,4697,4809,4906,5006,5132,5257,5358,5477,5631,5752,5872,5981,6078,6208,6299,6401,6505,6604,6703,6819,6940,7049,7157,7263,7375,7490,7610,7722,7839,7926,8007,8107,8245,14815", "endColumns": "106,100,95,93,117,111,96,99,125,124,100,118,153,120,119,108,96,129,90,101,103,98,98,115,120,108,107,105,111,114,119,111,116,86,80,99,137,156,88", "endOffsets": "4283,4384,4480,4574,4692,4804,4901,5001,5127,5252,5353,5472,5626,5747,5867,5976,6073,6203,6294,6396,6500,6599,6698,6814,6935,7044,7152,7258,7370,7485,7605,7717,7834,7921,8002,8102,8240,8397,14899"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-da/values-da.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,130,220", "endColumns": "74,89,86", "endOffsets": "125,215,302"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2765,15892,15982", "endColumns": "74,89,86", "endOffsets": "2835,15977,16064"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-da/values-da.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "181,273,353,448,547,629,706,795,884,966,1047,1131,1201,1292,1366,1438,1509,1587,1654", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,90,73,71,70,77,66,119", "endOffsets": "268,348,443,542,624,701,790,879,961,1042,1126,1196,1287,1361,1433,1504,1582,1649,1769"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3567,3659,3739,3834,3933,4015,4092,14644,14733,14904,14985,15148,15218,15309,15383,15455,15627,15705,15772", "endColumns": "91,79,94,98,81,76,88,88,81,80,83,69,90,73,71,70,77,66,119", "endOffsets": "3654,3734,3829,3928,4010,4087,4176,14728,14810,14980,15064,15213,15304,15378,15450,15521,15700,15767,15887"}}]}]}