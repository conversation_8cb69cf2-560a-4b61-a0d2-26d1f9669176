{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-km/values-km.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-km/values-km.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,278,358,462,560,648,732,815,900,987,1067,1152,1228,1316,1390,1462,1533,1617,1683", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,87,73,71,70,83,65,117", "endOffsets": "273,353,457,555,643,727,810,895,982,1062,1147,1223,1311,1385,1457,1528,1612,1678,1796"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3607,3692,3772,3876,3974,4062,4146,14807,14892,15073,15153,15321,15397,15485,15559,15631,15803,15887,15953", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,87,73,71,70,83,65,117", "endOffsets": "3687,3767,3871,3969,4057,4141,4224,14887,14974,15148,15233,15392,15480,15554,15626,15697,15882,15948,16066"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,398,514,616,722,845,989,1117,1269,1360,1460,1560,1670,1794,1919,2024,2150,2276,2404,2566,2688,2802,2915,3038,3139,3239,3365,3504,3608,3713,3825,3950,4078,4195,4303,4379,4476,4572,4680,4768,4856,4957,5037,5121,5221,5323,5419,5528,5615,5720,5818,5929,6046,6126,6233", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "168,287,393,509,611,717,840,984,1112,1264,1355,1455,1555,1665,1789,1914,2019,2145,2271,2399,2561,2683,2797,2910,3033,3134,3234,3360,3499,3603,3708,3820,3945,4073,4190,4298,4374,4471,4567,4675,4763,4851,4952,5032,5116,5216,5318,5414,5523,5610,5715,5813,5924,6041,6121,6228,6328"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8437,8555,8674,8780,8896,8998,9104,9227,9371,9499,9651,9742,9842,9942,10052,10176,10301,10406,10532,10658,10786,10948,11070,11184,11297,11420,11521,11621,11747,11886,11990,12095,12207,12332,12460,12577,12685,12761,12858,12954,13062,13150,13238,13339,13419,13503,13603,13705,13801,13910,13997,14102,14200,14311,14428,14508,14615", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "8550,8669,8775,8891,8993,9099,9222,9366,9494,9646,9737,9837,9937,10047,10171,10296,10401,10527,10653,10781,10943,11065,11179,11292,11415,11516,11616,11742,11881,11985,12090,12202,12327,12455,12572,12680,12756,12853,12949,13057,13145,13233,13334,13414,13498,13598,13700,13796,13905,13992,14097,14195,14306,14423,14503,14610,14710"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-km/values-km.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,233", "endColumns": "86,90,91", "endOffsets": "137,228,320"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2799,16071,16162", "endColumns": "86,90,91", "endOffsets": "2881,16157,16249"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,406,500,622,730,829,923,1042,1160,1262,1382,1529,1646,1762,1873,1963,2087,2176,2286,2395,2500,2598,2713,2834,2944,3054,3158,3265,3385,3510,3625,3743,3830,3915,4021,4157,4313", "endColumns": "106,100,92,93,121,107,98,93,118,117,101,119,146,116,115,110,89,123,88,109,108,104,97,114,120,109,109,103,106,119,124,114,117,86,84,105,135,155,93", "endOffsets": "207,308,401,495,617,725,824,918,1037,1155,1257,1377,1524,1641,1757,1868,1958,2082,2171,2281,2390,2495,2593,2708,2829,2939,3049,3153,3260,3380,3505,3620,3738,3825,3910,4016,4152,4308,4402"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4229,4336,4437,4530,4624,4746,4854,4953,5047,5166,5284,5386,5506,5653,5770,5886,5997,6087,6211,6300,6410,6519,6624,6722,6837,6958,7068,7178,7282,7389,7509,7634,7749,7867,7954,8039,8145,8281,14979", "endColumns": "106,100,92,93,121,107,98,93,118,117,101,119,146,116,115,110,89,123,88,109,108,104,97,114,120,109,109,103,106,119,124,114,117,86,84,105,135,155,93", "endOffsets": "4331,4432,4525,4619,4741,4849,4948,5042,5161,5279,5381,5501,5648,5765,5881,5992,6082,6206,6295,6405,6514,6619,6717,6832,6953,7063,7173,7277,7384,7504,7629,7744,7862,7949,8034,8140,8276,8432,15068"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,308,420,507,611,729,806,883,974,1067,1163,1257,1358,1451,1546,1640,1731,1822,1905,2009,2114,2214,2324,2431,2539,2701,2799", "endColumns": "102,99,111,86,103,117,76,76,90,92,95,93,100,92,94,93,90,90,82,103,104,99,109,106,107,161,97,82", "endOffsets": "203,303,415,502,606,724,801,878,969,1062,1158,1252,1353,1446,1541,1635,1726,1817,1900,2004,2109,2209,2319,2426,2534,2696,2794,2877"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,308,420,507,611,729,806,883,974,1067,1163,1257,1358,1451,1546,1640,1731,1822,1905,2009,2114,2214,2324,2431,2539,2701,15238", "endColumns": "102,99,111,86,103,117,76,76,90,92,95,93,100,92,94,93,90,90,82,103,104,99,109,106,107,161,97,82", "endOffsets": "203,303,415,502,606,724,801,878,969,1062,1158,1252,1353,1446,1541,1635,1726,1817,1900,2004,2109,2209,2319,2426,2534,2696,2794,15316"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-km/values-km.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14715", "endColumns": "91", "endOffsets": "14802"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-km/values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2886,2981,3084,3182,3282,3383,3495,15702", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "2976,3079,3177,3277,3378,3490,3602,15798"}}]}]}