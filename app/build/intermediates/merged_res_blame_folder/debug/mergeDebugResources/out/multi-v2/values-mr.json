{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-mr/values-mr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,84", "endOffsets": "123,208,293"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2807,15991,16076", "endColumns": "72,84,84", "endOffsets": "2875,16071,16156"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2880,2980,3084,3185,3288,3390,3495,15630", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "2975,3079,3180,3283,3385,3490,3607,15726"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,735,812,890,981,1074,1168,1265,1366,1459,1554,1651,1742,1833,1913,2025,2127,2223,2332,2433,2545,2702,2807", "endColumns": "110,105,106,89,100,114,76,77,90,92,93,96,100,92,94,96,90,90,79,111,101,95,108,100,111,156,104,79", "endOffsets": "211,317,424,514,615,730,807,885,976,1069,1163,1260,1361,1454,1549,1646,1737,1828,1908,2020,2122,2218,2327,2428,2540,2697,2802,2882"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,735,812,890,981,1074,1168,1265,1366,1459,1554,1651,1742,1833,1913,2025,2127,2223,2332,2433,2545,2702,15167", "endColumns": "110,105,106,89,100,114,76,77,90,92,93,96,100,92,94,96,90,90,79,111,101,95,108,100,111,156,104,79", "endOffsets": "211,317,424,514,615,730,807,885,976,1069,1163,1260,1361,1454,1549,1646,1737,1828,1908,2020,2122,2218,2327,2428,2540,2697,2802,15242"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-mr/values-mr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,355,452,550,637,723,808,897,980,1060,1145,1216,1300,1376,1452,1528,1604,1670", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,83,75,75,75,75,65,117", "endOffsets": "268,350,447,545,632,718,803,892,975,1055,1140,1211,1295,1371,1447,1523,1599,1665,1783"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3612,3706,3788,3885,3983,4070,4156,14743,14832,15002,15082,15247,15318,15402,15478,15554,15731,15807,15873", "endColumns": "93,81,96,97,86,85,84,88,82,79,84,70,83,75,75,75,75,65,117", "endOffsets": "3701,3783,3880,3978,4065,4151,4236,14827,14910,15077,15162,15313,15397,15473,15549,15625,15802,15868,15986"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,625,737,834,927,1048,1168,1269,1391,1538,1660,1782,1886,1981,2111,2200,2305,2417,2519,2617,2728,2843,2954,3065,3165,3269,3382,3495,3604,3713,3800,3882,3983,4116,4268", "endColumns": "106,100,97,93,119,111,96,92,120,119,100,121,146,121,121,103,94,129,88,104,111,101,97,110,114,110,110,99,103,112,112,108,108,86,81,100,132,151,86", "endOffsets": "207,308,406,500,620,732,829,922,1043,1163,1264,1386,1533,1655,1777,1881,1976,2106,2195,2300,2412,2514,2612,2723,2838,2949,3060,3160,3264,3377,3490,3599,3708,3795,3877,3978,4111,4263,4350"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4241,4348,4449,4547,4641,4761,4873,4970,5063,5184,5304,5405,5527,5674,5796,5918,6022,6117,6247,6336,6441,6553,6655,6753,6864,6979,7090,7201,7301,7405,7518,7631,7740,7849,7936,8018,8119,8252,14915", "endColumns": "106,100,97,93,119,111,96,92,120,119,100,121,146,121,121,103,94,129,88,104,111,101,97,110,114,110,110,99,103,112,112,108,108,86,81,100,132,151,86", "endOffsets": "4343,4444,4542,4636,4756,4868,4965,5058,5179,5299,5400,5522,5669,5791,5913,6017,6112,6242,6331,6436,6548,6650,6748,6859,6974,7085,7196,7296,7400,7513,7626,7735,7844,7931,8013,8114,8247,8399,14997"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-mr/values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,303,414,539,642,742,857,993,1116,1262,1347,1453,1544,1642,1756,1886,1997,2132,2266,2394,2572,2697,2813,2932,3057,3149,3244,3364,3493,3593,3696,3805,3942,4084,4199,4297,4373,4476,4580,4687,4772,4862,4962,5042,5125,5224,5323,5420,5519,5606,5710,5810,5914,6032,6112,6212", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "175,298,409,534,637,737,852,988,1111,1257,1342,1448,1539,1637,1751,1881,1992,2127,2261,2389,2567,2692,2808,2927,3052,3144,3239,3359,3488,3588,3691,3800,3937,4079,4194,4292,4368,4471,4575,4682,4767,4857,4957,5037,5120,5219,5318,5415,5514,5601,5705,5805,5909,6027,6107,6207,6301"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8404,8529,8652,8763,8888,8991,9091,9206,9342,9465,9611,9696,9802,9893,9991,10105,10235,10346,10481,10615,10743,10921,11046,11162,11281,11406,11498,11593,11713,11842,11942,12045,12154,12291,12433,12548,12646,12722,12825,12929,13036,13121,13211,13311,13391,13474,13573,13672,13769,13868,13955,14059,14159,14263,14381,14461,14561", "endColumns": "124,122,110,124,102,99,114,135,122,145,84,105,90,97,113,129,110,134,133,127,177,124,115,118,124,91,94,119,128,99,102,108,136,141,114,97,75,102,103,106,84,89,99,79,82,98,98,96,98,86,103,99,103,117,79,99,93", "endOffsets": "8524,8647,8758,8883,8986,9086,9201,9337,9460,9606,9691,9797,9888,9986,10100,10230,10341,10476,10610,10738,10916,11041,11157,11276,11401,11493,11588,11708,11837,11937,12040,12149,12286,12428,12543,12641,12717,12820,12924,13031,13116,13206,13306,13386,13469,13568,13667,13764,13863,13950,14054,14154,14258,14376,14456,14556,14650"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-mr/values-mr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14655", "endColumns": "87", "endOffsets": "14738"}}]}]}