{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,91", "endOffsets": "140,230,322"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2828,16254,16344", "endColumns": "89,89,91", "endOffsets": "2913,16339,16431"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,293,380,477,578,664,741,832,924,1009,1089,1174,1247,1337,1414,1493,1570,1649,1719", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,89,76,78,76,78,69,117", "endOffsets": "288,375,472,573,659,736,827,919,1004,1084,1169,1242,1332,1409,1488,1565,1644,1714,1832"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3644,3741,3828,3925,4026,4112,4189,14972,15064,15241,15321,15490,15563,15653,15730,15809,15987,16066,16136", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,89,76,78,76,78,69,117", "endOffsets": "3736,3823,3920,4021,4107,4184,4275,15059,15144,15316,15401,15558,15648,15725,15804,15881,16061,16131,16249"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,503,628,743,840,934,1057,1177,1285,1411,1568,1693,1815,1918,2011,2135,2225,2326,2438,2540,2642,2761,2878,3002,3123,3232,3347,3468,3591,3707,3825,3912,4000,4117,4256,4422", "endColumns": "106,100,95,93,124,114,96,93,122,119,107,125,156,124,121,102,92,123,89,100,111,101,101,118,116,123,120,108,114,120,122,115,117,86,87,116,138,165,91", "endOffsets": "207,308,404,498,623,738,835,929,1052,1172,1280,1406,1563,1688,1810,1913,2006,2130,2220,2321,2433,2535,2637,2756,2873,2997,3118,3227,3342,3463,3586,3702,3820,3907,3995,4112,4251,4417,4509"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4280,4387,4488,4584,4678,4803,4918,5015,5109,5232,5352,5460,5586,5743,5868,5990,6093,6186,6310,6400,6501,6613,6715,6817,6936,7053,7177,7298,7407,7522,7643,7766,7882,8000,8087,8175,8292,8431,15149", "endColumns": "106,100,95,93,124,114,96,93,122,119,107,125,156,124,121,102,92,123,89,100,111,101,101,118,116,123,120,108,114,120,122,115,117,86,87,116,138,165,91", "endOffsets": "4382,4483,4579,4673,4798,4913,5010,5104,5227,5347,5455,5581,5738,5863,5985,6088,6181,6305,6395,6496,6608,6710,6812,6931,7048,7172,7293,7402,7517,7638,7761,7877,7995,8082,8170,8287,8426,8592,15236"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "93", "endOffsets": "144"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14878", "endColumns": "93", "endOffsets": "14967"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,424,510,614,732,814,895,986,1079,1175,1269,1370,1463,1558,1663,1754,1845,1933,2039,2147,2248,2353,2461,2562,2731,2828", "endColumns": "108,103,105,85,103,117,81,80,90,92,95,93,100,92,94,104,90,90,87,105,107,100,104,107,100,168,96,83", "endOffsets": "209,313,419,505,609,727,809,890,981,1074,1170,1264,1365,1458,1553,1658,1749,1840,1928,2034,2142,2243,2348,2456,2557,2726,2823,2907"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,424,510,614,732,814,895,986,1079,1175,1269,1370,1463,1558,1663,1754,1845,1933,2039,2147,2248,2353,2461,2562,2731,15406", "endColumns": "108,103,105,85,103,117,81,80,90,92,95,93,100,92,94,104,90,90,87,105,107,100,104,107,100,168,96,83", "endOffsets": "209,313,419,505,609,727,809,890,981,1074,1170,1264,1365,1458,1553,1658,1749,1840,1928,2034,2142,2243,2348,2456,2557,2726,2823,15485"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2918,3016,3118,3215,3319,3423,3528,15886", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3011,3113,3210,3314,3418,3523,3639,15982"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-b+sr+Latn/values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,289,417,534,633,727,838,974,1094,1236,1321,1421,1516,1614,1730,1855,1960,2101,2241,2374,2554,2679,2799,2924,3046,3142,3240,3358,3488,3588,3690,3799,3941,4090,4199,4302,4379,4478,4576,4685,4774,4860,4967,5047,5130,5227,5330,5423,5521,5608,5716,5813,5915,6048,6128,6237", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "167,284,412,529,628,722,833,969,1089,1231,1316,1416,1511,1609,1725,1850,1955,2096,2236,2369,2549,2674,2794,2919,3041,3137,3235,3353,3483,3583,3685,3794,3936,4085,4194,4297,4374,4473,4571,4680,4769,4855,4962,5042,5125,5222,5325,5418,5516,5603,5711,5808,5910,6043,6123,6232,6331"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8597,8714,8831,8959,9076,9175,9269,9380,9516,9636,9778,9863,9963,10058,10156,10272,10397,10502,10643,10783,10916,11096,11221,11341,11466,11588,11684,11782,11900,12030,12130,12232,12341,12483,12632,12741,12844,12921,13020,13118,13227,13316,13402,13509,13589,13672,13769,13872,13965,14063,14150,14258,14355,14457,14590,14670,14779", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "8709,8826,8954,9071,9170,9264,9375,9511,9631,9773,9858,9958,10053,10151,10267,10392,10497,10638,10778,10911,11091,11216,11336,11461,11583,11679,11777,11895,12025,12125,12227,12336,12478,12627,12736,12839,12916,13015,13113,13222,13311,13397,13504,13584,13667,13764,13867,13960,14058,14145,14253,14350,14452,14585,14665,14774,14873"}}]}]}