{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-in/values-in.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,401,517,615,721,844,991,1114,1264,1351,1455,1548,1652,1770,1890,1999,2139,2277,2406,2584,2706,2826,2949,3072,3166,3267,3387,3520,3622,3729,3836,3978,4125,4234,4334,4410,4506,4601,4719,4808,4893,4992,5072,5155,5254,5353,5450,5550,5637,5740,5839,5943,6060,6140,6245", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "169,284,396,512,610,716,839,986,1109,1259,1346,1450,1543,1647,1765,1885,1994,2134,2272,2401,2579,2701,2821,2944,3067,3161,3262,3382,3515,3617,3724,3831,3973,4120,4229,4329,4405,4501,4596,4714,4803,4888,4987,5067,5150,5249,5348,5445,5545,5632,5735,5834,5938,6055,6135,6240,6335"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8474,8593,8708,8820,8936,9034,9140,9263,9410,9533,9683,9770,9874,9967,10071,10189,10309,10418,10558,10696,10825,11003,11125,11245,11368,11491,11585,11686,11806,11939,12041,12148,12255,12397,12544,12653,12753,12829,12925,13020,13138,13227,13312,13411,13491,13574,13673,13772,13869,13969,14056,14159,14258,14362,14479,14559,14664", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "8588,8703,8815,8931,9029,9135,9258,9405,9528,9678,9765,9869,9962,10066,10184,10304,10413,10553,10691,10820,10998,11120,11240,11363,11486,11580,11681,11801,11934,12036,12143,12250,12392,12539,12648,12748,12824,12920,13015,13133,13222,13307,13406,13486,13569,13668,13767,13864,13964,14051,14154,14253,14357,14474,14554,14659,14754"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,506,626,736,838,928,1064,1196,1292,1406,1554,1682,1806,1915,2004,2132,2222,2325,2430,2528,2621,2731,2842,2954,3062,3172,3286,3407,3530,3643,3758,3845,3931,4040,4177,4337", "endColumns": "106,100,97,94,119,109,101,89,135,131,95,113,147,127,123,108,88,127,89,102,104,97,92,109,110,111,107,109,113,120,122,112,114,86,85,108,136,159,96", "endOffsets": "207,308,406,501,621,731,833,923,1059,1191,1287,1401,1549,1677,1801,1910,1999,2127,2217,2320,2425,2523,2616,2726,2837,2949,3057,3167,3281,3402,3525,3638,3753,3840,3926,4035,4172,4332,4429"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4242,4349,4450,4548,4643,4763,4873,4975,5065,5201,5333,5429,5543,5691,5819,5943,6052,6141,6269,6359,6462,6567,6665,6758,6868,6979,7091,7199,7309,7423,7544,7667,7780,7895,7982,8068,8177,8314,15027", "endColumns": "106,100,97,94,119,109,101,89,135,131,95,113,147,127,123,108,88,127,89,102,104,97,92,109,110,111,107,109,113,120,122,112,114,86,85,108,136,159,96", "endOffsets": "4344,4445,4543,4638,4758,4868,4970,5060,5196,5328,5424,5538,5686,5814,5938,6047,6136,6264,6354,6457,6562,6660,6753,6863,6974,7086,7194,7304,7418,7539,7662,7775,7890,7977,8063,8172,8309,8469,15119"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-in/values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,220", "endColumns": "78,85,89", "endOffsets": "129,215,305"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2803,16128,16214", "endColumns": "78,85,89", "endOffsets": "2877,16209,16299"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-in/values-in.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,275,357,455,555,641,724,815,902,987,1069,1152,1224,1316,1393,1470,1543,1621,1687", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "270,352,450,550,636,719,810,897,982,1064,1147,1219,1311,1388,1465,1538,1616,1682,1801"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3612,3702,3784,3882,3982,4068,4151,14855,14942,15124,15206,15373,15445,15537,15614,15691,15865,15943,16009", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,91,76,76,72,77,65,118", "endOffsets": "3697,3779,3877,3977,4063,4146,4237,14937,15022,15201,15284,15440,15532,15609,15686,15759,15938,16004,16123"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,317,422,509,613,729,811,890,981,1074,1170,1264,1365,1458,1553,1647,1738,1829,1915,2018,2127,2228,2332,2440,2548,2704,2803", "endColumns": "109,101,104,86,103,115,81,78,90,92,95,93,100,92,94,93,90,90,85,102,108,100,103,107,107,155,98,83", "endOffsets": "210,312,417,504,608,724,806,885,976,1069,1165,1259,1360,1453,1548,1642,1733,1824,1910,2013,2122,2223,2327,2435,2543,2699,2798,2882"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,317,422,509,613,729,811,890,981,1074,1170,1264,1365,1458,1553,1647,1738,1829,1915,2018,2127,2228,2332,2440,2548,2704,15289", "endColumns": "109,101,104,86,103,115,81,78,90,92,95,93,100,92,94,93,90,90,85,102,108,100,103,107,107,155,98,83", "endOffsets": "210,312,417,504,608,724,806,885,976,1069,1165,1259,1360,1453,1548,1642,1733,1824,1910,2013,2122,2223,2327,2435,2543,2699,2798,15368"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-in/values-in.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "95", "endOffsets": "146"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14759", "endColumns": "95", "endOffsets": "14850"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-in/values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2882,2977,3079,3176,3273,3379,3497,15764", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "2972,3074,3171,3268,3374,3492,3607,15860"}}]}]}