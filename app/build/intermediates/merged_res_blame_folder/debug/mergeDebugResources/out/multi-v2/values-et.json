{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-et/values-et.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-et/values-et.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,225", "endColumns": "79,89,88", "endOffsets": "130,220,309"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2860,16186,16276", "endColumns": "79,89,88", "endOffsets": "2935,16271,16360"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-et/values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2940,3035,3137,3235,3338,3444,3549,15815", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3030,3132,3230,3333,3439,3544,3664,15911"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-et/values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,297,411,530,629,730,848,981,1101,1249,1336,1437,1531,1630,1746,1873,1979,2114,2247,2378,2553,2679,2798,2919,3041,3136,3233,3353,3487,3592,3695,3800,3931,4066,4174,4277,4354,4450,4546,4650,4737,4822,4928,5008,5094,5195,5299,5393,5497,5584,5693,5794,5901,6018,6098,6202", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "170,292,406,525,624,725,843,976,1096,1244,1331,1432,1526,1625,1741,1868,1974,2109,2242,2373,2548,2674,2793,2914,3036,3131,3228,3348,3482,3587,3690,3795,3926,4061,4169,4272,4349,4445,4541,4645,4732,4817,4923,5003,5089,5190,5294,5388,5492,5579,5688,5789,5896,6013,6093,6197,6296"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8584,8704,8826,8940,9059,9158,9259,9377,9510,9630,9778,9865,9966,10060,10159,10275,10402,10508,10643,10776,10907,11082,11208,11327,11448,11570,11665,11762,11882,12016,12121,12224,12329,12460,12595,12703,12806,12883,12979,13075,13179,13266,13351,13457,13537,13623,13724,13828,13922,14026,14113,14222,14323,14430,14547,14627,14731", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "8699,8821,8935,9054,9153,9254,9372,9505,9625,9773,9860,9961,10055,10154,10270,10397,10503,10638,10771,10902,11077,11203,11322,11443,11565,11660,11757,11877,12011,12116,12219,12324,12455,12590,12698,12801,12878,12974,13070,13174,13261,13346,13452,13532,13618,13719,13823,13917,14021,14108,14217,14318,14425,14542,14622,14726,14825"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-et/values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,407,501,622,736,836,929,1051,1171,1273,1393,1550,1679,1806,1916,2006,2131,2221,2323,2431,2531,2629,2744,2857,2991,3123,3241,3362,3476,3594,3704,3818,3905,3989,4092,4239,4405", "endColumns": "106,100,93,93,120,113,99,92,121,119,101,119,156,128,126,109,89,124,89,101,107,99,97,114,112,133,131,117,120,113,117,109,113,86,83,102,146,165,90", "endOffsets": "207,308,402,496,617,731,831,924,1046,1166,1268,1388,1545,1674,1801,1911,2001,2126,2216,2318,2426,2526,2624,2739,2852,2986,3118,3236,3357,3471,3589,3699,3813,3900,3984,4087,4234,4400,4491"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4284,4391,4492,4586,4680,4801,4915,5015,5108,5230,5350,5452,5572,5729,5858,5985,6095,6185,6310,6400,6502,6610,6710,6808,6923,7036,7170,7302,7420,7541,7655,7773,7883,7997,8084,8168,8271,8418,15095", "endColumns": "106,100,93,93,120,113,99,92,121,119,101,119,156,128,126,109,89,124,89,101,107,99,97,114,112,133,131,117,120,113,117,109,113,86,83,102,146,165,90", "endOffsets": "4386,4487,4581,4675,4796,4910,5010,5103,5225,5345,5447,5567,5724,5853,5980,6090,6180,6305,6395,6497,6605,6705,6803,6918,7031,7165,7297,7415,7536,7650,7768,7878,7992,8079,8163,8266,8413,8579,15181"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-et/values-et.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14830", "endColumns": "89", "endOffsets": "14915"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-et/values-et.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,279,359,455,550,632,710,801,892,976,1058,1143,1215,1301,1376,1451,1523,1600,1671", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,85,74,74,71,76,70,121", "endOffsets": "274,354,450,545,627,705,796,887,971,1053,1138,1210,1296,1371,1446,1518,1595,1666,1788"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3669,3762,3842,3938,4033,4115,4193,14920,15011,15186,15268,15435,15507,15593,15668,15743,15916,15993,16064", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,85,74,74,71,76,70,121", "endOffsets": "3757,3837,3933,4028,4110,4188,4279,15006,15090,15263,15348,15502,15588,15663,15738,15810,15988,16059,16181"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-et/values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,329,440,526,628,745,825,903,995,1089,1194,1296,1406,1500,1601,1695,1787,1880,1963,2074,2179,2278,2388,2489,2592,2758,2860", "endColumns": "116,106,110,85,101,116,79,77,91,93,104,101,109,93,100,93,91,92,82,110,104,98,109,100,102,165,101,81", "endOffsets": "217,324,435,521,623,740,820,898,990,1084,1189,1291,1401,1495,1596,1690,1782,1875,1958,2069,2174,2273,2383,2484,2587,2753,2855,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,329,440,526,628,745,825,903,995,1089,1194,1296,1406,1500,1601,1695,1787,1880,1963,2074,2179,2278,2388,2489,2592,2758,15353", "endColumns": "116,106,110,85,101,116,79,77,91,93,104,101,109,93,100,93,91,92,82,110,104,98,109,100,102,165,101,81", "endOffsets": "217,324,435,521,623,740,820,898,990,1084,1189,1291,1401,1495,1596,1690,1782,1875,1958,2069,2174,2273,2383,2484,2587,2753,2855,15430"}}]}]}