{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-sr/values-sr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,287,414,530,628,722,833,969,1088,1230,1315,1415,1510,1608,1724,1849,1954,2095,2235,2368,2548,2673,2793,2918,3040,3136,3234,3351,3481,3581,3683,3792,3934,4083,4192,4295,4372,4470,4568,4677,4766,4852,4959,5039,5122,5219,5322,5415,5513,5600,5708,5805,5907,6040,6120,6227", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "166,282,409,525,623,717,828,964,1083,1225,1310,1410,1505,1603,1719,1844,1949,2090,2230,2363,2543,2668,2788,2913,3035,3131,3229,3346,3476,3576,3678,3787,3929,4078,4187,4290,4367,4465,4563,4672,4761,4847,4954,5034,5117,5214,5317,5410,5508,5595,5703,5800,5902,6035,6115,6222,6319"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8583,8699,8815,8942,9058,9156,9250,9361,9497,9616,9758,9843,9943,10038,10136,10252,10377,10482,10623,10763,10896,11076,11201,11321,11446,11568,11664,11762,11879,12009,12109,12211,12320,12462,12611,12720,12823,12900,12998,13096,13205,13294,13380,13487,13567,13650,13747,13850,13943,14041,14128,14236,14333,14435,14568,14648,14755", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "8694,8810,8937,9053,9151,9245,9356,9492,9611,9753,9838,9938,10033,10131,10247,10372,10477,10618,10758,10891,11071,11196,11316,11441,11563,11659,11757,11874,12004,12104,12206,12315,12457,12606,12715,12818,12895,12993,13091,13200,13289,13375,13482,13562,13645,13742,13845,13938,14036,14123,14231,14328,14430,14563,14643,14750,14847"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,503,628,743,839,933,1056,1176,1284,1410,1567,1692,1814,1916,2009,2133,2223,2324,2435,2537,2639,2758,2875,2998,3118,3227,3342,3463,3586,3702,3820,3907,3995,4112,4251,4417", "endColumns": "106,100,95,93,124,114,95,93,122,119,107,125,156,124,121,101,92,123,89,100,110,101,101,118,116,122,119,108,114,120,122,115,117,86,87,116,138,165,90", "endOffsets": "207,308,404,498,623,738,834,928,1051,1171,1279,1405,1562,1687,1809,1911,2004,2128,2218,2319,2430,2532,2634,2753,2870,2993,3113,3222,3337,3458,3581,3697,3815,3902,3990,4107,4246,4412,4503"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4271,4378,4479,4575,4669,4794,4909,5005,5099,5222,5342,5450,5576,5733,5858,5980,6082,6175,6299,6389,6490,6601,6703,6805,6924,7041,7164,7284,7393,7508,7629,7752,7868,7986,8073,8161,8278,8417,15122", "endColumns": "106,100,95,93,124,114,95,93,122,119,107,125,156,124,121,101,92,123,89,100,110,101,101,118,116,122,119,108,114,120,122,115,117,86,87,116,138,165,90", "endOffsets": "4373,4474,4570,4664,4789,4904,5000,5094,5217,5337,5445,5571,5728,5853,5975,6077,6170,6294,6384,6485,6596,6698,6800,6919,7036,7159,7279,7388,7503,7624,7747,7863,7981,8068,8156,8273,8412,8578,15208"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2909,3007,3109,3206,3310,3414,3519,15855", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3002,3104,3201,3305,3409,3514,3630,15951"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,423,509,613,731,812,892,983,1076,1172,1266,1367,1460,1555,1660,1751,1842,1930,2035,2143,2244,2348,2456,2557,2724,2821", "endColumns": "108,102,105,85,103,117,80,79,90,92,95,93,100,92,94,104,90,90,87,104,107,100,103,107,100,166,96,83", "endOffsets": "209,312,418,504,608,726,807,887,978,1071,1167,1261,1362,1455,1550,1655,1746,1837,1925,2030,2138,2239,2343,2451,2552,2719,2816,2900"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,423,509,613,731,812,892,983,1076,1172,1266,1367,1460,1555,1660,1751,1842,1930,2035,2143,2244,2348,2456,2557,2724,15378", "endColumns": "108,102,105,85,103,117,80,79,90,92,95,93,100,92,94,104,90,90,87,104,107,100,103,107,100,166,96,83", "endOffsets": "209,312,418,504,608,726,807,887,978,1071,1167,1261,1362,1455,1550,1655,1746,1837,1925,2030,2138,2239,2343,2451,2552,2719,2816,15457"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-sr/values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14852", "endColumns": "92", "endOffsets": "14940"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,231", "endColumns": "87,87,89", "endOffsets": "138,226,316"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2821,16223,16311", "endColumns": "87,87,89", "endOffsets": "2904,16306,16396"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-sr/values-sr.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,291,378,475,576,662,739,830,922,1007,1087,1172,1245,1334,1411,1489,1565,1644,1714", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,88,76,77,75,78,69,117", "endOffsets": "286,373,470,571,657,734,825,917,1002,1082,1167,1240,1329,1406,1484,1560,1639,1709,1827"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3635,3732,3819,3916,4017,4103,4180,14945,15037,15213,15293,15462,15535,15624,15701,15779,15956,16035,16105", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,88,76,77,75,78,69,117", "endOffsets": "3727,3814,3911,4012,4098,4175,4266,15032,15117,15288,15373,15530,15619,15696,15774,15850,16030,16100,16218"}}]}]}