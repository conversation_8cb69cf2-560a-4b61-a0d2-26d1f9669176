{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-te/values-te.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-te/values-te.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,217", "endColumns": "73,87,94", "endOffsets": "124,212,307"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2864,16595,16683", "endColumns": "73,87,94", "endOffsets": "2933,16678,16773"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2938,3040,3148,3250,3351,3457,3564,16231", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "3035,3143,3245,3346,3452,3559,3683,16327"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,315,429,559,663,762,878,1019,1131,1274,1358,1461,1557,1655,1771,1901,2009,2158,2305,2438,2634,2762,2878,2999,3136,3233,3330,3455,3583,3689,3795,3901,4044,4194,4302,4406,4482,4581,4682,4798,4892,4984,5091,5171,5254,5355,5483,5577,5689,5777,5888,5990,6107,6230,6310,6417", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,115,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "179,310,424,554,658,757,873,1014,1126,1269,1353,1456,1552,1650,1766,1896,2004,2153,2300,2433,2629,2757,2873,2994,3131,3228,3325,3450,3578,3684,3790,3896,4039,4189,4297,4401,4477,4576,4677,4793,4887,4979,5086,5166,5249,5350,5478,5572,5684,5772,5883,5985,6102,6225,6305,6412,6509"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8760,8889,9020,9134,9264,9368,9467,9583,9724,9836,9979,10063,10166,10262,10360,10476,10606,10714,10863,11010,11143,11339,11467,11583,11704,11841,11938,12035,12160,12288,12394,12500,12606,12749,12899,13007,13111,13187,13286,13387,13503,13597,13689,13796,13876,13959,14060,14188,14282,14394,14482,14593,14695,14812,14935,15015,15122", "endColumns": "128,130,113,129,103,98,115,140,111,142,83,102,95,97,115,129,107,148,146,132,195,127,115,120,136,96,96,124,127,105,105,105,142,149,107,103,75,98,100,115,93,91,106,79,82,100,127,93,111,87,110,101,116,122,79,106,96", "endOffsets": "8884,9015,9129,9259,9363,9462,9578,9719,9831,9974,10058,10161,10257,10355,10471,10601,10709,10858,11005,11138,11334,11462,11578,11699,11836,11933,12030,12155,12283,12389,12495,12601,12744,12894,13002,13106,13182,13281,13382,13498,13592,13684,13791,13871,13954,14055,14183,14277,14389,14477,14588,14690,14807,14930,15010,15117,15214"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,328,439,529,634,759,841,923,1014,1107,1203,1297,1398,1491,1586,1681,1772,1863,1947,2060,2168,2267,2378,2480,2597,2763,2864", "endColumns": "113,108,110,89,104,124,81,81,90,92,95,93,100,92,94,94,90,90,83,112,107,98,110,101,116,165,100,81", "endOffsets": "214,323,434,524,629,754,836,918,1009,1102,1198,1292,1393,1486,1581,1676,1767,1858,1942,2055,2163,2262,2373,2475,2592,2758,2859,2941"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,328,439,529,634,759,841,923,1014,1107,1203,1297,1398,1491,1586,1681,1772,1863,1947,2060,2168,2267,2378,2480,2597,2763,15744", "endColumns": "113,108,110,89,104,124,81,81,90,92,95,93,100,92,94,94,90,90,83,112,107,98,110,101,116,165,100,81", "endOffsets": "214,323,434,524,629,754,836,918,1009,1102,1198,1292,1393,1486,1581,1676,1767,1858,1942,2055,2163,2262,2373,2475,2592,2758,2859,15821"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-te/values-te.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "180,278,367,464,564,653,742,838,926,1010,1094,1184,1261,1348,1430,1510,1589,1666,1735", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,86,81,79,78,76,68,116", "endOffsets": "273,362,459,559,648,737,833,921,1005,1089,1179,1256,1343,1425,1505,1584,1661,1730,1847"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3688,3786,3875,3972,4072,4161,4250,15310,15398,15570,15654,15826,15903,15990,16072,16152,16332,16409,16478", "endColumns": "97,88,96,99,88,88,95,87,83,83,89,76,86,81,79,78,76,68,116", "endOffsets": "3781,3870,3967,4067,4156,4245,4341,15393,15477,15649,15739,15898,15985,16067,16147,16226,16404,16473,16590"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-te/values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15219", "endColumns": "90", "endOffsets": "15305"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-te/values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,414,511,632,743,841,939,1076,1215,1328,1459,1607,1734,1863,1970,2066,2201,2295,2404,2516,2627,2726,2842,2956,3078,3202,3313,3429,3550,3679,3797,3923,4010,4101,4206,4347,4519", "endColumns": "106,100,100,96,120,110,97,97,136,138,112,130,147,126,128,106,95,134,93,108,111,110,98,115,113,121,123,110,115,120,128,117,125,86,90,104,140,171,87", "endOffsets": "207,308,409,506,627,738,836,934,1071,1210,1323,1454,1602,1729,1858,1965,2061,2196,2290,2399,2511,2622,2721,2837,2951,3073,3197,3308,3424,3545,3674,3792,3918,4005,4096,4201,4342,4514,4602"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4346,4453,4554,4655,4752,4873,4984,5082,5180,5317,5456,5569,5700,5848,5975,6104,6211,6307,6442,6536,6645,6757,6868,6967,7083,7197,7319,7443,7554,7670,7791,7920,8038,8164,8251,8342,8447,8588,15482", "endColumns": "106,100,100,96,120,110,97,97,136,138,112,130,147,126,128,106,95,134,93,108,111,110,98,115,113,121,123,110,115,120,128,117,125,86,90,104,140,171,87", "endOffsets": "4448,4549,4650,4747,4868,4979,5077,5175,5312,5451,5564,5695,5843,5970,6099,6206,6302,6437,6531,6640,6752,6863,6962,7078,7192,7314,7438,7549,7665,7786,7915,8033,8159,8246,8337,8442,8583,8755,15565"}}]}]}