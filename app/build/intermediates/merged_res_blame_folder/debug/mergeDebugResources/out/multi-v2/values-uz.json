{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-uz/values-uz.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-uz/values-uz.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "191,290,375,479,586,682,765,855,948,1031,1112,1195,1269,1354,1430,1505,1578,1661,1729", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,84,75,74,72,82,67,116", "endOffsets": "285,370,474,581,677,760,850,943,1026,1107,1190,1264,1349,1425,1500,1573,1656,1724,1841"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3626,3725,3810,3914,4021,4117,4200,14976,15069,15242,15323,15490,15564,15649,15725,15800,15974,16057,16125", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,84,75,74,72,82,67,116", "endOffsets": "3720,3805,3909,4016,4112,4195,4285,15064,15147,15318,15401,15559,15644,15720,15795,15868,16052,16120,16237"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,631,747,848,941,1073,1198,1307,1443,1600,1730,1851,1958,2048,2183,2272,2383,2489,2599,2702,2831,2944,3056,3184,3292,3405,3530,3650,3773,3891,3978,4063,4171,4316,4483", "endColumns": "106,100,97,93,125,115,100,92,131,124,108,135,156,129,120,106,89,134,88,110,105,109,102,128,112,111,127,107,112,124,119,122,117,86,84,107,144,166,89", "endOffsets": "207,308,406,500,626,742,843,936,1068,1193,1302,1438,1595,1725,1846,1953,2043,2178,2267,2378,2484,2594,2697,2826,2939,3051,3179,3287,3400,3525,3645,3768,3886,3973,4058,4166,4311,4478,4568"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4290,4397,4498,4596,4690,4816,4932,5033,5126,5258,5383,5492,5628,5785,5915,6036,6143,6233,6368,6457,6568,6674,6784,6887,7016,7129,7241,7369,7477,7590,7715,7835,7958,8076,8163,8248,8356,8501,15152", "endColumns": "106,100,97,93,125,115,100,92,131,124,108,135,156,129,120,106,89,134,88,110,105,109,102,128,112,111,127,107,112,124,119,122,117,86,84,107,144,166,89", "endOffsets": "4392,4493,4591,4685,4811,4927,5028,5121,5253,5378,5487,5623,5780,5910,6031,6138,6228,6363,6452,6563,6669,6779,6882,7011,7124,7236,7364,7472,7585,7710,7830,7953,8071,8158,8243,8351,8496,8663,15237"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,140,251", "endColumns": "84,110,100", "endOffsets": "135,246,347"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2805,16242,16353", "endColumns": "84,110,100", "endOffsets": "2885,16348,16449"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,401,515,612,710,825,958,1067,1209,1293,1397,1491,1589,1703,1824,1933,2058,2181,2311,2479,2604,2725,2849,2970,3065,3163,3280,3406,3510,3620,3727,3850,3978,4091,4195,4279,4375,4469,4599,4687,4773,4874,4954,5038,5138,5242,5338,5437,5525,5633,5733,5836,5975,6055,6171", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,129,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "167,282,396,510,607,705,820,953,1062,1204,1288,1392,1486,1584,1698,1819,1928,2053,2176,2306,2474,2599,2720,2844,2965,3060,3158,3275,3401,3505,3615,3722,3845,3973,4086,4190,4274,4370,4464,4594,4682,4768,4869,4949,5033,5133,5237,5333,5432,5520,5628,5728,5831,5970,6050,6166,6269"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8668,8785,8900,9014,9128,9225,9323,9438,9571,9680,9822,9906,10010,10104,10202,10316,10437,10546,10671,10794,10924,11092,11217,11338,11462,11583,11678,11776,11893,12019,12123,12233,12340,12463,12591,12704,12808,12892,12988,13082,13212,13300,13386,13487,13567,13651,13751,13855,13951,14050,14138,14246,14346,14449,14588,14668,14784", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,129,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "8780,8895,9009,9123,9220,9318,9433,9566,9675,9817,9901,10005,10099,10197,10311,10432,10541,10666,10789,10919,11087,11212,11333,11457,11578,11673,11771,11888,12014,12118,12228,12335,12458,12586,12699,12803,12887,12983,13077,13207,13295,13381,13482,13562,13646,13746,13850,13946,14045,14133,14241,14341,14444,14583,14663,14779,14882"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,314,428,514,614,730,810,889,980,1073,1169,1263,1358,1451,1546,1641,1732,1824,1908,2017,2124,2225,2333,2438,2545,2706,2805", "endColumns": "104,103,113,85,99,115,79,78,90,92,95,93,94,92,94,94,90,91,83,108,106,100,107,104,106,160,98,83", "endOffsets": "205,309,423,509,609,725,805,884,975,1068,1164,1258,1353,1446,1541,1636,1727,1819,1903,2012,2119,2220,2328,2433,2540,2701,2800,2884"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,314,428,514,614,730,810,889,980,1073,1169,1263,1358,1451,1546,1641,1732,1824,1908,2017,2124,2225,2333,2438,2545,2706,15406", "endColumns": "104,103,113,85,99,115,79,78,90,92,95,93,94,92,94,94,90,91,83,108,106,100,107,104,106,160,98,83", "endOffsets": "205,309,423,509,609,725,805,884,975,1068,1164,1258,1353,1446,1541,1636,1727,1819,1903,2012,2119,2220,2328,2433,2540,2701,2800,15485"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-uz/values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2890,2992,3094,3195,3295,3403,3507,15873", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "2987,3089,3190,3290,3398,3502,3621,15969"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-uz/values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14887", "endColumns": "88", "endOffsets": "14971"}}]}]}