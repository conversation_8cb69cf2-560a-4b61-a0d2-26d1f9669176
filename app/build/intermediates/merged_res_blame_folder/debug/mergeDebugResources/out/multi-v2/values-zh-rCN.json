{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,207", "endColumns": "70,80,76", "endOffsets": "121,202,279"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2666,14580,14661", "endColumns": "70,80,76", "endOffsets": "2732,14656,14733"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,296,396,478,575,681,757,833,924,1017,1115,1211,1306,1399,1494,1586,1677,1768,1846,1942,2038,2133,2230,2325,2423,2572,2666", "endColumns": "95,94,99,81,96,105,75,75,90,92,97,95,94,92,94,91,90,90,77,95,95,94,96,94,97,148,93,77", "endOffsets": "196,291,391,473,570,676,752,828,919,1012,1110,1206,1301,1394,1489,1581,1672,1763,1841,1937,2033,2128,2225,2320,2418,2567,2661,2739"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,296,396,478,575,681,757,833,924,1017,1115,1211,1306,1399,1494,1586,1677,1768,1846,1942,2038,2133,2230,2325,2423,2572,13781", "endColumns": "95,94,99,81,96,105,75,75,90,92,97,95,94,92,94,91,90,90,77,95,95,94,96,94,97,148,93,77", "endOffsets": "196,291,391,473,570,676,752,828,919,1012,1110,1206,1301,1394,1489,1581,1672,1763,1841,1937,2033,2128,2225,2320,2418,2567,2661,13854"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2737,2829,2930,3024,3118,3211,3305,14224", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "2824,2925,3019,3113,3206,3300,3396,14320"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,254,330,415,506,583,657,734,812,887,960,1035,1103,1184,1257,1329,1400,1473,1539", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "249,325,410,501,578,652,729,807,882,955,1030,1098,1179,1252,1324,1395,1468,1534,1650"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3401,3478,3554,3639,3730,3807,3881,13399,13477,13633,13706,13859,13927,14008,14081,14153,14325,14398,14464", "endColumns": "76,75,84,90,76,73,76,77,74,72,74,67,80,72,71,70,72,65,115", "endOffsets": "3473,3549,3634,3725,3802,3876,3953,13472,13547,13701,13776,13922,14003,14076,14148,14219,14393,14459,14575"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "13314", "endColumns": "84", "endOffsets": "13394"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,408,498,611,712,806,895,1005,1114,1208,1321,1424,1532,1639,1736,1823,1929,2016,2113,2210,2306,2395,2502,2595,2697,2798,2893,2992,3088,3192,3288,3390,3477,3557,3650,3780,3923", "endColumns": "108,102,90,89,112,100,93,88,109,108,93,112,102,107,106,96,86,105,86,96,96,95,88,106,92,101,100,94,98,95,103,95,101,86,79,92,129,142,80", "endOffsets": "209,312,403,493,606,707,801,890,1000,1109,1203,1316,1419,1527,1634,1731,1818,1924,2011,2108,2205,2301,2390,2497,2590,2692,2793,2888,2987,3083,3187,3283,3385,3472,3552,3645,3775,3918,3999"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3958,4067,4170,4261,4351,4464,4565,4659,4748,4858,4967,5061,5174,5277,5385,5492,5589,5676,5782,5869,5966,6063,6159,6248,6355,6448,6550,6651,6746,6845,6941,7045,7141,7243,7330,7410,7503,7633,13552", "endColumns": "108,102,90,89,112,100,93,88,109,108,93,112,102,107,106,96,86,105,86,96,96,95,88,106,92,101,100,94,98,95,103,95,101,86,79,92,129,142,80", "endOffsets": "4062,4165,4256,4346,4459,4560,4654,4743,4853,4962,5056,5169,5272,5380,5487,5584,5671,5777,5864,5961,6058,6154,6243,6350,6443,6545,6646,6741,6840,6936,7040,7136,7238,7325,7405,7498,7628,7771,13628"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-zh-rCN/values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,860,963,1078,1160,1256,1340,1429,1535,1649,1750,1860,1968,2076,2192,2299,2400,2504,2610,2695,2790,2895,3004,3094,3192,3290,3400,3515,3615,3706,3779,3869,3958,4051,4134,4216,4308,4388,4470,4568,4662,4755,4850,4934,5030,5126,5223,5331,5411,5503", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "154,257,361,463,555,643,747,855,958,1073,1155,1251,1335,1424,1530,1644,1745,1855,1963,2071,2187,2294,2395,2499,2605,2690,2785,2890,2999,3089,3187,3285,3395,3510,3610,3701,3774,3864,3953,4046,4129,4211,4303,4383,4465,4563,4657,4750,4845,4929,5025,5121,5218,5326,5406,5498,5588"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7776,7880,7983,8087,8189,8281,8369,8473,8581,8684,8799,8881,8977,9061,9150,9256,9370,9471,9581,9689,9797,9913,10020,10121,10225,10331,10416,10511,10616,10725,10815,10913,11011,11121,11236,11336,11427,11500,11590,11679,11772,11855,11937,12029,12109,12191,12289,12383,12476,12571,12655,12751,12847,12944,13052,13132,13224", "endColumns": "103,102,103,101,91,87,103,107,102,114,81,95,83,88,105,113,100,109,107,107,115,106,100,103,105,84,94,104,108,89,97,97,109,114,99,90,72,89,88,92,82,81,91,79,81,97,93,92,94,83,95,95,96,107,79,91,89", "endOffsets": "7875,7978,8082,8184,8276,8364,8468,8576,8679,8794,8876,8972,9056,9145,9251,9365,9466,9576,9684,9792,9908,10015,10116,10220,10326,10411,10506,10611,10720,10810,10908,11006,11116,11231,11331,11422,11495,11585,11674,11767,11850,11932,12024,12104,12186,12284,12378,12471,12566,12650,12746,12842,12939,13047,13127,13219,13309"}}]}]}