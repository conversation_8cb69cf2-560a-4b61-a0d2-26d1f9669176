{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-ms/values-ms.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,294,408,527,627,732,854,1004,1132,1280,1366,1466,1558,1656,1772,1898,2003,2141,2276,2408,2587,2712,2837,2965,3094,3187,3288,3409,3537,3638,3745,3851,3992,4138,4245,4344,4420,4518,4616,4718,4805,4894,4996,5076,5159,5258,5357,5454,5557,5644,5747,5846,5953,6075,6156,6262", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "170,289,403,522,622,727,849,999,1127,1275,1361,1461,1553,1651,1767,1893,1998,2136,2271,2403,2582,2707,2832,2960,3089,3182,3283,3404,3532,3633,3740,3846,3987,4133,4240,4339,4415,4513,4611,4713,4800,4889,4991,5071,5154,5253,5352,5449,5552,5639,5742,5841,5948,6070,6151,6257,6353"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8461,8581,8700,8814,8933,9033,9138,9260,9410,9538,9686,9772,9872,9964,10062,10178,10304,10409,10547,10682,10814,10993,11118,11243,11371,11500,11593,11694,11815,11943,12044,12151,12257,12398,12544,12651,12750,12826,12924,13022,13124,13211,13300,13402,13482,13565,13664,13763,13860,13963,14050,14153,14252,14359,14481,14562,14668", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "8576,8695,8809,8928,9028,9133,9255,9405,9533,9681,9767,9867,9959,10057,10173,10299,10404,10542,10677,10809,10988,11113,11238,11366,11495,11588,11689,11810,11938,12039,12146,12252,12393,12539,12646,12745,12821,12919,13017,13119,13206,13295,13397,13477,13560,13659,13758,13855,13958,14045,14148,14247,14354,14476,14557,14663,14759"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-ms/values-ms.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "184,274,358,454,556,641,724,819,906,991,1076,1162,1234,1321,1398,1471,1544,1620,1686", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,86,76,72,72,75,65,119", "endOffsets": "269,353,449,551,636,719,814,901,986,1071,1157,1229,1316,1393,1466,1539,1615,1681,1801"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3622,3712,3796,3892,3994,4079,4162,14855,14942,15119,15204,15370,15442,15529,15606,15679,15853,15929,15995", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,86,76,72,72,75,65,119", "endOffsets": "3707,3791,3887,3989,4074,4157,4252,14937,15022,15199,15285,15437,15524,15601,15674,15747,15924,15990,16110"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,220", "endColumns": "77,86,90", "endOffsets": "128,215,306"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2801,16115,16202", "endColumns": "77,86,90", "endOffsets": "2874,16197,16288"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,505,626,736,838,930,1062,1191,1294,1415,1568,1693,1815,1921,2010,2139,2228,2330,2434,2534,2627,2737,2848,2960,3069,3177,3289,3404,3523,3630,3741,3828,3910,4021,4154,4309", "endColumns": "106,100,96,94,120,109,101,91,131,128,102,120,152,124,121,105,88,128,88,101,103,99,92,109,110,111,108,107,111,114,118,106,110,86,81,110,132,154,91", "endOffsets": "207,308,405,500,621,731,833,925,1057,1186,1289,1410,1563,1688,1810,1916,2005,2134,2223,2325,2429,2529,2622,2732,2843,2955,3064,3172,3284,3399,3518,3625,3736,3823,3905,4016,4149,4304,4396"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4257,4364,4465,4562,4657,4778,4888,4990,5082,5214,5343,5446,5567,5720,5845,5967,6073,6162,6291,6380,6482,6586,6686,6779,6889,7000,7112,7221,7329,7441,7556,7675,7782,7893,7980,8062,8173,8306,15027", "endColumns": "106,100,96,94,120,109,101,91,131,128,102,120,152,124,121,105,88,128,88,101,103,99,92,109,110,111,108,107,111,114,118,106,110,86,81,110,132,154,91", "endOffsets": "4359,4460,4557,4652,4773,4883,4985,5077,5209,5338,5441,5562,5715,5840,5962,6068,6157,6286,6375,6477,6581,6681,6774,6884,6995,7107,7216,7324,7436,7551,7670,7777,7888,7975,8057,8168,8301,8456,15114"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,809,888,979,1072,1167,1261,1360,1453,1548,1642,1733,1824,1904,2016,2125,2222,2331,2434,2541,2700,2801", "endColumns": "110,104,107,86,103,110,77,78,90,92,94,93,98,92,94,93,90,90,79,111,108,96,108,102,106,158,100,79", "endOffsets": "211,316,424,511,615,726,804,883,974,1067,1162,1256,1355,1448,1543,1637,1728,1819,1899,2011,2120,2217,2326,2429,2536,2695,2796,2876"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,809,888,979,1072,1167,1261,1360,1453,1548,1642,1733,1824,1904,2016,2125,2222,2331,2434,2541,2700,15290", "endColumns": "110,104,107,86,103,110,77,78,90,92,94,93,98,92,94,93,90,90,79,111,108,96,108,102,106,158,100,79", "endOffsets": "211,316,424,511,615,726,804,883,974,1067,1162,1256,1355,1448,1543,1637,1728,1819,1899,2011,2120,2217,2326,2429,2536,2695,2796,15365"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-ms/values-ms.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14764", "endColumns": "90", "endOffsets": "14850"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-ms/values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2879,2974,3076,3173,3283,3389,3507,15752", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "2969,3071,3168,3278,3384,3502,3617,15848"}}]}]}