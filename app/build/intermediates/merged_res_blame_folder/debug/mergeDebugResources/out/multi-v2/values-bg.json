{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-bg/values-bg.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-bg/values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,432,551,648,744,857,987,1108,1255,1339,1438,1534,1630,1743,1872,1976,2119,2262,2407,2595,2735,2862,2992,3126,3223,3320,3457,3592,3695,3800,3905,4050,4200,4308,4411,4498,4590,4685,4798,4895,4985,5094,5174,5257,5357,5459,5555,5653,5741,5848,5948,6052,6171,6251,6361", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,112,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "169,290,427,546,643,739,852,982,1103,1250,1334,1433,1529,1625,1738,1867,1971,2114,2257,2402,2590,2730,2857,2987,3121,3218,3315,3452,3587,3690,3795,3900,4045,4195,4303,4406,4493,4585,4680,4793,4890,4980,5089,5169,5252,5352,5454,5550,5648,5736,5843,5943,6047,6166,6246,6356,6453"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8750,8869,8990,9127,9246,9343,9439,9552,9682,9803,9950,10034,10133,10229,10325,10438,10567,10671,10814,10957,11102,11290,11430,11557,11687,11821,11918,12015,12152,12287,12390,12495,12600,12745,12895,13003,13106,13193,13285,13380,13493,13590,13680,13789,13869,13952,14052,14154,14250,14348,14436,14543,14643,14747,14866,14946,15056", "endColumns": "118,120,136,118,96,95,112,129,120,146,83,98,95,95,112,128,103,142,142,144,187,139,126,129,133,96,96,136,134,102,104,104,144,149,107,102,86,91,94,112,96,89,108,79,82,99,101,95,97,87,106,99,103,118,79,109,96", "endOffsets": "8864,8985,9122,9241,9338,9434,9547,9677,9798,9945,10029,10128,10224,10320,10433,10562,10666,10809,10952,11097,11285,11425,11552,11682,11816,11913,12010,12147,12282,12385,12490,12595,12740,12890,12998,13101,13188,13280,13375,13488,13585,13675,13784,13864,13947,14047,14149,14245,14343,14431,14538,14638,14742,14861,14941,15051,15148"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-bg/values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,500,637,751,849,946,1078,1207,1316,1455,1620,1754,1885,1990,2093,2233,2333,2443,2548,2663,2766,2898,3020,3145,3267,3378,3493,3609,3735,3846,3967,4054,4139,4238,4375,4526", "endColumns": "106,100,94,91,136,113,97,96,131,128,108,138,164,133,130,104,102,139,99,109,104,114,102,131,121,124,121,110,114,115,125,110,120,86,84,98,136,150,95", "endOffsets": "207,308,403,495,632,746,844,941,1073,1202,1311,1450,1615,1749,1880,1985,2088,2228,2328,2438,2543,2658,2761,2893,3015,3140,3262,3373,3488,3604,3730,3841,3962,4049,4134,4233,4370,4521,4617"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4329,4436,4537,4632,4724,4861,4975,5073,5170,5302,5431,5540,5679,5844,5978,6109,6214,6317,6457,6557,6667,6772,6887,6990,7122,7244,7369,7491,7602,7717,7833,7959,8070,8191,8278,8363,8462,8599,15417", "endColumns": "106,100,94,91,136,113,97,96,131,128,108,138,164,133,130,104,102,139,99,109,104,114,102,131,121,124,121,110,114,115,125,110,120,86,84,98,136,150,95", "endOffsets": "4431,4532,4627,4719,4856,4970,5068,5165,5297,5426,5535,5674,5839,5973,6104,6209,6312,6452,6552,6662,6767,6882,6985,7117,7239,7364,7486,7597,7712,7828,7954,8065,8186,8273,8358,8457,8594,8745,15508"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-bg/values-bg.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,143,231", "endColumns": "87,87,94", "endOffsets": "138,226,321"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2847,16530,16618", "endColumns": "87,87,94", "endOffsets": "2930,16613,16708"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-bg/values-bg.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "194,297,390,493,596,680,756,847,938,1022,1106,1194,1266,1351,1428,1506,1582,1665,1734", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,84,76,77,75,82,68,120", "endOffsets": "292,385,488,591,675,751,842,933,1017,1101,1189,1261,1346,1423,1501,1577,1660,1729,1850"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3676,3779,3872,3975,4078,4162,4238,15242,15333,15513,15597,15768,15840,15925,16002,16080,16257,16340,16409", "endColumns": "102,92,102,102,83,75,90,90,83,83,87,71,84,76,77,75,82,68,120", "endOffsets": "3774,3867,3970,4073,4157,4233,4324,15328,15412,15592,15680,15835,15920,15997,16075,16151,16335,16404,16525"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-bg/values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,327,432,518,623,744,823,901,992,1085,1181,1275,1376,1469,1564,1672,1763,1854,1937,2051,2160,2260,2374,2480,2588,2748,2847", "endColumns": "114,106,104,85,104,120,78,77,90,92,95,93,100,92,94,107,90,90,82,113,108,99,113,105,107,159,98,82", "endOffsets": "215,322,427,513,618,739,818,896,987,1080,1176,1270,1371,1464,1559,1667,1758,1849,1932,2046,2155,2255,2369,2475,2583,2743,2842,2925"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,327,432,518,623,744,823,901,992,1085,1181,1275,1376,1469,1564,1672,1763,1854,1937,2051,2160,2260,2374,2480,2588,2748,15685", "endColumns": "114,106,104,85,104,120,78,77,90,92,95,93,100,92,94,107,90,90,82,113,108,99,113,105,107,159,98,82", "endOffsets": "215,322,427,513,618,739,818,896,987,1080,1176,1270,1371,1464,1559,1667,1758,1849,1932,2046,2155,2255,2369,2475,2583,2743,2842,15763"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-bg/values-bg.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15153", "endColumns": "88", "endOffsets": "15237"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-bg/values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2935,3032,3142,3244,3345,3452,3557,16156", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3027,3137,3239,3340,3447,3552,3671,16252"}}]}]}