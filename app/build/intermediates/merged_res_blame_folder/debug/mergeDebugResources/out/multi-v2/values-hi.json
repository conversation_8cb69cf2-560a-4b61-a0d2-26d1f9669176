{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-hi/values-hi.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2901,2999,3102,3207,3308,3421,3527,15947", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "2994,3097,3202,3303,3416,3522,3649,16043"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-hi/values-hi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "89", "endOffsets": "140"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14937", "endColumns": "89", "endOffsets": "15022"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,508,630,742,842,936,1068,1200,1311,1440,1594,1738,1882,1989,2079,2210,2300,2405,2510,2614,2713,2838,2947,3071,3195,3297,3404,3534,3655,3781,3903,3990,4073,4169,4304,4458", "endColumns": "106,100,97,96,121,111,99,93,131,131,110,128,153,143,143,106,89,130,89,104,104,103,98,124,108,123,123,101,106,129,120,125,121,86,82,95,134,153,97", "endOffsets": "207,308,406,503,625,737,837,931,1063,1195,1306,1435,1589,1733,1877,1984,2074,2205,2295,2400,2505,2609,2708,2833,2942,3066,3190,3292,3399,3529,3650,3776,3898,3985,4068,4164,4299,4453,4551"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4287,4394,4495,4593,4690,4812,4924,5024,5118,5250,5382,5493,5622,5776,5920,6064,6171,6261,6392,6482,6587,6692,6796,6895,7020,7129,7253,7377,7479,7586,7716,7837,7963,8085,8172,8255,8351,8486,15201", "endColumns": "106,100,97,96,121,111,99,93,131,131,110,128,153,143,143,106,89,130,89,104,104,103,98,124,108,123,123,101,106,129,120,125,121,86,82,95,134,153,97", "endOffsets": "4389,4490,4588,4685,4807,4919,5019,5113,5245,5377,5488,5617,5771,5915,6059,6166,6256,6387,6477,6582,6687,6791,6890,7015,7124,7248,7372,7474,7581,7711,7832,7958,8080,8167,8250,8346,8481,8635,15294"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,415,531,626,722,835,973,1093,1243,1328,1431,1522,1619,1749,1869,1977,2122,2268,2398,2587,2714,2832,2954,3080,3172,3267,3395,3521,3620,3722,3834,3980,4132,4246,4346,4422,4522,4621,4731,4817,4907,5012,5092,5176,5276,5376,5471,5573,5659,5761,5859,5963,6078,6158,6258", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,109,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "168,286,410,526,621,717,830,968,1088,1238,1323,1426,1517,1614,1744,1864,1972,2117,2263,2393,2582,2709,2827,2949,3075,3167,3262,3390,3516,3615,3717,3829,3975,4127,4241,4341,4417,4517,4616,4726,4812,4902,5007,5087,5171,5271,5371,5466,5568,5654,5756,5854,5958,6073,6153,6253,6347"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8640,8758,8876,9000,9116,9211,9307,9420,9558,9678,9828,9913,10016,10107,10204,10334,10454,10562,10707,10853,10983,11172,11299,11417,11539,11665,11757,11852,11980,12106,12205,12307,12419,12565,12717,12831,12931,13007,13107,13206,13316,13402,13492,13597,13677,13761,13861,13961,14056,14158,14244,14346,14444,14548,14663,14743,14843", "endColumns": "117,117,123,115,94,95,112,137,119,149,84,102,90,96,129,119,107,144,145,129,188,126,117,121,125,91,94,127,125,98,101,111,145,151,113,99,75,99,98,109,85,89,104,79,83,99,99,94,101,85,101,97,103,114,79,99,93", "endOffsets": "8753,8871,8995,9111,9206,9302,9415,9553,9673,9823,9908,10011,10102,10199,10329,10449,10557,10702,10848,10978,11167,11294,11412,11534,11660,11752,11847,11975,12101,12200,12302,12414,12560,12712,12826,12926,13002,13102,13201,13311,13397,13487,13592,13672,13756,13856,13956,14051,14153,14239,14341,14439,14543,14658,14738,14838,14932"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,308,418,504,606,728,805,883,974,1067,1163,1257,1358,1451,1546,1640,1731,1822,1912,2021,2124,2226,2336,2437,2549,2711,2812", "endColumns": "105,96,109,85,101,121,76,77,90,92,95,93,100,92,94,93,90,90,89,108,102,101,109,100,111,161,100,79", "endOffsets": "206,303,413,499,601,723,800,878,969,1062,1158,1252,1353,1446,1541,1635,1726,1817,1907,2016,2119,2221,2331,2432,2544,2706,2807,2887"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,308,418,504,606,728,805,883,974,1067,1163,1257,1358,1451,1546,1640,1731,1822,1912,2021,2124,2226,2336,2437,2549,2711,15465", "endColumns": "105,96,109,85,101,121,76,77,90,92,95,93,100,92,94,93,90,90,89,108,102,101,109,100,111,161,100,79", "endOffsets": "206,303,413,499,601,723,800,878,969,1062,1158,1252,1353,1446,1541,1635,1726,1817,1907,2016,2119,2221,2331,2432,2544,2706,2807,15540"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-hi/values-hi.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "195,290,373,466,564,653,731,828,917,1002,1083,1168,1241,1327,1420,1495,1570,1651,1717", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,85,92,74,74,80,65,119", "endOffsets": "285,368,461,559,648,726,823,912,997,1078,1163,1236,1322,1415,1490,1565,1646,1712,1832"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3654,3749,3832,3925,4023,4112,4190,15027,15116,15299,15380,15545,15618,15704,15797,15872,16048,16129,16195", "endColumns": "94,82,92,97,88,77,96,88,84,80,84,72,85,92,74,74,80,65,119", "endOffsets": "3744,3827,3920,4018,4107,4185,4282,15111,15196,15375,15460,15613,15699,15792,15867,15942,16124,16190,16310"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-hi/values-hi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,144,229", "endColumns": "88,84,85", "endOffsets": "139,224,310"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2812,16315,16400", "endColumns": "88,84,85", "endOffsets": "2896,16395,16481"}}]}]}