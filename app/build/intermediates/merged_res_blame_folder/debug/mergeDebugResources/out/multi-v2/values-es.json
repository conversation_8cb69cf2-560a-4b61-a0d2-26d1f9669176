{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-es/values-es.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-es/values-es.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,282,364,462,565,654,733,826,918,1005,1091,1182,1259,1344,1420,1500,1576,1658,1728", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,84,75,79,75,81,69,120", "endOffsets": "277,359,457,560,649,728,821,913,1000,1086,1177,1254,1339,1415,1495,1571,1653,1723,1844"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3654,3750,3832,3930,4033,4122,4201,15088,15180,15357,15443,15616,15693,15778,15854,15934,16111,16193,16263", "endColumns": "95,81,97,102,88,78,92,91,86,85,90,76,84,75,79,75,81,69,120", "endOffsets": "3745,3827,3925,4028,4117,4196,4289,15175,15262,15438,15529,15688,15773,15849,15929,16005,16188,16258,16379"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,418,539,639,739,856,999,1117,1265,1350,1457,1554,1656,1770,1888,2000,2138,2275,2419,2588,2724,2844,2966,3096,3194,3290,3411,3546,3649,3763,3878,4015,4156,4267,4372,4459,4555,4651,4767,4854,4940,5051,5134,5218,5319,5425,5525,5628,5717,5828,5929,6038,6157,6240,6357", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,115,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "171,290,413,534,634,734,851,994,1112,1260,1345,1452,1549,1651,1765,1883,1995,2133,2270,2414,2583,2719,2839,2961,3091,3189,3285,3406,3541,3644,3758,3873,4010,4151,4262,4367,4454,4550,4646,4762,4849,4935,5046,5129,5213,5314,5420,5520,5623,5712,5823,5924,6033,6152,6235,6352,6461"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8588,8709,8828,8951,9072,9172,9272,9389,9532,9650,9798,9883,9990,10087,10189,10303,10421,10533,10671,10808,10952,11121,11257,11377,11499,11629,11727,11823,11944,12079,12182,12296,12411,12548,12689,12800,12905,12992,13088,13184,13300,13387,13473,13584,13667,13751,13852,13958,14058,14161,14250,14361,14462,14571,14690,14773,14890", "endColumns": "120,118,122,120,99,99,116,142,117,147,84,106,96,101,113,117,111,137,136,143,168,135,119,121,129,97,95,120,134,102,113,114,136,140,110,104,86,95,95,115,86,85,110,82,83,100,105,99,102,88,110,100,108,118,82,116,108", "endOffsets": "8704,8823,8946,9067,9167,9267,9384,9527,9645,9793,9878,9985,10082,10184,10298,10416,10528,10666,10803,10947,11116,11252,11372,11494,11624,11722,11818,11939,12074,12177,12291,12406,12543,12684,12795,12900,12987,13083,13179,13295,13382,13468,13579,13662,13746,13847,13953,14053,14156,14245,14356,14457,14566,14685,14768,14885,14994"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-es/values-es.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,235", "endColumns": "79,99,101", "endOffsets": "130,230,332"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2842,16384,16484", "endColumns": "79,99,101", "endOffsets": "2917,16479,16581"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,335,443,528,629,757,842,924,1016,1110,1208,1302,1403,1497,1593,1689,1781,1873,1955,2062,2162,2261,2369,2476,2583,2742,2842", "endColumns": "116,112,107,84,100,127,84,81,91,93,97,93,100,93,95,95,91,91,81,106,99,98,107,106,106,158,99,81", "endOffsets": "217,330,438,523,624,752,837,919,1011,1105,1203,1297,1398,1492,1588,1684,1776,1868,1950,2057,2157,2256,2364,2471,2578,2737,2837,2919"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,335,443,528,629,757,842,924,1016,1110,1208,1302,1403,1497,1593,1689,1781,1873,1955,2062,2162,2261,2369,2476,2583,2742,15534", "endColumns": "116,112,107,84,100,127,84,81,91,93,97,93,100,93,95,95,91,91,81,106,99,98,107,106,106,158,99,81", "endOffsets": "217,330,438,523,624,752,837,919,1011,1105,1203,1297,1398,1492,1588,1684,1776,1868,1950,2057,2157,2256,2364,2471,2578,2737,2837,15611"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-es/values-es.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14999", "endColumns": "88", "endOffsets": "15083"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,508,635,750,851,943,1071,1196,1307,1436,1586,1711,1833,1938,2029,2157,2252,2355,2457,2558,2654,2767,2883,3016,3146,3252,3361,3478,3600,3712,3829,3916,4010,4112,4246,4399", "endColumns": "106,100,97,96,126,114,100,91,127,124,110,128,149,124,121,104,90,127,94,102,101,100,95,112,115,132,129,105,108,116,121,111,116,86,93,101,133,152,89", "endOffsets": "207,308,406,503,630,745,846,938,1066,1191,1302,1431,1581,1706,1828,1933,2024,2152,2247,2350,2452,2553,2649,2762,2878,3011,3141,3247,3356,3473,3595,3707,3824,3911,4005,4107,4241,4394,4484"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4294,4401,4502,4600,4697,4824,4939,5040,5132,5260,5385,5496,5625,5775,5900,6022,6127,6218,6346,6441,6544,6646,6747,6843,6956,7072,7205,7335,7441,7550,7667,7789,7901,8018,8105,8199,8301,8435,15267", "endColumns": "106,100,97,96,126,114,100,91,127,124,110,128,149,124,121,104,90,127,94,102,101,100,95,112,115,132,129,105,108,116,121,111,116,86,93,101,133,152,89", "endOffsets": "4396,4497,4595,4692,4819,4934,5035,5127,5255,5380,5491,5620,5770,5895,6017,6122,6213,6341,6436,6539,6641,6742,6838,6951,7067,7200,7330,7436,7545,7662,7784,7896,8013,8100,8194,8296,8430,8583,15352"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-es/values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2922,3021,3123,3223,3321,3428,3534,16010", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3016,3118,3218,3316,3423,3529,3649,16106"}}]}]}