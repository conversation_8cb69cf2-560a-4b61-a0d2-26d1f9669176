{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-zu/values-zu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-zu/values-zu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,150,241", "endColumns": "94,90,91", "endOffsets": "145,236,328"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2817,16401,16492", "endColumns": "94,90,91", "endOffsets": "2907,16487,16579"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-zu/values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2912,3010,3114,3213,3316,3422,3529,16027", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "3005,3109,3208,3311,3417,3524,3637,16123"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-zu/values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,407,524,627,725,840,977,1094,1249,1334,1434,1526,1627,1747,1869,1974,2118,2253,2390,2562,2694,2820,2945,3073,3166,3266,3394,3536,3635,3737,3846,3986,4127,4237,4339,4417,4512,4609,4717,4803,4889,4995,5075,5160,5268,5370,5474,5572,5660,5766,5872,5974,6096,6176,6283", "endColumns": "117,118,114,116,102,97,114,136,116,154,84,99,91,100,119,121,104,143,134,136,171,131,125,124,127,92,99,127,141,98,101,108,139,140,109,101,77,94,96,107,85,85,105,79,84,107,101,103,97,87,105,105,101,121,79,106,99", "endOffsets": "168,287,402,519,622,720,835,972,1089,1244,1329,1429,1521,1622,1742,1864,1969,2113,2248,2385,2557,2689,2815,2940,3068,3161,3261,3389,3531,3630,3732,3841,3981,4122,4232,4334,4412,4507,4604,4712,4798,4884,4990,5070,5155,5263,5365,5469,5567,5655,5761,5867,5969,6091,6171,6278,6378"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8683,8801,8920,9035,9152,9255,9353,9468,9605,9722,9877,9962,10062,10154,10255,10375,10497,10602,10746,10881,11018,11190,11322,11448,11573,11701,11794,11894,12022,12164,12263,12365,12474,12614,12755,12865,12967,13045,13140,13237,13345,13431,13517,13623,13703,13788,13896,13998,14102,14200,14288,14394,14500,14602,14724,14804,14911", "endColumns": "117,118,114,116,102,97,114,136,116,154,84,99,91,100,119,121,104,143,134,136,171,131,125,124,127,92,99,127,141,98,101,108,139,140,109,101,77,94,96,107,85,85,105,79,84,107,101,103,97,87,105,105,101,121,79,106,99", "endOffsets": "8796,8915,9030,9147,9250,9348,9463,9600,9717,9872,9957,10057,10149,10250,10370,10492,10597,10741,10876,11013,11185,11317,11443,11568,11696,11789,11889,12017,12159,12258,12360,12469,12609,12750,12860,12962,13040,13135,13232,13340,13426,13512,13618,13698,13783,13891,13993,14097,14195,14283,14389,14495,14597,14719,14799,14906,15006"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-zu/values-zu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "92", "endOffsets": "143"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15011", "endColumns": "92", "endOffsets": "15099"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-zu/values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,501,645,761,865,956,1098,1244,1360,1500,1651,1779,1911,2019,2120,2256,2346,2449,2557,2661,2762,2880,2995,3107,3223,3330,3445,3569,3693,3816,3929,4016,4099,4204,4340,4498", "endColumns": "106,100,95,91,143,115,103,90,141,145,115,139,150,127,131,107,100,135,89,102,107,103,100,117,114,111,115,106,114,123,123,122,112,86,82,104,135,157,92", "endOffsets": "207,308,404,496,640,756,860,951,1093,1239,1355,1495,1646,1774,1906,2014,2115,2251,2341,2444,2552,2656,2757,2875,2990,3102,3218,3325,3440,3564,3688,3811,3924,4011,4094,4199,4335,4493,4586"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4290,4397,4498,4594,4686,4830,4946,5050,5141,5283,5429,5545,5685,5836,5964,6096,6204,6305,6441,6531,6634,6742,6846,6947,7065,7180,7292,7408,7515,7630,7754,7878,8001,8114,8201,8284,8389,8525,15280", "endColumns": "106,100,95,91,143,115,103,90,141,145,115,139,150,127,131,107,100,135,89,102,107,103,100,117,114,111,115,106,114,123,123,122,112,86,82,104,135,157,92", "endOffsets": "4392,4493,4589,4681,4825,4941,5045,5136,5278,5424,5540,5680,5831,5959,6091,6199,6300,6436,6526,6629,6737,6841,6942,7060,7175,7287,7403,7510,7625,7749,7873,7996,8109,8196,8279,8384,8520,8678,15368"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-zu/values-zu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "201,296,378,483,588,678,760,849,942,1025,1113,1201,1277,1366,1447,1523,1598,1677,1747", "endColumns": "94,81,104,104,89,81,88,92,82,87,87,75,88,80,75,74,78,69,123", "endOffsets": "291,373,478,583,673,755,844,937,1020,1108,1196,1272,1361,1442,1518,1593,1672,1742,1866"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3642,3737,3819,3924,4029,4119,4201,15104,15197,15373,15461,15630,15706,15795,15876,15952,16128,16207,16277", "endColumns": "94,81,104,104,89,81,88,92,82,87,87,75,88,80,75,74,78,69,123", "endOffsets": "3732,3814,3919,4024,4114,4196,4285,15192,15275,15456,15544,15701,15790,15871,15947,16022,16202,16272,16396"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-zu/values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,434,522,625,752,832,912,1003,1096,1190,1284,1385,1478,1573,1667,1758,1851,1937,2041,2147,2245,2352,2458,2564,2721,2817", "endColumns": "107,106,113,87,102,126,79,79,90,92,93,93,100,92,94,93,90,92,85,103,105,97,106,105,105,156,95,80", "endOffsets": "208,315,429,517,620,747,827,907,998,1091,1185,1279,1380,1473,1568,1662,1753,1846,1932,2036,2142,2240,2347,2453,2559,2716,2812,2893"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,434,522,625,752,832,912,1003,1096,1190,1284,1385,1478,1573,1667,1758,1851,1937,2041,2147,2245,2352,2458,2564,2721,15549", "endColumns": "107,106,113,87,102,126,79,79,90,92,93,93,100,92,94,93,90,92,85,103,105,97,106,105,105,156,95,80", "endOffsets": "208,315,429,517,620,747,827,907,998,1091,1185,1279,1380,1473,1568,1662,1753,1846,1932,2036,2142,2240,2347,2453,2559,2716,2812,15625"}}]}]}