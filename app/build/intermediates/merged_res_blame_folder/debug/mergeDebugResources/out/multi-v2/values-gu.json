{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-gu/values-gu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-gu/values-gu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "200,292,374,467,566,653,739,840,927,1013,1096,1179,1254,1338,1413,1488,1563,1639,1705", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,83,74,74,74,75,65,115", "endOffsets": "287,369,462,561,648,734,835,922,1008,1091,1174,1249,1333,1408,1483,1558,1634,1700,1816"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3618,3710,3792,3885,3984,4071,4157,14825,14912,15089,15172,15335,15410,15494,15569,15644,15820,15896,15962", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,83,74,74,74,75,65,115", "endOffsets": "3705,3787,3880,3979,4066,4152,4253,14907,14993,15167,15250,15405,15489,15564,15639,15714,15891,15957,16073"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,406,518,613,712,828,967,1087,1229,1314,1418,1512,1612,1726,1854,1963,2098,2230,2360,2539,2665,2787,2913,3048,3143,3239,3366,3496,3597,3702,3809,3944,4085,4194,4296,4371,4468,4564,4671,4756,4843,4941,5021,5105,5205,5308,5406,5506,5593,5699,5798,5901,6019,6099,6199", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,106,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "164,276,401,513,608,707,823,962,1082,1224,1309,1413,1507,1607,1721,1849,1958,2093,2225,2355,2534,2660,2782,2908,3043,3138,3234,3361,3491,3592,3697,3804,3939,4080,4189,4291,4366,4463,4559,4666,4751,4838,4936,5016,5100,5200,5303,5401,5501,5588,5694,5793,5896,6014,6094,6194,6288"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8499,8613,8725,8850,8962,9057,9156,9272,9411,9531,9673,9758,9862,9956,10056,10170,10298,10407,10542,10674,10804,10983,11109,11231,11357,11492,11587,11683,11810,11940,12041,12146,12253,12388,12529,12638,12740,12815,12912,13008,13115,13200,13287,13385,13465,13549,13649,13752,13850,13950,14037,14143,14242,14345,14463,14543,14643", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,106,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "8608,8720,8845,8957,9052,9151,9267,9406,9526,9668,9753,9857,9951,10051,10165,10293,10402,10537,10669,10799,10978,11104,11226,11352,11487,11582,11678,11805,11935,12036,12141,12248,12383,12524,12633,12735,12810,12907,13003,13110,13195,13282,13380,13460,13544,13644,13747,13845,13945,14032,14138,14237,14340,14458,14538,14638,14732"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,149,234", "endColumns": "93,84,84", "endOffsets": "144,229,314"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2802,16078,16163", "endColumns": "93,84,84", "endOffsets": "2891,16158,16243"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,425,512,613,736,813,891,982,1075,1174,1268,1369,1462,1557,1654,1745,1836,1916,2022,2124,2221,2330,2429,2539,2699,2802", "endColumns": "108,103,106,86,100,122,76,77,90,92,98,93,100,92,94,96,90,90,79,105,101,96,108,98,109,159,102,79", "endOffsets": "209,313,420,507,608,731,808,886,977,1070,1169,1263,1364,1457,1552,1649,1740,1831,1911,2017,2119,2216,2325,2424,2534,2694,2797,2877"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,318,425,512,613,736,813,891,982,1075,1174,1268,1369,1462,1557,1654,1745,1836,1916,2022,2124,2221,2330,2429,2539,2699,15255", "endColumns": "108,103,106,86,100,122,76,77,90,92,98,93,100,92,94,96,90,90,79,105,101,96,108,98,109,159,102,79", "endOffsets": "209,313,420,507,608,731,808,886,977,1070,1169,1263,1364,1457,1552,1649,1740,1831,1911,2017,2119,2216,2325,2424,2534,2694,2797,15330"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,353,455,557,655,777", "endColumns": "97,102,96,101,101,97,121,100", "endOffsets": "148,251,348,450,552,650,772,873"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2896,2994,3097,3194,3296,3398,3496,15719", "endColumns": "97,102,96,101,101,97,121,100", "endOffsets": "2989,3092,3189,3291,3393,3491,3613,15815"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-gu/values-gu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14737", "endColumns": "87", "endOffsets": "14820"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,507,628,738,833,929,1061,1193,1302,1432,1576,1698,1820,1924,2015,2145,2235,2341,2452,2561,2660,2779,2892,3005,3118,3220,3327,3442,3563,3674,3791,3878,3960,4060,4193,4346", "endColumns": "106,100,95,97,120,109,94,95,131,131,108,129,143,121,121,103,90,129,89,105,110,108,98,118,112,112,112,101,106,114,120,110,116,86,81,99,132,152,90", "endOffsets": "207,308,404,502,623,733,828,924,1056,1188,1297,1427,1571,1693,1815,1919,2010,2140,2230,2336,2447,2556,2655,2774,2887,3000,3113,3215,3322,3437,3558,3669,3786,3873,3955,4055,4188,4341,4432"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4258,4365,4466,4562,4660,4781,4891,4986,5082,5214,5346,5455,5585,5729,5851,5973,6077,6168,6298,6388,6494,6605,6714,6813,6932,7045,7158,7271,7373,7480,7595,7716,7827,7944,8031,8113,8213,8346,14998", "endColumns": "106,100,95,97,120,109,94,95,131,131,108,129,143,121,121,103,90,129,89,105,110,108,98,118,112,112,112,101,106,114,120,110,116,86,81,99,132,152,90", "endOffsets": "4360,4461,4557,4655,4776,4886,4981,5077,5209,5341,5450,5580,5724,5846,5968,6072,6163,6293,6383,6489,6600,6709,6808,6927,7040,7153,7266,7368,7475,7590,7711,7822,7939,8026,8108,8208,8341,8494,15084"}}]}]}