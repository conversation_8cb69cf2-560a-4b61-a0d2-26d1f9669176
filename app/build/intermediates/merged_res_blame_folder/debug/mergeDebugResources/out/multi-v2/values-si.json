{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-si/values-si.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,399,516,614,711,825,954,1074,1213,1297,1403,1494,1591,1705,1833,1944,2072,2198,2330,2503,2627,2744,2864,2985,3077,3172,3291,3412,3513,3616,3720,3851,3987,4094,4191,4267,4363,4461,4566,4652,4741,4835,4918,5001,5100,5200,5292,5393,5481,5592,5694,5806,5927,6009,6117", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,104,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "166,283,394,511,609,706,820,949,1069,1208,1292,1398,1489,1586,1700,1828,1939,2067,2193,2325,2498,2622,2739,2859,2980,3072,3167,3286,3407,3508,3611,3715,3846,3982,4089,4186,4262,4358,4456,4561,4647,4736,4830,4913,4996,5095,5195,5287,5388,5476,5587,5689,5801,5922,6004,6112,6211"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8563,8679,8796,8907,9024,9122,9219,9333,9462,9582,9721,9805,9911,10002,10099,10213,10341,10452,10580,10706,10838,11011,11135,11252,11372,11493,11585,11680,11799,11920,12021,12124,12228,12359,12495,12602,12699,12775,12871,12969,13074,13160,13249,13343,13426,13509,13608,13708,13800,13901,13989,14100,14202,14314,14435,14517,14625", "endColumns": "115,116,110,116,97,96,113,128,119,138,83,105,90,96,113,127,110,127,125,131,172,123,116,119,120,91,94,118,120,100,102,103,130,135,106,96,75,95,97,104,85,88,93,82,82,98,99,91,100,87,110,101,111,120,81,107,98", "endOffsets": "8674,8791,8902,9019,9117,9214,9328,9457,9577,9716,9800,9906,9997,10094,10208,10336,10447,10575,10701,10833,11006,11130,11247,11367,11488,11580,11675,11794,11915,12016,12119,12223,12354,12490,12597,12694,12770,12866,12964,13069,13155,13244,13338,13421,13504,13603,13703,13795,13896,13984,14095,14197,14309,14430,14512,14620,14719"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-si/values-si.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,274,357,456,555,637,722,813,899,979,1058,1140,1213,1297,1372,1456,1537,1618,1685", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,83,74,83,80,80,66,117", "endOffsets": "269,352,451,550,632,717,808,894,974,1053,1135,1208,1292,1367,1451,1532,1613,1680,1798"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3632,3721,3804,3903,4002,4084,4169,14812,14898,15070,15149,15313,15386,15470,15545,15629,15811,15892,15959", "endColumns": "88,82,98,98,81,84,90,85,79,78,81,72,83,74,83,80,80,66,117", "endOffsets": "3716,3799,3898,3997,4079,4164,4255,14893,14973,15144,15226,15381,15465,15540,15624,15705,15887,15954,16072"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-si/values-si.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,224", "endColumns": "78,89,92", "endOffsets": "129,219,312"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2821,16077,16167", "endColumns": "78,89,92", "endOffsets": "2895,16162,16255"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,322,429,517,622,738,827,914,1005,1098,1193,1287,1388,1481,1576,1670,1761,1852,1936,2045,2150,2248,2358,2457,2563,2722,2821", "endColumns": "109,106,106,87,104,115,88,86,90,92,94,93,100,92,94,93,90,90,83,108,104,97,109,98,105,158,98,81", "endOffsets": "210,317,424,512,617,733,822,909,1000,1093,1188,1282,1383,1476,1571,1665,1756,1847,1931,2040,2145,2243,2353,2452,2558,2717,2816,2898"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,322,429,517,622,738,827,914,1005,1098,1193,1287,1388,1481,1576,1670,1761,1852,1936,2045,2150,2248,2358,2457,2563,2722,15231", "endColumns": "109,106,106,87,104,115,88,86,90,92,94,93,100,92,94,93,90,90,83,108,104,97,109,98,105,158,98,81", "endOffsets": "210,317,424,512,617,733,822,909,1000,1093,1188,1282,1383,1476,1571,1665,1756,1847,1931,2040,2145,2243,2353,2452,2558,2717,2816,15308"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2900,3002,3105,3210,3315,3414,3518,15710", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "2997,3100,3205,3310,3409,3513,3627,15806"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-si/values-si.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14724", "endColumns": "87", "endOffsets": "14807"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-si/values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,414,513,629,738,833,927,1063,1198,1308,1439,1569,1693,1816,1925,2021,2162,2252,2360,2474,2579,2678,2794,2901,3011,3120,3226,3338,3464,3596,3720,3850,3937,4020,4121,4256,4408", "endColumns": "106,100,100,98,115,108,94,93,135,134,109,130,129,123,122,108,95,140,89,107,113,104,98,115,106,109,108,105,111,125,131,123,129,86,82,100,134,151,91", "endOffsets": "207,308,409,508,624,733,828,922,1058,1193,1303,1434,1564,1688,1811,1920,2016,2157,2247,2355,2469,2574,2673,2789,2896,3006,3115,3221,3333,3459,3591,3715,3845,3932,4015,4116,4251,4403,4495"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4260,4367,4468,4569,4668,4784,4893,4988,5082,5218,5353,5463,5594,5724,5848,5971,6080,6176,6317,6407,6515,6629,6734,6833,6949,7056,7166,7275,7381,7493,7619,7751,7875,8005,8092,8175,8276,8411,14978", "endColumns": "106,100,100,98,115,108,94,93,135,134,109,130,129,123,122,108,95,140,89,107,113,104,98,115,106,109,108,105,111,125,131,123,129,86,82,100,134,151,91", "endOffsets": "4362,4463,4564,4663,4779,4888,4983,5077,5213,5348,5458,5589,5719,5843,5966,6075,6171,6312,6402,6510,6624,6729,6828,6944,7051,7161,7270,7376,7488,7614,7746,7870,8000,8087,8170,8271,8406,8558,15065"}}]}]}