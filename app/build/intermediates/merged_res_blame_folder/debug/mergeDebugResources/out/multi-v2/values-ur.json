{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-ur/values-ur.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,128,213", "endColumns": "72,84,86", "endOffsets": "123,208,295"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2824,16284,16369", "endColumns": "72,84,86", "endOffsets": "2892,16364,16451"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2897,2995,3097,3199,3303,3406,3504,15925", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "2990,3092,3194,3298,3401,3499,3613,16021"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,321,430,516,620,740,816,892,984,1078,1174,1268,1370,1464,1560,1654,1746,1838,1923,2031,2140,2242,2353,2453,2561,2726,2824", "endColumns": "109,105,108,85,103,119,75,75,91,93,95,93,101,93,95,93,91,91,84,107,108,101,110,99,107,164,97,79", "endOffsets": "210,316,425,511,615,735,811,887,979,1073,1169,1263,1365,1459,1555,1649,1741,1833,1918,2026,2135,2237,2348,2448,2556,2721,2819,2899"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,321,430,516,620,740,816,892,984,1078,1174,1268,1370,1464,1560,1654,1746,1838,1923,2031,2140,2242,2353,2453,2561,2726,15462", "endColumns": "109,105,108,85,103,119,75,75,91,93,95,93,101,93,95,93,91,91,84,107,108,101,110,99,107,164,97,79", "endOffsets": "210,316,425,511,615,735,811,887,979,1073,1169,1263,1365,1459,1555,1649,1741,1833,1918,2026,2135,2237,2348,2448,2556,2721,2819,15537"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-ur/values-ur.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "179,273,356,446,543,631,712,805,893,979,1062,1147,1222,1305,1383,1457,1530,1605,1671", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,82,77,73,72,74,65,116", "endOffsets": "268,351,441,538,626,707,800,888,974,1057,1142,1217,1300,1378,1452,1525,1600,1666,1783"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3618,3712,3795,3885,3982,4070,4151,15027,15115,15294,15377,15542,15617,15700,15778,15852,16026,16101,16167", "endColumns": "93,82,89,96,87,80,92,87,85,82,84,74,82,77,73,72,74,65,116", "endOffsets": "3707,3790,3880,3977,4065,4146,4239,15110,15196,15372,15457,15612,15695,15773,15847,15920,16096,16162,16279"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,295,411,529,627,724,839,974,1098,1238,1323,1427,1523,1623,1740,1870,1979,2123,2266,2395,2593,2718,2837,2960,3098,3195,3290,3414,3538,3639,3744,3850,3993,4142,4248,4352,4428,4524,4621,4733,4823,4914,5029,5109,5194,5297,5403,5500,5603,5688,5794,5893,5996,6117,6197,6299", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "168,290,406,524,622,719,834,969,1093,1233,1318,1422,1518,1618,1735,1865,1974,2118,2261,2390,2588,2713,2832,2955,3093,3190,3285,3409,3533,3634,3739,3845,3988,4137,4243,4347,4423,4519,4616,4728,4818,4909,5024,5104,5189,5292,5398,5495,5598,5683,5789,5888,5991,6112,6192,6294,6388"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8602,8720,8842,8958,9076,9174,9271,9386,9521,9645,9785,9870,9974,10070,10170,10287,10417,10526,10670,10813,10942,11140,11265,11384,11507,11645,11742,11837,11961,12085,12186,12291,12397,12540,12689,12795,12899,12975,13071,13168,13280,13370,13461,13576,13656,13741,13844,13950,14047,14150,14235,14341,14440,14543,14664,14744,14846", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,111,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "8715,8837,8953,9071,9169,9266,9381,9516,9640,9780,9865,9969,10065,10165,10282,10412,10521,10665,10808,10937,11135,11260,11379,11502,11640,11737,11832,11956,12080,12181,12286,12392,12535,12684,12790,12894,12970,13066,13163,13275,13365,13456,13571,13651,13736,13839,13945,14042,14145,14230,14336,14435,14538,14659,14739,14841,14935"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-ur/values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,509,634,745,840,934,1063,1187,1298,1430,1567,1692,1812,1920,2015,2150,2241,2347,2456,2560,2660,2780,2888,3001,3109,3212,3320,3461,3606,3745,3888,3975,4062,4168,4306,4463", "endColumns": "106,100,98,96,124,110,94,93,128,123,110,131,136,124,119,107,94,134,90,105,108,103,99,119,107,112,107,102,107,140,144,138,142,86,86,105,137,156,92", "endOffsets": "207,308,407,504,629,740,835,929,1058,1182,1293,1425,1562,1687,1807,1915,2010,2145,2236,2342,2451,2555,2655,2775,2883,2996,3104,3207,3315,3456,3601,3740,3883,3970,4057,4163,4301,4458,4551"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4244,4351,4452,4551,4648,4773,4884,4979,5073,5202,5326,5437,5569,5706,5831,5951,6059,6154,6289,6380,6486,6595,6699,6799,6919,7027,7140,7248,7351,7459,7600,7745,7884,8027,8114,8201,8307,8445,15201", "endColumns": "106,100,98,96,124,110,94,93,128,123,110,131,136,124,119,107,94,134,90,105,108,103,99,119,107,112,107,102,107,140,144,138,142,86,86,105,137,156,92", "endOffsets": "4346,4447,4546,4643,4768,4879,4974,5068,5197,5321,5432,5564,5701,5826,5946,6054,6149,6284,6375,6481,6590,6694,6794,6914,7022,7135,7243,7346,7454,7595,7740,7879,8022,8109,8196,8302,8440,8597,15289"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-ur/values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14940", "endColumns": "86", "endOffsets": "15022"}}]}]}