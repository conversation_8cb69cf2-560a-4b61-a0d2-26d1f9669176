{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-sw/values-sw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,503,642,756,855,944,1062,1180,1291,1413,1557,1675,1793,1899,1991,2123,2213,2314,2421,2521,2621,2738,2854,2980,3106,3219,3339,3460,3582,3694,3807,3894,3978,4080,4215,4368", "endColumns": "106,100,95,93,138,113,98,88,117,117,110,121,143,117,117,105,91,131,89,100,106,99,99,116,115,125,125,112,119,120,121,111,112,86,83,101,134,152,96", "endOffsets": "207,308,404,498,637,751,850,939,1057,1175,1286,1408,1552,1670,1788,1894,1986,2118,2208,2309,2416,2516,2616,2733,2849,2975,3101,3214,3334,3455,3577,3689,3802,3889,3973,4075,4210,4363,4460"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4252,4359,4460,4556,4650,4789,4903,5002,5091,5209,5327,5438,5560,5704,5822,5940,6046,6138,6270,6360,6461,6568,6668,6768,6885,7001,7127,7253,7366,7486,7607,7729,7841,7954,8041,8125,8227,8362,15036", "endColumns": "106,100,95,93,138,113,98,88,117,117,110,121,143,117,117,105,91,131,89,100,106,99,99,116,115,125,125,112,119,120,121,111,112,86,83,101,134,152,96", "endOffsets": "4354,4455,4551,4645,4784,4898,4997,5086,5204,5322,5433,5555,5699,5817,5935,6041,6133,6265,6355,6456,6563,6663,6763,6880,6996,7122,7248,7361,7481,7602,7724,7836,7949,8036,8120,8222,8357,8510,15128"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,306,414,504,609,726,808,891,982,1075,1169,1263,1364,1457,1552,1647,1738,1830,1912,2013,2122,2221,2328,2437,2542,2704,2801", "endColumns": "102,97,107,89,104,116,81,82,90,92,93,93,100,92,94,94,90,91,81,100,108,98,106,108,104,161,96,81", "endOffsets": "203,301,409,499,604,721,803,886,977,1070,1164,1258,1359,1452,1547,1642,1733,1825,1907,2008,2117,2216,2323,2432,2537,2699,2796,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,306,414,504,609,726,808,891,982,1075,1169,1263,1364,1457,1552,1647,1738,1830,1912,2013,2122,2221,2328,2437,2542,2704,15305", "endColumns": "102,97,107,89,104,116,81,82,90,92,93,93,100,92,94,94,90,91,81,100,108,98,106,108,104,161,96,81", "endOffsets": "203,301,409,499,604,721,803,886,977,1070,1164,1258,1359,1452,1547,1642,1733,1825,1907,2008,2117,2216,2323,2432,2537,2699,2796,15382"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-sw/values-sw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14775", "endColumns": "87", "endOffsets": "14858"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,402,516,616,715,831,972,1088,1239,1325,1425,1518,1620,1738,1865,1970,2100,2229,2365,2530,2659,2783,2912,3021,3115,3211,3334,3462,3559,3671,3781,3913,4054,4166,4266,4345,4441,4538,4653,4740,4825,4939,5019,5102,5201,5301,5396,5495,5583,5688,5788,5891,6007,6087,6205", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,114,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "166,281,397,511,611,710,826,967,1083,1234,1320,1420,1513,1615,1733,1860,1965,2095,2224,2360,2525,2654,2778,2907,3016,3110,3206,3329,3457,3554,3666,3776,3908,4049,4161,4261,4340,4436,4533,4648,4735,4820,4934,5014,5097,5196,5296,5391,5490,5578,5683,5783,5886,6002,6082,6200,6310"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8515,8631,8746,8862,8976,9076,9175,9291,9432,9548,9699,9785,9885,9978,10080,10198,10325,10430,10560,10689,10825,10990,11119,11243,11372,11481,11575,11671,11794,11922,12019,12131,12241,12373,12514,12626,12726,12805,12901,12998,13113,13200,13285,13399,13479,13562,13661,13761,13856,13955,14043,14148,14248,14351,14467,14547,14665", "endColumns": "115,114,115,113,99,98,115,140,115,150,85,99,92,101,117,126,104,129,128,135,164,128,123,128,108,93,95,122,127,96,111,109,131,140,111,99,78,95,96,114,86,84,113,79,82,98,99,94,98,87,104,99,102,115,79,117,109", "endOffsets": "8626,8741,8857,8971,9071,9170,9286,9427,9543,9694,9780,9880,9973,10075,10193,10320,10425,10555,10684,10820,10985,11114,11238,11367,11476,11570,11666,11789,11917,12014,12126,12236,12368,12509,12621,12721,12800,12896,12993,13108,13195,13280,13394,13474,13557,13656,13756,13851,13950,14038,14143,14243,14346,14462,14542,14660,14770"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,138,239", "endColumns": "82,100,102", "endOffsets": "133,234,337"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2801,16143,16244", "endColumns": "82,100,102", "endOffsets": "2879,16239,16342"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-sw/values-sw.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "189,283,364,465,566,652,733,834,925,1007,1092,1179,1253,1337,1412,1489,1566,1643,1713", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,83,74,76,76,76,69,120", "endOffsets": "278,359,460,561,647,728,829,920,1002,1087,1174,1248,1332,1407,1484,1561,1638,1708,1829"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3607,3701,3782,3883,3984,4070,4151,14863,14954,15133,15218,15387,15461,15545,15620,15697,15875,15952,16022", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,83,74,76,76,76,69,120", "endOffsets": "3696,3777,3878,3979,4065,4146,4247,14949,15031,15213,15300,15456,15540,15615,15692,15769,15947,16017,16138"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2884,2978,3080,3177,3278,3385,3492,15774", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "2973,3075,3172,3273,3380,3487,3602,15870"}}]}]}