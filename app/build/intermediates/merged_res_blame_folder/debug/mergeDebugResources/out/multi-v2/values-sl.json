{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-sl/values-sl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,235", "endColumns": "89,89,90", "endOffsets": "140,230,321"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2853,16325,16415", "endColumns": "89,89,90", "endOffsets": "2938,16410,16501"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2943,3040,3142,3240,3344,3447,3549,15955", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3035,3137,3235,3339,3442,3544,3661,16051"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,319,427,514,617,736,821,905,997,1091,1187,1281,1377,1471,1567,1667,1759,1851,1935,2043,2152,2252,2365,2472,2576,2756,2853", "endColumns": "106,106,107,86,102,118,84,83,91,93,95,93,95,93,95,99,91,91,83,107,108,99,112,106,103,179,96,82", "endOffsets": "207,314,422,509,612,731,816,900,992,1086,1182,1276,1372,1466,1562,1662,1754,1846,1930,2038,2147,2247,2360,2467,2571,2751,2848,2931"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,319,427,514,617,736,821,905,997,1091,1187,1281,1377,1471,1567,1667,1759,1851,1935,2043,2152,2252,2365,2472,2576,2756,15477", "endColumns": "106,106,107,86,102,118,84,83,91,93,95,93,95,93,95,99,91,91,83,107,108,99,112,106,103,179,96,82", "endOffsets": "207,314,422,509,612,731,816,900,992,1086,1182,1276,1372,1466,1562,1662,1754,1846,1930,2038,2147,2247,2360,2467,2571,2751,2848,15555"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-sl/values-sl.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,291,372,468,566,651,728,815,907,989,1071,1157,1229,1317,1394,1474,1552,1630,1700", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,87,76,79,77,77,69,120", "endOffsets": "286,367,463,561,646,723,810,902,984,1066,1152,1224,1312,1389,1469,1547,1625,1695,1816"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3666,3761,3842,3938,4036,4121,4198,15043,15135,15309,15391,15560,15632,15720,15797,15877,16056,16134,16204", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,87,76,79,77,77,69,120", "endOffsets": "3756,3837,3933,4031,4116,4193,4280,15130,15212,15386,15472,15627,15715,15792,15872,15950,16129,16199,16320"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,184,313,431,560,670,766,879,1019,1145,1288,1373,1472,1565,1662,1779,1901,2005,2142,2276,2407,2591,2718,2841,2966,3088,3182,3280,3400,3524,3624,3733,3839,3982,4129,4238,4340,4424,4519,4615,4723,4811,4897,5000,5082,5165,5260,5360,5451,5548,5636,5740,5837,5939,6081,6163,6269", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "179,308,426,555,665,761,874,1014,1140,1283,1368,1467,1560,1657,1774,1896,2000,2137,2271,2402,2586,2713,2836,2961,3083,3177,3275,3395,3519,3619,3728,3834,3977,4124,4233,4335,4419,4514,4610,4718,4806,4892,4995,5077,5160,5255,5355,5446,5543,5631,5735,5832,5934,6076,6158,6264,6363"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8638,8767,8896,9014,9143,9253,9349,9462,9602,9728,9871,9956,10055,10148,10245,10362,10484,10588,10725,10859,10990,11174,11301,11424,11549,11671,11765,11863,11983,12107,12207,12316,12422,12565,12712,12821,12923,13007,13102,13198,13306,13394,13480,13583,13665,13748,13843,13943,14034,14131,14219,14323,14420,14522,14664,14746,14852", "endColumns": "128,128,117,128,109,95,112,139,125,142,84,98,92,96,116,121,103,136,133,130,183,126,122,124,121,93,97,119,123,99,108,105,142,146,108,101,83,94,95,107,87,85,102,81,82,94,99,90,96,87,103,96,101,141,81,105,98", "endOffsets": "8762,8891,9009,9138,9248,9344,9457,9597,9723,9866,9951,10050,10143,10240,10357,10479,10583,10720,10854,10985,11169,11296,11419,11544,11666,11760,11858,11978,12102,12202,12311,12417,12560,12707,12816,12918,13002,13097,13193,13301,13389,13475,13578,13660,13743,13838,13938,14029,14126,14214,14318,14415,14517,14659,14741,14847,14946"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-sl/values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,422,518,644,759,857,949,1074,1196,1301,1442,1598,1724,1847,1950,2049,2177,2271,2372,2480,2580,2679,2807,2931,3058,3182,3295,3411,3526,3641,3754,3867,3954,4039,4152,4289,4458", "endColumns": "112,106,96,95,125,114,97,91,124,121,104,140,155,125,122,102,98,127,93,100,107,99,98,127,123,126,123,112,115,114,114,112,112,86,84,112,136,168,91", "endOffsets": "213,320,417,513,639,754,852,944,1069,1191,1296,1437,1593,1719,1842,1945,2044,2172,2266,2367,2475,2575,2674,2802,2926,3053,3177,3290,3406,3521,3636,3749,3862,3949,4034,4147,4284,4453,4545"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4285,4398,4505,4602,4698,4824,4939,5037,5129,5254,5376,5481,5622,5778,5904,6027,6130,6229,6357,6451,6552,6660,6760,6859,6987,7111,7238,7362,7475,7591,7706,7821,7934,8047,8134,8219,8332,8469,15217", "endColumns": "112,106,96,95,125,114,97,91,124,121,104,140,155,125,122,102,98,127,93,100,107,99,98,127,123,126,123,112,115,114,114,112,112,86,84,112,136,168,91", "endOffsets": "4393,4500,4597,4693,4819,4934,5032,5124,5249,5371,5476,5617,5773,5899,6022,6125,6224,6352,6446,6547,6655,6755,6854,6982,7106,7233,7357,7470,7586,7701,7816,7929,8042,8129,8214,8327,8464,8633,15304"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-sl/values-sl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "91", "endOffsets": "142"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14951", "endColumns": "91", "endOffsets": "15038"}}]}]}