{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-pt/values-pt.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,746,829,909,1000,1093,1189,1283,1384,1477,1572,1667,1758,1849,1936,2043,2155,2257,2365,2472,2582,2744,2844", "endColumns": "119,105,106,88,100,117,82,79,90,92,95,93,100,92,94,94,90,90,86,106,111,101,107,106,109,161,99,84", "endOffsets": "220,326,433,522,623,741,824,904,995,1088,1184,1278,1379,1472,1567,1662,1753,1844,1931,2038,2150,2252,2360,2467,2577,2739,2839,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,746,829,909,1000,1093,1189,1283,1384,1477,1572,1667,1758,1849,1936,2043,2155,2257,2365,2472,2582,2744,15362", "endColumns": "119,105,106,88,100,117,82,79,90,92,95,93,100,92,94,94,90,90,86,106,111,101,107,106,109,161,99,84", "endOffsets": "220,326,433,522,623,741,824,904,995,1088,1184,1278,1379,1472,1567,1662,1753,1844,1931,2038,2150,2252,2360,2467,2577,2739,2839,15442"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,563,673,793", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "147,249,348,448,558,668,788,889"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2935,3032,3134,3233,3333,3443,3553,15838", "endColumns": "96,101,98,99,109,109,119,100", "endOffsets": "3027,3129,3228,3328,3438,3548,3668,15934"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,507,633,747,846,944,1077,1206,1305,1422,1589,1714,1835,1938,2029,2161,2256,2359,2462,2567,2660,2770,2898,3019,3136,3239,3347,3458,3574,3679,3789,3876,3963,4067,4205,4360", "endColumns": "106,100,97,95,125,113,98,97,132,128,98,116,166,124,120,102,90,131,94,102,102,104,92,109,127,120,116,102,107,110,115,104,109,86,86,103,137,154,92", "endOffsets": "207,308,406,502,628,742,841,939,1072,1201,1300,1417,1584,1709,1830,1933,2024,2156,2251,2354,2457,2562,2655,2765,2893,3014,3131,3234,3342,3453,3569,3674,3784,3871,3958,4062,4200,4355,4448"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4316,4423,4524,4622,4718,4844,4958,5057,5155,5288,5417,5516,5633,5800,5925,6046,6149,6240,6372,6467,6570,6673,6778,6871,6981,7109,7230,7347,7450,7558,7669,7785,7890,8000,8087,8174,8278,8416,15094", "endColumns": "106,100,97,95,125,113,98,97,132,128,98,116,166,124,120,102,90,131,94,102,102,104,92,109,127,120,116,102,107,110,115,104,109,86,86,103,137,154,92", "endOffsets": "4418,4519,4617,4713,4839,4953,5052,5150,5283,5412,5511,5628,5795,5920,6041,6144,6235,6367,6462,6565,6668,6773,6866,6976,7104,7225,7342,7445,7553,7664,7780,7885,7995,8082,8169,8273,8411,8566,15182"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,295,411,527,629,726,840,974,1092,1244,1328,1429,1524,1624,1739,1869,1975,2114,2250,2381,2547,2674,2794,2918,3038,3134,3231,3351,3467,3567,3678,3787,3927,4072,4182,4285,4371,4465,4557,4673,4763,4852,4953,5033,5117,5218,5324,5416,5515,5603,5715,5816,5920,6039,6119,6219", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "169,290,406,522,624,721,835,969,1087,1239,1323,1424,1519,1619,1734,1864,1970,2109,2245,2376,2542,2669,2789,2913,3033,3129,3226,3346,3462,3562,3673,3782,3922,4067,4177,4280,4366,4460,4552,4668,4758,4847,4948,5028,5112,5213,5319,5411,5510,5598,5710,5811,5915,6034,6114,6214,6306"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8571,8690,8811,8927,9043,9145,9242,9356,9490,9608,9760,9844,9945,10040,10140,10255,10385,10491,10630,10766,10897,11063,11190,11310,11434,11554,11650,11747,11867,11983,12083,12194,12303,12443,12588,12698,12801,12887,12981,13073,13189,13279,13368,13469,13549,13633,13734,13840,13932,14031,14119,14231,14332,14436,14555,14635,14735", "endColumns": "118,120,115,115,101,96,113,133,117,151,83,100,94,99,114,129,105,138,135,130,165,126,119,123,119,95,96,119,115,99,110,108,139,144,109,102,85,93,91,115,89,88,100,79,83,100,105,91,98,87,111,100,103,118,79,99,91", "endOffsets": "8685,8806,8922,9038,9140,9237,9351,9485,9603,9755,9839,9940,10035,10135,10250,10380,10486,10625,10761,10892,11058,11185,11305,11429,11549,11645,11742,11862,11978,12078,12189,12298,12438,12583,12693,12796,12882,12976,13068,13184,13274,13363,13464,13544,13628,13729,13835,13927,14026,14114,14226,14327,14431,14550,14630,14730,14822"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-pt/values-pt.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,146,229", "endColumns": "90,82,84", "endOffsets": "141,224,309"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2844,16195,16278", "endColumns": "90,82,84", "endOffsets": "2930,16273,16358"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-pt/values-pt.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14827", "endColumns": "88", "endOffsets": "14911"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-pt/values-pt.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "197,292,378,475,574,660,743,840,931,1018,1103,1193,1269,1354,1430,1509,1584,1660,1727", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "287,373,470,569,655,738,835,926,1013,1098,1188,1264,1349,1425,1504,1579,1655,1722,1835"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3673,3768,3854,3951,4050,4136,4219,14916,15007,15187,15272,15447,15523,15608,15684,15763,15939,16015,16082", "endColumns": "94,85,96,98,85,82,96,90,86,84,89,75,84,75,78,74,75,66,112", "endOffsets": "3763,3849,3946,4045,4131,4214,4311,15002,15089,15267,15357,15518,15603,15679,15758,15833,16010,16077,16190"}}]}]}