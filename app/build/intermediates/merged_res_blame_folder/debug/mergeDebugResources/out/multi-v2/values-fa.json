{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-fa/values-fa.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-fa/values-fa.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,272,351,445,543,629,711,814,899,982,1063,1145,1219,1303,1378,1452,1524,1599,1666", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "267,346,440,538,624,706,809,894,977,1058,1140,1214,1298,1373,1447,1519,1594,1661,1778"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3625,3712,3791,3885,3983,4069,4151,14698,14783,14955,15036,15199,15273,15357,15432,15506,15679,15754,15821", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,83,74,73,71,74,66,116", "endOffsets": "3707,3786,3880,3978,4064,4146,4249,14778,14861,15031,15113,15268,15352,15427,15501,15573,15749,15816,15933"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,318,429,513,616,731,810,888,981,1076,1172,1266,1369,1464,1561,1660,1753,1843,1924,2036,2139,2237,2347,2451,2560,2721,2822", "endColumns": "109,102,110,83,102,114,78,77,92,94,95,93,102,94,96,98,92,89,80,111,102,97,109,103,108,160,100,80", "endOffsets": "210,313,424,508,611,726,805,883,976,1071,1167,1261,1364,1459,1556,1655,1748,1838,1919,2031,2134,2232,2342,2446,2555,2716,2817,2898"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,318,429,513,616,731,810,888,981,1076,1172,1266,1369,1464,1561,1660,1753,1843,1924,2036,2139,2237,2347,2451,2560,2721,15118", "endColumns": "109,102,110,83,102,114,78,77,92,94,95,93,102,94,96,98,92,89,80,111,102,97,109,103,108,160,100,80", "endOffsets": "210,313,424,508,611,726,805,883,976,1071,1167,1261,1364,1459,1556,1655,1748,1838,1919,2031,2134,2232,2342,2446,2555,2716,2817,15194"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,223", "endColumns": "78,88,88", "endOffsets": "129,218,307"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2822,15938,16027", "endColumns": "78,88,88", "endOffsets": "2896,16022,16111"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2901,3000,3102,3201,3301,3402,3508,15578", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "2995,3097,3196,3296,3397,3503,3620,15674"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,280,394,512,608,704,818,957,1073,1208,1293,1396,1488,1585,1699,1822,1930,2063,2194,2316,2481,2603,2716,2832,2949,3042,3140,3261,3393,3500,3603,3708,3839,3975,4081,4191,4271,4364,4461,4582,4668,4752,4851,4933,5017,5118,5219,5316,5416,5503,5607,5707,5810,5930,6012,6116", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "165,275,389,507,603,699,813,952,1068,1203,1288,1391,1483,1580,1694,1817,1925,2058,2189,2311,2476,2598,2711,2827,2944,3037,3135,3256,3388,3495,3598,3703,3834,3970,4076,4186,4266,4359,4456,4577,4663,4747,4846,4928,5012,5113,5214,5311,5411,5498,5602,5702,5805,5925,6007,6111,6209"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8451,8566,8676,8790,8908,9004,9100,9214,9353,9469,9604,9689,9792,9884,9981,10095,10218,10326,10459,10590,10712,10877,10999,11112,11228,11345,11438,11536,11657,11789,11896,11999,12104,12235,12371,12477,12587,12667,12760,12857,12978,13064,13148,13247,13329,13413,13514,13615,13712,13812,13899,14003,14103,14206,14326,14408,14512", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "8561,8671,8785,8903,8999,9095,9209,9348,9464,9599,9684,9787,9879,9976,10090,10213,10321,10454,10585,10707,10872,10994,11107,11223,11340,11433,11531,11652,11784,11891,11994,12099,12230,12366,12472,12582,12662,12755,12852,12973,13059,13143,13242,13324,13408,13509,13610,13707,13807,13894,13998,14098,14201,14321,14403,14507,14605"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-fa/values-fa.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14610", "endColumns": "87", "endOffsets": "14693"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-fa/values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,407,500,618,726,822,918,1044,1166,1271,1396,1548,1672,1792,1900,1988,2118,2206,2306,2412,2514,2609,2723,2841,2960,3075,3179,3287,3398,3513,3622,3735,3822,3905,4013,4148,4302", "endColumns": "106,100,93,92,117,107,95,95,125,121,104,124,151,123,119,107,87,129,87,99,105,101,94,113,117,118,114,103,107,110,114,108,112,86,82,107,134,153,88", "endOffsets": "207,308,402,495,613,721,817,913,1039,1161,1266,1391,1543,1667,1787,1895,1983,2113,2201,2301,2407,2509,2604,2718,2836,2955,3070,3174,3282,3393,3508,3617,3730,3817,3900,4008,4143,4297,4386"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4254,4361,4462,4556,4649,4767,4875,4971,5067,5193,5315,5420,5545,5697,5821,5941,6049,6137,6267,6355,6455,6561,6663,6758,6872,6990,7109,7224,7328,7436,7547,7662,7771,7884,7971,8054,8162,8297,14866", "endColumns": "106,100,93,92,117,107,95,95,125,121,104,124,151,123,119,107,87,129,87,99,105,101,94,113,117,118,114,103,107,110,114,108,112,86,82,107,134,153,88", "endOffsets": "4356,4457,4551,4644,4762,4870,4966,5062,5188,5310,5415,5540,5692,5816,5936,6044,6132,6262,6350,6450,6556,6658,6753,6867,6985,7104,7219,7323,7431,7542,7657,7766,7879,7966,8049,8157,8292,8446,14950"}}]}]}