{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-mn/values-mn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,318,431,518,624,736,818,900,991,1084,1180,1276,1374,1467,1562,1654,1745,1835,1917,2026,2130,2227,2335,2436,2539,2698,2795", "endColumns": "112,99,112,86,105,111,81,81,90,92,95,95,97,92,94,91,90,89,81,108,103,96,107,100,102,158,96,80", "endOffsets": "213,313,426,513,619,731,813,895,986,1079,1175,1271,1369,1462,1557,1649,1740,1830,1912,2021,2125,2222,2330,2431,2534,2693,2790,2871"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,318,431,518,624,736,818,900,991,1084,1180,1276,1374,1467,1562,1654,1745,1835,1917,2026,2130,2227,2335,2436,2539,2698,15336", "endColumns": "112,99,112,86,105,111,81,81,90,92,95,95,97,92,94,91,90,89,81,108,103,96,107,100,102,158,96,80", "endOffsets": "213,313,426,513,619,731,813,895,986,1079,1175,1271,1369,1462,1557,1649,1740,1830,1912,2021,2125,2222,2330,2431,2534,2693,2790,15412"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-mn/values-mn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "94", "endOffsets": "145"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14816", "endColumns": "94", "endOffsets": "14906"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2879,2977,3079,3180,3278,3383,3495,15815", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "2972,3074,3175,3273,3378,3490,3609,15911"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,414,509,632,742,840,933,1070,1202,1312,1440,1591,1721,1846,1951,2047,2183,2277,2382,2494,2597,2690,2800,2916,3035,3149,3256,3366,3492,3615,3740,3861,3948,4030,4135,4268,4424", "endColumns": "106,100,100,94,122,109,97,92,136,131,109,127,150,129,124,104,95,135,93,104,111,102,92,109,115,118,113,106,109,125,122,124,120,86,81,104,132,155,90", "endOffsets": "207,308,409,504,627,737,835,928,1065,1197,1307,1435,1586,1716,1841,1946,2042,2178,2272,2377,2489,2592,2685,2795,2911,3030,3144,3251,3361,3487,3610,3735,3856,3943,4025,4130,4263,4419,4510"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4243,4350,4451,4552,4647,4770,4880,4978,5071,5208,5340,5450,5578,5729,5859,5984,6089,6185,6321,6415,6520,6632,6735,6828,6938,7054,7173,7287,7394,7504,7630,7753,7878,7999,8086,8168,8273,8406,15079", "endColumns": "106,100,100,94,122,109,97,92,136,131,109,127,150,129,124,104,95,135,93,104,111,102,92,109,115,118,113,106,109,125,122,124,120,86,81,104,132,155,90", "endOffsets": "4345,4446,4547,4642,4765,4875,4973,5066,5203,5335,5445,5573,5724,5854,5979,6084,6180,6316,6410,6515,6627,6730,6823,6933,7049,7168,7282,7389,7499,7625,7748,7873,7994,8081,8163,8268,8401,8557,15165"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,139,225", "endColumns": "83,85,88", "endOffsets": "134,220,309"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2795,16176,16262", "endColumns": "83,85,88", "endOffsets": "2874,16257,16346"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-mn/values-mn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "190,282,368,460,556,639,725,819,906,987,1070,1153,1226,1317,1394,1474,1551,1628,1694", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,90,76,79,76,76,65,116", "endOffsets": "277,363,455,551,634,720,814,901,982,1065,1148,1221,1312,1389,1469,1546,1623,1689,1806"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3614,3706,3792,3884,3980,4063,4149,14911,14998,15170,15253,15417,15490,15581,15658,15738,15916,15993,16059", "endColumns": "91,85,91,95,82,85,93,86,80,82,82,72,90,76,79,76,76,65,116", "endOffsets": "3701,3787,3879,3975,4058,4144,4238,14993,15074,15248,15331,15485,15576,15653,15733,15810,15988,16054,16171"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,290,402,519,618,715,829,971,1089,1228,1313,1415,1507,1605,1723,1845,1952,2094,2238,2370,2546,2672,2793,2913,3032,3125,3225,3348,3486,3585,3691,3797,3941,4086,4193,4292,4375,4470,4564,4675,4760,4844,4945,5025,5108,5207,5307,5402,5504,5591,5695,5794,5899,6030,6110,6214", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "169,285,397,514,613,710,824,966,1084,1223,1308,1410,1502,1600,1718,1840,1947,2089,2233,2365,2541,2667,2788,2908,3027,3120,3220,3343,3481,3580,3686,3792,3936,4081,4188,4287,4370,4465,4559,4670,4755,4839,4940,5020,5103,5202,5302,5397,5499,5586,5690,5789,5894,6025,6105,6209,6304"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8562,8681,8797,8909,9026,9125,9222,9336,9478,9596,9735,9820,9922,10014,10112,10230,10352,10459,10601,10745,10877,11053,11179,11300,11420,11539,11632,11732,11855,11993,12092,12198,12304,12448,12593,12700,12799,12882,12977,13071,13182,13267,13351,13452,13532,13615,13714,13814,13909,14011,14098,14202,14301,14406,14537,14617,14721", "endColumns": "118,115,111,116,98,96,113,141,117,138,84,101,91,97,117,121,106,141,143,131,175,125,120,119,118,92,99,122,137,98,105,105,143,144,106,98,82,94,93,110,84,83,100,79,82,98,99,94,101,86,103,98,104,130,79,103,94", "endOffsets": "8676,8792,8904,9021,9120,9217,9331,9473,9591,9730,9815,9917,10009,10107,10225,10347,10454,10596,10740,10872,11048,11174,11295,11415,11534,11627,11727,11850,11988,12087,12193,12299,12443,12588,12695,12794,12877,12972,13066,13177,13262,13346,13447,13527,13610,13709,13809,13904,14006,14093,14197,14296,14401,14532,14612,14716,14811"}}]}]}