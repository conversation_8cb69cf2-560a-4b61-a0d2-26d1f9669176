{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-eu/values-eu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2942,3040,3143,3243,3346,3451,3554,16171", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3035,3138,3238,3341,3446,3549,3668,16267"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,227", "endColumns": "85,85,88", "endOffsets": "136,222,311"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2856,16537,16623", "endColumns": "85,85,88", "endOffsets": "2937,16618,16707"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,825,909,1001,1095,1192,1286,1388,1482,1578,1675,1767,1860,1942,2051,2161,2260,2369,2475,2586,2757,2856", "endColumns": "108,97,109,85,105,123,86,83,91,93,96,93,101,93,95,96,91,92,81,108,109,98,108,105,110,170,98,81", "endOffsets": "209,307,417,503,609,733,820,904,996,1090,1187,1281,1383,1477,1573,1670,1762,1855,1937,2046,2156,2255,2364,2470,2581,2752,2851,2933"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,825,909,1001,1095,1192,1286,1388,1482,1578,1675,1767,1860,1942,2051,2161,2260,2369,2475,2586,2757,15693", "endColumns": "108,97,109,85,105,123,86,83,91,93,96,93,101,93,95,96,91,92,81,108,109,98,108,105,110,170,98,81", "endOffsets": "209,307,417,503,609,733,820,904,996,1090,1187,1281,1383,1477,1573,1670,1762,1855,1937,2046,2156,2255,2364,2470,2581,2752,2851,15770"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-eu/values-eu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15168", "endColumns": "88", "endOffsets": "15252"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,414,508,637,749,850,950,1077,1200,1301,1420,1614,1755,1892,2000,2092,2232,2330,2439,2544,2648,2743,2855,2988,3115,3238,3346,3457,3576,3701,3816,3937,4024,4108,4216,4351,4521", "endColumns": "106,100,100,93,128,111,100,99,126,122,100,118,193,140,136,107,91,139,97,108,104,103,94,111,132,126,122,107,110,118,124,114,120,86,83,107,134,169,84", "endOffsets": "207,308,409,503,632,744,845,945,1072,1195,1296,1415,1609,1750,1887,1995,2087,2227,2325,2434,2539,2643,2738,2850,2983,3110,3233,3341,3452,3571,3696,3811,3932,4019,4103,4211,4346,4516,4601"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4305,4412,4513,4614,4708,4837,4949,5050,5150,5277,5400,5501,5620,5814,5955,6092,6200,6292,6432,6530,6639,6744,6848,6943,7055,7188,7315,7438,7546,7657,7776,7901,8016,8137,8224,8308,8416,8551,15430", "endColumns": "106,100,100,93,128,111,100,99,126,122,100,118,193,140,136,107,91,139,97,108,104,103,94,111,132,126,122,107,110,118,124,114,120,86,83,107,134,169,84", "endOffsets": "4407,4508,4609,4703,4832,4944,5045,5145,5272,5395,5496,5615,5809,5950,6087,6195,6287,6427,6525,6634,6739,6843,6938,7050,7183,7310,7433,7541,7652,7771,7896,8011,8132,8219,8303,8411,8546,8716,15510"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-eu/values-eu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,283,364,465,569,661,737,824,913,997,1085,1175,1249,1334,1411,1493,1571,1648,1716", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,84,76,81,77,76,67,119", "endOffsets": "278,359,460,564,656,732,819,908,992,1080,1170,1244,1329,1406,1488,1566,1643,1711,1831"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3673,3764,3845,3946,4050,4142,4218,15257,15346,15515,15603,15775,15849,15934,16011,16093,16272,16349,16417", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,84,76,81,77,76,67,119", "endOffsets": "3759,3840,3941,4045,4137,4213,4300,15341,15425,15598,15688,15844,15929,16006,16088,16166,16344,16412,16532"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-eu/values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,349,476,621,750,848,963,1103,1222,1367,1451,1556,1652,1752,1871,1992,2102,2245,2389,2524,2715,2840,2962,3086,3208,3305,3402,3530,3665,3763,3866,3972,4119,4270,4378,4478,4554,4650,4745,4864,4951,5039,5149,5229,5314,5409,5512,5603,5702,5791,5899,5999,6105,6223,6303,6407", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "197,344,471,616,745,843,958,1098,1217,1362,1446,1551,1647,1747,1866,1987,2097,2240,2384,2519,2710,2835,2957,3081,3203,3300,3397,3525,3660,3758,3861,3967,4114,4265,4373,4473,4549,4645,4740,4859,4946,5034,5144,5224,5309,5404,5507,5598,5697,5786,5894,5994,6100,6218,6298,6402,6497"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8721,8868,9015,9142,9287,9416,9514,9629,9769,9888,10033,10117,10222,10318,10418,10537,10658,10768,10911,11055,11190,11381,11506,11628,11752,11874,11971,12068,12196,12331,12429,12532,12638,12785,12936,13044,13144,13220,13316,13411,13530,13617,13705,13815,13895,13980,14075,14178,14269,14368,14457,14565,14665,14771,14889,14969,15073", "endColumns": "146,146,126,144,128,97,114,139,118,144,83,104,95,99,118,120,109,142,143,134,190,124,121,123,121,96,96,127,134,97,102,105,146,150,107,99,75,95,94,118,86,87,109,79,84,94,102,90,98,88,107,99,105,117,79,103,94", "endOffsets": "8863,9010,9137,9282,9411,9509,9624,9764,9883,10028,10112,10217,10313,10413,10532,10653,10763,10906,11050,11185,11376,11501,11623,11747,11869,11966,12063,12191,12326,12424,12527,12633,12780,12931,13039,13139,13215,13311,13406,13525,13612,13700,13810,13890,13975,14070,14173,14264,14363,14452,14560,14660,14766,14884,14964,15068,15163"}}]}]}