{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-hu/values-hu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,505,622,734,832,930,1055,1183,1293,1415,1574,1706,1841,1951,2042,2166,2260,2367,2473,2576,2676,2793,2905,3034,3166,3275,3385,3512,3647,3768,3897,3984,4069,4185,4326,4490", "endColumns": "106,100,94,96,116,111,97,97,124,127,109,121,158,131,134,109,90,123,93,106,105,102,99,116,111,128,131,108,109,126,134,120,128,86,84,115,140,163,92", "endOffsets": "207,308,403,500,617,729,827,925,1050,1178,1288,1410,1569,1701,1836,1946,2037,2161,2255,2362,2468,2571,2671,2788,2900,3029,3161,3270,3380,3507,3642,3763,3892,3979,4064,4180,4321,4485,4578"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4331,4438,4539,4634,4731,4848,4960,5058,5156,5281,5409,5519,5641,5800,5932,6067,6177,6268,6392,6486,6593,6699,6802,6902,7019,7131,7260,7392,7501,7611,7738,7873,7994,8123,8210,8295,8411,8552,15319", "endColumns": "106,100,94,96,116,111,97,97,124,127,109,121,158,131,134,109,90,123,93,106,105,102,99,116,111,128,131,108,109,126,134,120,128,86,84,115,140,163,92", "endOffsets": "4433,4534,4629,4726,4843,4955,5053,5151,5276,5404,5514,5636,5795,5927,6062,6172,6263,6387,6481,6588,6694,6797,6897,7014,7126,7255,7387,7496,7606,7733,7868,7989,8118,8205,8290,8406,8547,8711,15407"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,292,400,516,611,708,822,962,1085,1232,1317,1417,1515,1617,1739,1876,1981,2121,2259,2385,2581,2704,2826,2948,3074,3173,3268,3387,3524,3626,3737,3841,3986,4133,4240,4347,4431,4529,4623,4731,4819,4906,5007,5088,5171,5270,5376,5471,5574,5660,5769,5867,5973,6094,6175,6287", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "171,287,395,511,606,703,817,957,1080,1227,1312,1412,1510,1612,1734,1871,1976,2116,2254,2380,2576,2699,2821,2943,3069,3168,3263,3382,3519,3621,3732,3836,3981,4128,4235,4342,4426,4524,4618,4726,4814,4901,5002,5083,5166,5265,5371,5466,5569,5655,5764,5862,5968,6089,6170,6282,6380"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8716,8837,8953,9061,9177,9272,9369,9483,9623,9746,9893,9978,10078,10176,10278,10400,10537,10642,10782,10920,11046,11242,11365,11487,11609,11735,11834,11929,12048,12185,12287,12398,12502,12647,12794,12901,13008,13092,13190,13284,13392,13480,13567,13668,13749,13832,13931,14037,14132,14235,14321,14430,14528,14634,14755,14836,14948", "endColumns": "120,115,107,115,94,96,113,139,122,146,84,99,97,101,121,136,104,139,137,125,195,122,121,121,125,98,94,118,136,101,110,103,144,146,106,106,83,97,93,107,87,86,100,80,82,98,105,94,102,85,108,97,105,120,80,111,97", "endOffsets": "8832,8948,9056,9172,9267,9364,9478,9618,9741,9888,9973,10073,10171,10273,10395,10532,10637,10777,10915,11041,11237,11360,11482,11604,11730,11829,11924,12043,12180,12282,12393,12497,12642,12789,12896,13003,13087,13185,13279,13387,13475,13562,13663,13744,13827,13926,14032,14127,14230,14316,14425,14523,14629,14750,14831,14943,15041"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,433,517,629,759,835,911,1002,1095,1191,1285,1386,1479,1574,1669,1760,1851,1934,2044,2155,2255,2366,2474,2593,2775,2878", "endColumns": "107,104,114,83,111,129,75,75,90,92,95,93,100,92,94,94,90,90,82,109,110,99,110,107,118,181,102,82", "endOffsets": "208,313,428,512,624,754,830,906,997,1090,1186,1280,1381,1474,1569,1664,1755,1846,1929,2039,2150,2250,2361,2469,2588,2770,2873,2956"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,433,517,629,759,835,911,1002,1095,1191,1285,1386,1479,1574,1669,1760,1851,1934,2044,2155,2255,2366,2474,2593,2775,15583", "endColumns": "107,104,114,83,111,129,75,75,90,92,95,93,100,92,94,94,90,90,82,109,110,99,110,107,118,181,102,82", "endOffsets": "208,313,428,512,624,754,830,906,997,1090,1186,1280,1381,1474,1569,1664,1755,1846,1929,2039,2150,2250,2361,2469,2588,2770,2873,15661"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-hu/values-hu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,288,376,473,572,659,741,837,926,1013,1096,1184,1258,1351,1426,1497,1567,1646,1712", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,92,74,70,69,78,65,120", "endOffsets": "283,371,468,567,654,736,832,921,1008,1091,1179,1253,1346,1421,1492,1562,1641,1707,1828"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3687,3782,3870,3967,4066,4153,4235,15143,15232,15412,15495,15666,15740,15833,15908,15979,16150,16229,16295", "endColumns": "94,87,96,98,86,81,95,88,86,82,87,73,92,74,70,69,78,65,120", "endOffsets": "3777,3865,3962,4061,4148,4230,4326,15227,15314,15490,15578,15735,15828,15903,15974,16044,16224,16290,16411"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,231", "endColumns": "86,88,96", "endOffsets": "137,226,323"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2878,16416,16505", "endColumns": "86,88,96", "endOffsets": "2960,16500,16597"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-hu/values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2965,3062,3164,3266,3367,3470,3577,16049", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "3057,3159,3261,3362,3465,3572,3682,16145"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-hu/values-hu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "96", "endOffsets": "147"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15046", "endColumns": "96", "endOffsets": "15138"}}]}]}