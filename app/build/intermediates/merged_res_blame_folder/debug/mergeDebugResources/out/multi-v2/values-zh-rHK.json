{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "177,253,327,415,506,584,658,735,813,887,960,1035,1102,1183,1256,1326,1395,1470,1535", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,80,72,69,68,74,64,115", "endOffsets": "248,322,410,501,579,653,730,808,882,955,1030,1097,1178,1251,1321,1390,1465,1530,1646"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3400,3476,3550,3638,3729,3807,3881,13392,13470,13625,13698,13851,13918,13999,14072,14142,14312,14387,14452", "endColumns": "75,73,87,90,77,73,76,77,73,72,74,66,80,72,69,68,74,64,115", "endOffsets": "3471,3545,3633,3724,3802,3876,3953,13465,13539,13693,13768,13913,13994,14067,14137,14206,14382,14447,14563"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "84", "endOffsets": "135"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "13307", "endColumns": "84", "endOffsets": "13387"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,404,494,607,708,802,891,1004,1116,1212,1324,1428,1536,1643,1740,1827,1935,2022,2121,2218,2316,2405,2511,2605,2707,2808,2905,3006,3103,3206,3300,3400,3487,3567,3658,3790,3933", "endColumns": "106,100,90,89,112,100,93,88,112,111,95,111,103,107,106,96,86,107,86,98,96,97,88,105,93,101,100,96,100,96,102,93,99,86,79,90,131,142,80", "endOffsets": "207,308,399,489,602,703,797,886,999,1111,1207,1319,1423,1531,1638,1735,1822,1930,2017,2116,2213,2311,2400,2506,2600,2702,2803,2900,3001,3098,3201,3295,3395,3482,3562,3653,3785,3928,4009"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3958,4065,4166,4257,4347,4460,4561,4655,4744,4857,4969,5065,5177,5281,5389,5496,5593,5680,5788,5875,5974,6071,6169,6258,6364,6458,6560,6661,6758,6859,6956,7059,7153,7253,7340,7420,7511,7643,13544", "endColumns": "106,100,90,89,112,100,93,88,112,111,95,111,103,107,106,96,86,107,86,98,96,97,88,105,93,101,100,96,100,96,102,93,99,86,79,90,131,142,80", "endOffsets": "4060,4161,4252,4342,4455,4556,4650,4739,4852,4964,5060,5172,5276,5384,5491,5588,5675,5783,5870,5969,6066,6164,6253,6359,6453,6555,6656,6753,6854,6951,7054,7148,7248,7335,7415,7506,7638,7781,13620"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,364,465,556,645,750,855,960,1076,1158,1254,1338,1426,1531,1644,1745,1853,1959,2067,2183,2288,2390,2495,2601,2686,2781,2886,2995,3085,3187,3285,3394,3508,3608,3699,3772,3862,3951,4042,4125,4207,4296,4376,4458,4555,4649,4742,4835,4919,5015,5111,5206,5314,5394,5486", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,90,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "153,255,359,460,551,640,745,850,955,1071,1153,1249,1333,1421,1526,1639,1740,1848,1954,2062,2178,2283,2385,2490,2596,2681,2776,2881,2990,3080,3182,3280,3389,3503,3603,3694,3767,3857,3946,4037,4120,4202,4291,4371,4453,4550,4644,4737,4830,4914,5010,5106,5201,5309,5389,5481,5571"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7786,7889,7991,8095,8196,8287,8376,8481,8586,8691,8807,8889,8985,9069,9157,9262,9375,9476,9584,9690,9798,9914,10019,10121,10226,10332,10417,10512,10617,10726,10816,10918,11016,11125,11239,11339,11430,11503,11593,11682,11773,11856,11938,12027,12107,12189,12286,12380,12473,12566,12650,12746,12842,12937,13045,13125,13217", "endColumns": "102,101,103,100,90,88,104,104,104,115,81,95,83,87,104,112,100,107,105,107,115,104,101,104,105,84,94,104,108,89,101,97,108,113,99,90,72,89,88,90,82,81,88,79,81,96,93,92,92,83,95,95,94,107,79,91,89", "endOffsets": "7884,7986,8090,8191,8282,8371,8476,8581,8686,8802,8884,8980,9064,9152,9257,9370,9471,9579,9685,9793,9909,10014,10116,10221,10327,10412,10507,10612,10721,10811,10913,11011,11120,11234,11334,11425,11498,11588,11677,11768,11851,11933,12022,12102,12184,12281,12375,12468,12561,12645,12741,12837,12932,13040,13120,13212,13302"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,126,207", "endColumns": "70,80,76", "endOffsets": "121,202,279"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2668,14568,14649", "endColumns": "70,80,76", "endOffsets": "2734,14644,14721"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1110,1206,1302,1396,1492,1584,1676,1768,1846,1942,2038,2133,2230,2325,2423,2574,2668", "endColumns": "94,92,99,81,96,107,75,75,91,93,91,95,95,93,95,91,91,91,77,95,95,94,96,94,97,150,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1105,1201,1297,1391,1487,1579,1671,1763,1841,1937,2033,2128,2225,2320,2418,2569,2663,2741"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,756,832,924,1018,1110,1206,1302,1396,1492,1584,1676,1768,1846,1942,2038,2133,2230,2325,2423,2574,13773", "endColumns": "94,92,99,81,96,107,75,75,91,93,91,95,95,93,95,91,91,91,77,95,95,94,96,94,97,150,93,77", "endOffsets": "195,288,388,470,567,675,751,827,919,1013,1105,1201,1297,1391,1487,1579,1671,1763,1841,1937,2033,2128,2225,2320,2418,2569,2663,13846"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-zh-rHK/values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2739,2831,2930,3024,3118,3211,3304,14211", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "2826,2925,3019,3113,3206,3299,3395,14307"}}]}]}