{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-th/values-th.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,416,505,607,717,794,872,963,1056,1152,1246,1347,1440,1535,1629,1720,1811,1892,2000,2104,2202,2310,2415,2516,2669,2764", "endColumns": "104,97,107,88,101,109,76,77,90,92,95,93,100,92,94,93,90,90,80,107,103,97,107,104,100,152,94,80", "endOffsets": "205,303,411,500,602,712,789,867,958,1051,1147,1241,1342,1435,1530,1624,1715,1806,1887,1995,2099,2197,2305,2410,2511,2664,2759,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,308,416,505,607,717,794,872,963,1056,1152,1246,1347,1440,1535,1629,1720,1811,1892,2000,2104,2202,2310,2415,2516,2669,14992", "endColumns": "104,97,107,88,101,109,76,77,90,92,95,93,100,92,94,93,90,90,80,107,103,97,107,104,100,152,94,80", "endOffsets": "205,303,411,500,602,712,789,867,958,1051,1147,1241,1342,1435,1530,1624,1715,1806,1887,1995,2099,2197,2305,2410,2511,2664,2759,15068"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2851,2947,3050,3148,3246,3349,3454,15464", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "2942,3045,3143,3241,3344,3449,3561,15560"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,407,504,631,740,837,930,1053,1176,1281,1404,1531,1647,1763,1877,1974,2101,2190,2295,2397,2505,2599,2710,2817,2930,3043,3150,3264,3378,3491,3600,3708,3795,3878,3976,4110,4259", "endColumns": "106,100,93,96,126,108,96,92,122,122,104,122,126,115,115,113,96,126,88,104,101,107,93,110,106,112,112,106,113,113,112,108,107,86,82,97,133,148,93", "endOffsets": "207,308,402,499,626,735,832,925,1048,1171,1276,1399,1526,1642,1758,1872,1969,2096,2185,2290,2392,2500,2594,2705,2812,2925,3038,3145,3259,3373,3486,3595,3703,3790,3873,3971,4105,4254,4348"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4185,4292,4393,4487,4584,4711,4820,4917,5010,5133,5256,5361,5484,5611,5727,5843,5957,6054,6181,6270,6375,6477,6585,6679,6790,6897,7010,7123,7230,7344,7458,7571,7680,7788,7875,7958,8056,8190,14723", "endColumns": "106,100,93,96,126,108,96,92,122,122,104,122,126,115,115,113,96,126,88,104,101,107,93,110,106,112,112,106,113,113,112,108,107,86,82,97,133,148,93", "endOffsets": "4287,4388,4482,4579,4706,4815,4912,5005,5128,5251,5356,5479,5606,5722,5838,5952,6049,6176,6265,6370,6472,6580,6674,6785,6892,7005,7118,7225,7339,7453,7566,7675,7783,7870,7953,8051,8185,8334,14812"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-th/values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,280,393,505,604,697,807,937,1061,1202,1288,1388,1479,1577,1695,1811,1916,2043,2167,2295,2447,2570,2688,2812,2933,3025,3124,3236,3369,3465,3583,3690,3816,3950,4060,4158,4239,4333,4427,4534,4620,4703,4808,4888,4975,5074,5176,5270,5374,5460,5561,5659,5762,5879,5959,6069", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,106,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "163,275,388,500,599,692,802,932,1056,1197,1283,1383,1474,1572,1690,1806,1911,2038,2162,2290,2442,2565,2683,2807,2928,3020,3119,3231,3364,3460,3578,3685,3811,3945,4055,4153,4234,4328,4422,4529,4615,4698,4803,4883,4970,5069,5171,5265,5369,5455,5556,5654,5757,5874,5954,6064,6170"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8339,8452,8564,8677,8789,8888,8981,9091,9221,9345,9486,9572,9672,9763,9861,9979,10095,10200,10327,10451,10579,10731,10854,10972,11096,11217,11309,11408,11520,11653,11749,11867,11974,12100,12234,12344,12442,12523,12617,12711,12818,12904,12987,13092,13172,13259,13358,13460,13554,13658,13744,13845,13943,14046,14163,14243,14353", "endColumns": "112,111,112,111,98,92,109,129,123,140,85,99,90,97,117,115,104,126,123,127,151,122,117,123,120,91,98,111,132,95,117,106,125,133,109,97,80,93,93,106,85,82,104,79,86,98,101,93,103,85,100,97,102,116,79,109,105", "endOffsets": "8447,8559,8672,8784,8883,8976,9086,9216,9340,9481,9567,9667,9758,9856,9974,10090,10195,10322,10446,10574,10726,10849,10967,11091,11212,11304,11403,11515,11648,11744,11862,11969,12095,12229,12339,12437,12518,12612,12706,12813,12899,12982,13087,13167,13254,13353,13455,13549,13653,13739,13840,13938,14041,14158,14238,14348,14454"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-th/values-th.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,239", "endColumns": "86,96,94", "endOffsets": "137,234,329"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2764,15833,15930", "endColumns": "86,96,94", "endOffsets": "2846,15925,16020"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-th/values-th.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "94", "endOffsets": "145"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14459", "endColumns": "94", "endOffsets": "14549"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-th/values-th.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,279,356,453,554,642,727,812,898,981,1067,1156,1229,1320,1395,1471,1547,1625,1692", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,90,74,75,75,77,66,122", "endOffsets": "274,351,448,549,637,722,807,893,976,1062,1151,1224,1315,1390,1466,1542,1620,1687,1810"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3566,3652,3729,3826,3927,4015,4100,14554,14640,14817,14903,15073,15146,15237,15312,15388,15565,15643,15710", "endColumns": "85,76,96,100,87,84,84,85,82,85,88,72,90,74,75,75,77,66,122", "endOffsets": "3647,3724,3821,3922,4010,4095,4180,14635,14718,14898,14987,15141,15232,15307,15383,15459,15638,15705,15828"}}]}]}