{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-ta/values-ta.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,317,432,521,628,754,832,909,1009,1114,1210,1305,1412,1514,1618,1713,1815,1913,1995,2097,2201,2298,2408,2510,2617,2774,2874", "endColumns": "113,97,114,88,106,125,77,76,99,104,95,94,106,101,103,94,101,97,81,101,103,96,109,101,106,156,99,79", "endOffsets": "214,312,427,516,623,749,827,904,1004,1109,1205,1300,1407,1509,1613,1708,1810,1908,1990,2092,2196,2293,2403,2505,2612,2769,2869,2949"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,317,432,521,628,754,832,909,1009,1114,1210,1305,1412,1514,1618,1713,1815,1913,1995,2097,2201,2298,2408,2510,2617,2774,15897", "endColumns": "113,97,114,88,106,125,77,76,99,104,95,94,106,101,103,94,101,97,81,101,103,96,109,101,106,156,99,79", "endOffsets": "214,312,427,516,623,749,827,904,1004,1109,1205,1300,1407,1509,1613,1708,1810,1908,1990,2092,2196,2293,2403,2505,2612,2769,2869,15972"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2951,3047,3150,3249,3347,3454,3569,16388", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3042,3145,3244,3342,3449,3564,3692,16484"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-ta/values-ta.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "95", "endOffsets": "146"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15359", "endColumns": "95", "endOffsets": "15450"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,225", "endColumns": "76,92,97", "endOffsets": "127,220,318"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2874,16758,16851", "endColumns": "76,92,97", "endOffsets": "2946,16846,16944"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-ta/values-ta.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "183,280,364,458,559,650,733,842,933,1028,1110,1196,1286,1378,1463,1536,1607,1687,1756", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,91,84,72,70,79,68,119", "endOffsets": "275,359,453,554,645,728,837,928,1023,1105,1191,1281,1373,1458,1531,1602,1682,1751,1871"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3697,3794,3878,3972,4073,4164,4247,15455,15546,15729,15811,15977,16067,16159,16244,16317,16489,16569,16638", "endColumns": "96,83,93,100,90,82,108,90,94,81,85,89,91,84,72,70,79,68,119", "endOffsets": "3789,3873,3967,4068,4159,4242,4351,15541,15636,15806,15892,16062,16154,16239,16312,16383,16564,16633,16753"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,500,636,750,872,965,1095,1223,1330,1458,1628,1747,1864,1972,2068,2210,2301,2419,2536,2655,2750,2872,2996,3123,3237,3343,3454,3580,3712,3840,3974,4061,4144,4241,4378,4529", "endColumns": "106,100,94,91,135,113,121,92,129,127,106,127,169,118,116,107,95,141,90,117,116,118,94,121,123,126,113,105,110,125,131,127,133,86,82,96,136,150,87", "endOffsets": "207,308,403,495,631,745,867,960,1090,1218,1325,1453,1623,1742,1859,1967,2063,2205,2296,2414,2531,2650,2745,2867,2991,3118,3232,3338,3449,3575,3707,3835,3969,4056,4139,4236,4373,4524,4612"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4356,4463,4564,4659,4751,4887,5001,5123,5216,5346,5474,5581,5709,5879,5998,6115,6223,6319,6461,6552,6670,6787,6906,7001,7123,7247,7374,7488,7594,7705,7831,7963,8091,8225,8312,8395,8492,8629,15641", "endColumns": "106,100,94,91,135,113,121,92,129,127,106,127,169,118,116,107,95,141,90,117,116,118,94,121,123,126,113,105,110,125,131,127,133,86,82,96,136,150,87", "endOffsets": "4458,4559,4654,4746,4882,4996,5118,5211,5341,5469,5576,5704,5874,5993,6110,6218,6314,6456,6547,6665,6782,6901,6996,7118,7242,7369,7483,7589,7700,7826,7958,8086,8220,8307,8390,8487,8624,8775,15724"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,307,428,552,653,749,862,1013,1144,1285,1369,1473,1573,1681,1798,1921,2030,2176,2320,2454,2660,2789,2910,3035,3181,3282,3380,3526,3662,3768,3881,3988,4134,4286,4395,4507,4585,4687,4790,4907,4993,5086,5199,5279,5367,5466,5586,5681,5786,5875,5997,6101,6208,6341,6421,6532", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,116,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "176,302,423,547,648,744,857,1008,1139,1280,1364,1468,1568,1676,1793,1916,2025,2171,2315,2449,2655,2784,2905,3030,3176,3277,3375,3521,3657,3763,3876,3983,4129,4281,4390,4502,4580,4682,4785,4902,4988,5081,5194,5274,5362,5461,5581,5676,5781,5870,5992,6096,6203,6336,6416,6527,6629"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8780,8906,9032,9153,9277,9378,9474,9587,9738,9869,10010,10094,10198,10298,10406,10523,10646,10755,10901,11045,11179,11385,11514,11635,11760,11906,12007,12105,12251,12387,12493,12606,12713,12859,13011,13120,13232,13310,13412,13515,13632,13718,13811,13924,14004,14092,14191,14311,14406,14511,14600,14722,14826,14933,15066,15146,15257", "endColumns": "125,125,120,123,100,95,112,150,130,140,83,103,99,107,116,122,108,145,143,133,205,128,120,124,145,100,97,145,135,105,112,106,145,151,108,111,77,101,102,116,85,92,112,79,87,98,119,94,104,88,121,103,106,132,79,110,101", "endOffsets": "8901,9027,9148,9272,9373,9469,9582,9733,9864,10005,10089,10193,10293,10401,10518,10641,10750,10896,11040,11174,11380,11509,11630,11755,11901,12002,12100,12246,12382,12488,12601,12708,12854,13006,13115,13227,13305,13407,13510,13627,13713,13806,13919,13999,14087,14186,14306,14401,14506,14595,14717,14821,14928,15061,15141,15252,15354"}}]}]}