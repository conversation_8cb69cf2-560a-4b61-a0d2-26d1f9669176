{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-vi/values-vi.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,134,222", "endColumns": "78,87,86", "endOffsets": "129,217,304"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2817,15942,16030", "endColumns": "78,87,86", "endOffsets": "2891,16025,16112"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2896,2993,3095,3194,3294,3397,3510,15584", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "2988,3090,3189,3289,3392,3505,3621,15680"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,396,513,614,709,821,958,1078,1219,1303,1406,1495,1591,1710,1833,1941,2068,2191,2318,2477,2604,2727,2847,2966,3056,3156,3274,3407,3502,3608,3715,3838,3968,4076,4172,4251,4348,4444,4555,4644,4728,4835,4915,4998,5097,5195,5290,5389,5475,5576,5674,5776,5892,5972,6081", "endColumns": "118,114,106,116,100,94,111,136,119,140,83,102,88,95,118,122,107,126,122,126,158,126,122,119,118,89,99,117,132,94,105,106,122,129,107,95,78,96,95,110,88,83,106,79,82,98,97,94,98,85,100,97,101,115,79,108,103", "endOffsets": "169,284,391,508,609,704,816,953,1073,1214,1298,1401,1490,1586,1705,1828,1936,2063,2186,2313,2472,2599,2722,2842,2961,3051,3151,3269,3402,3497,3603,3710,3833,3963,4071,4167,4246,4343,4439,4550,4639,4723,4830,4910,4993,5092,5190,5285,5384,5470,5571,5669,5771,5887,5967,6076,6180"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8443,8562,8677,8784,8901,9002,9097,9209,9346,9466,9607,9691,9794,9883,9979,10098,10221,10329,10456,10579,10706,10865,10992,11115,11235,11354,11444,11544,11662,11795,11890,11996,12103,12226,12356,12464,12560,12639,12736,12832,12943,13032,13116,13223,13303,13386,13485,13583,13678,13777,13863,13964,14062,14164,14280,14360,14469", "endColumns": "118,114,106,116,100,94,111,136,119,140,83,102,88,95,118,122,107,126,122,126,158,126,122,119,118,89,99,117,132,94,105,106,122,129,107,95,78,96,95,110,88,83,106,79,82,98,97,94,98,85,100,97,101,115,79,108,103", "endOffsets": "8557,8672,8779,8896,8997,9092,9204,9341,9461,9602,9686,9789,9878,9974,10093,10216,10324,10451,10574,10701,10860,10987,11110,11230,11349,11439,11539,11657,11790,11885,11991,12098,12221,12351,12459,12555,12634,12731,12827,12938,13027,13111,13218,13298,13381,13480,13578,13673,13772,13858,13959,14057,14159,14275,14355,14464,14568"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,506,623,735,831,923,1039,1154,1252,1368,1507,1633,1758,1864,1957,2082,2171,2276,2381,2486,2580,2691,2813,2928,3042,3156,3269,3379,3494,3596,3703,3790,3876,3976,4113,4264", "endColumns": "106,100,96,95,116,111,95,91,115,114,97,115,138,125,124,105,92,124,88,104,104,104,93,110,121,114,113,113,112,109,114,101,106,86,85,99,136,150,91", "endOffsets": "207,308,405,501,618,730,826,918,1034,1149,1247,1363,1502,1628,1753,1859,1952,2077,2166,2271,2376,2481,2575,2686,2808,2923,3037,3151,3264,3374,3489,3591,3698,3785,3871,3971,4108,4259,4351"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4284,4391,4492,4589,4685,4802,4914,5010,5102,5218,5333,5431,5547,5686,5812,5937,6043,6136,6261,6350,6455,6560,6665,6759,6870,6992,7107,7221,7335,7448,7558,7673,7775,7882,7969,8055,8155,8292,14839", "endColumns": "106,100,96,95,116,111,95,91,115,114,97,115,138,125,124,105,92,124,88,104,104,104,93,110,121,114,113,113,112,109,114,101,106,86,85,99,136,150,91", "endOffsets": "4386,4487,4584,4680,4797,4909,5005,5097,5213,5328,5426,5542,5681,5807,5932,6038,6131,6256,6345,6450,6555,6660,6754,6865,6987,7102,7216,7330,7443,7553,7668,7770,7877,7964,8050,8150,8287,8438,14926"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-vi/values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14573", "endColumns": "90", "endOffsets": "14659"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-vi/values-vi.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "185,281,367,473,573,665,750,843,937,1018,1108,1199,1271,1358,1434,1511,1587,1664,1730", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,86,75,76,75,76,65,113", "endOffsets": "276,362,468,568,660,745,838,932,1013,1103,1194,1266,1353,1429,1506,1582,1659,1725,1839"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3626,3722,3808,3914,4014,4106,4191,14664,14758,14931,15021,15196,15268,15355,15431,15508,15685,15762,15828", "endColumns": "95,85,105,99,91,84,92,93,80,89,90,71,86,75,76,75,76,65,113", "endOffsets": "3717,3803,3909,4009,4101,4186,4279,14753,14834,15016,15107,15263,15350,15426,15503,15579,15757,15823,15937"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-vi/values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,327,436,520,623,742,819,896,987,1080,1176,1270,1371,1464,1559,1657,1748,1839,1923,2027,2136,2237,2342,2456,2561,2718,2817", "endColumns": "113,107,108,83,102,118,76,76,90,92,95,93,100,92,94,97,90,90,83,103,108,100,104,113,104,156,98,83", "endOffsets": "214,322,431,515,618,737,814,891,982,1075,1171,1265,1366,1459,1554,1652,1743,1834,1918,2022,2131,2232,2337,2451,2556,2713,2812,2896"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,327,436,520,623,742,819,896,987,1080,1176,1270,1371,1464,1559,1657,1748,1839,1923,2027,2136,2237,2342,2456,2561,2718,15112", "endColumns": "113,107,108,83,102,118,76,76,90,92,95,93,100,92,94,97,90,90,83,103,108,100,104,113,104,156,98,83", "endOffsets": "214,322,431,515,618,737,814,891,982,1075,1171,1265,1366,1459,1554,1652,1743,1834,1918,2022,2131,2232,2337,2451,2556,2713,2812,15191"}}]}]}