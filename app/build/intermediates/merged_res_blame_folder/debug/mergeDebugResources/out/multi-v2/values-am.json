{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-am/values-am.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,412,498,601,714,792,870,961,1054,1147,1241,1342,1435,1530,1624,1715,1805,1884,1984,2084,2180,2283,2382,2489,2642,2738", "endColumns": "101,98,105,85,102,112,77,77,90,92,92,93,100,92,94,93,90,89,78,99,99,95,102,98,106,152,95,78", "endOffsets": "202,301,407,493,596,709,787,865,956,1049,1142,1236,1337,1430,1525,1619,1710,1800,1879,1979,2079,2175,2278,2377,2484,2637,2733,2812"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,412,498,601,714,792,870,961,1054,1147,1241,1342,1435,1530,1624,1715,1805,1884,1984,2084,2180,2283,2382,2489,2642,14473", "endColumns": "101,98,105,85,102,112,77,77,90,92,92,93,100,92,94,93,90,89,78,99,99,95,102,98,106,152,95,78", "endOffsets": "202,301,407,493,596,709,787,865,956,1049,1142,1236,1337,1430,1525,1619,1710,1800,1879,1979,2079,2175,2278,2377,2484,2637,2733,14547"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2810,2903,3003,3100,3199,3295,3397,14923", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "2898,2998,3095,3194,3290,3392,3492,15019"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,405,496,612,717,812,900,1026,1149,1251,1371,1497,1613,1726,1830,1923,2042,2131,2230,2331,2429,2521,2631,2743,2850,2954,3055,3158,3259,3366,3465,3570,3657,3739,3834,3968,4115", "endColumns": "106,100,91,90,115,104,94,87,125,122,101,119,125,115,112,103,92,118,88,98,100,97,91,109,111,106,103,100,102,100,106,98,104,86,81,94,133,146,85", "endOffsets": "207,308,400,491,607,712,807,895,1021,1144,1246,1366,1492,1608,1721,1825,1918,2037,2126,2225,2326,2424,2516,2626,2738,2845,2949,3050,3153,3254,3361,3460,3565,3652,3734,3829,3963,4110,4196"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4088,4195,4296,4388,4479,4595,4700,4795,4883,5009,5132,5234,5354,5480,5596,5709,5813,5906,6025,6114,6213,6314,6412,6504,6614,6726,6833,6937,7038,7141,7242,7349,7448,7553,7640,7722,7817,7951,14228", "endColumns": "106,100,91,90,115,104,94,87,125,122,101,119,125,115,112,103,92,118,88,98,100,97,91,109,111,106,103,100,102,100,106,98,104,86,81,94,133,146,85", "endOffsets": "4190,4291,4383,4474,4590,4695,4790,4878,5004,5127,5229,5349,5475,5591,5704,5808,5901,6020,6109,6208,6309,6407,6499,6609,6721,6828,6932,7033,7136,7237,7344,7443,7548,7635,7717,7812,7946,8093,14309"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-am/values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,276,384,491,585,675,782,910,1020,1149,1231,1329,1416,1509,1619,1738,1841,1964,2089,2213,2361,2477,2590,2704,2819,2907,3002,3112,3231,3326,3428,3530,3650,3776,3880,3976,4050,4143,4235,4334,4418,4503,4605,4686,4769,4869,4966,5061,5156,5241,5343,5442,5541,5659,5740,5841", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,98,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "161,271,379,486,580,670,777,905,1015,1144,1226,1324,1411,1504,1614,1733,1836,1959,2084,2208,2356,2472,2585,2699,2814,2902,2997,3107,3226,3321,3423,3525,3645,3771,3875,3971,4045,4138,4230,4329,4413,4498,4600,4681,4764,4864,4961,5056,5151,5236,5338,5437,5536,5654,5735,5836,5933"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8098,8209,8319,8427,8534,8628,8718,8825,8953,9063,9192,9274,9372,9459,9552,9662,9781,9884,10007,10132,10256,10404,10520,10633,10747,10862,10950,11045,11155,11274,11369,11471,11573,11693,11819,11923,12019,12093,12186,12278,12377,12461,12546,12648,12729,12812,12912,13009,13104,13199,13284,13386,13485,13584,13702,13783,13884", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,98,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "8204,8314,8422,8529,8623,8713,8820,8948,9058,9187,9269,9367,9454,9547,9657,9776,9879,10002,10127,10251,10399,10515,10628,10742,10857,10945,11040,11150,11269,11364,11466,11568,11688,11814,11918,12014,12088,12181,12273,12372,12456,12541,12643,12724,12807,12907,13004,13099,13194,13279,13381,13480,13579,13697,13778,13879,13976"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-am/values-am.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,127,214", "endColumns": "71,86,85", "endOffsets": "122,209,295"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2738,15285,15372", "endColumns": "71,86,85", "endOffsets": "2805,15367,15453"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-am/values-am.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "178,261,338,430,526,608,686,769,851,929,1007,1088,1158,1241,1314,1387,1459,1539,1604", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,82,72,72,71,79,64,115", "endOffsets": "256,333,425,521,603,681,764,846,924,1002,1083,1153,1236,1309,1382,1454,1534,1599,1715"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3497,3580,3657,3749,3845,3927,4005,14068,14150,14314,14392,14552,14622,14705,14778,14851,15024,15104,15169", "endColumns": "82,76,91,95,81,77,82,81,77,77,80,69,82,72,72,71,79,64,115", "endOffsets": "3575,3652,3744,3840,3922,4000,4083,14145,14223,14387,14468,14617,14700,14773,14846,14918,15099,15164,15280"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-am/values-am.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "86", "endOffsets": "137"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "13981", "endColumns": "86", "endOffsets": "14063"}}]}]}