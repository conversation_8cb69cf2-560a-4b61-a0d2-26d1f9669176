{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-ml/values-ml.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,342,457,550,655,787,864,940,1031,1124,1226,1320,1421,1515,1610,1709,1800,1891,1973,2084,2190,2288,2402,2502,2613,2772,2873", "endColumns": "118,117,114,92,104,131,76,75,90,92,101,93,100,93,94,98,90,90,81,110,105,97,113,99,110,158,100,81", "endOffsets": "219,337,452,545,650,782,859,935,1026,1119,1221,1315,1416,1510,1605,1704,1795,1886,1968,2079,2185,2283,2397,2497,2608,2767,2868,2950"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,342,457,550,655,787,864,940,1031,1124,1226,1320,1421,1515,1610,1709,1800,1891,1973,2084,2190,2288,2402,2502,2613,2772,15914", "endColumns": "118,117,114,92,104,131,76,75,90,92,101,93,100,93,94,98,90,90,81,110,105,97,113,99,110,158,100,81", "endOffsets": "219,337,452,545,650,782,859,935,1026,1119,1221,1315,1416,1510,1605,1704,1795,1886,1968,2079,2185,2283,2397,2497,2608,2767,2868,15991"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-ml/values-ml.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "193,288,375,474,578,668,754,855,942,1030,1116,1203,1281,1373,1450,1524,1597,1673,1740", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,91,76,73,72,75,66,118", "endOffsets": "283,370,469,573,663,749,850,937,1025,1111,1198,1276,1368,1445,1519,1592,1668,1735,1854"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3697,3792,3879,3978,4082,4172,4258,15473,15560,15741,15827,15996,16074,16166,16243,16317,16491,16567,16634", "endColumns": "94,86,98,103,89,85,100,86,87,85,86,77,91,76,73,72,75,66,118", "endOffsets": "3787,3874,3973,4077,4167,4253,4354,15555,15643,15822,15909,16069,16161,16238,16312,16385,16562,16629,16748"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,142,230", "endColumns": "86,87,91", "endOffsets": "137,225,317"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2873,16753,16841", "endColumns": "86,87,91", "endOffsets": "2955,16836,16928"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,510,638,748,848,943,1088,1232,1351,1488,1655,1791,1926,2039,2147,2297,2394,2504,2617,2725,2830,2949,3074,3207,3339,3449,3566,3689,3812,3935,4058,4145,4229,4338,4473,4633", "endColumns": "106,100,94,101,127,109,99,94,144,143,118,136,166,135,134,112,107,149,96,109,112,107,104,118,124,132,131,109,116,122,122,122,122,86,83,108,134,159,92", "endOffsets": "207,308,403,505,633,743,843,938,1083,1227,1346,1483,1650,1786,1921,2034,2142,2292,2389,2499,2612,2720,2825,2944,3069,3202,3334,3444,3561,3684,3807,3930,4053,4140,4224,4333,4468,4628,4721"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4359,4466,4567,4662,4764,4892,5002,5102,5197,5342,5486,5605,5742,5909,6045,6180,6293,6401,6551,6648,6758,6871,6979,7084,7203,7328,7461,7593,7703,7820,7943,8066,8189,8312,8399,8483,8592,8727,15648", "endColumns": "106,100,94,101,127,109,99,94,144,143,118,136,166,135,134,112,107,149,96,109,112,107,104,118,124,132,131,109,116,122,122,122,122,86,83,108,134,159,92", "endOffsets": "4461,4562,4657,4759,4887,4997,5097,5192,5337,5481,5600,5737,5904,6040,6175,6288,6396,6546,6643,6753,6866,6974,7079,7198,7323,7456,7588,7698,7815,7938,8061,8184,8307,8394,8478,8587,8722,8882,15736"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,301,427,550,650,744,855,1007,1125,1282,1367,1472,1572,1674,1797,1930,2040,2176,2318,2449,2653,2787,2911,3041,3175,3276,3374,3492,3623,3722,3824,3937,4075,4221,4335,4444,4520,4618,4718,4832,4919,5016,5124,5204,5292,5390,5503,5598,5709,5799,5914,6016,6129,6261,6341,6448", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "170,296,422,545,645,739,850,1002,1120,1277,1362,1467,1567,1669,1792,1925,2035,2171,2313,2444,2648,2782,2906,3036,3170,3271,3369,3487,3618,3717,3819,3932,4070,4216,4330,4439,4515,4613,4713,4827,4914,5011,5119,5199,5287,5385,5498,5593,5704,5794,5909,6011,6124,6256,6336,6443,6540"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8887,9007,9133,9259,9382,9482,9576,9687,9839,9957,10114,10199,10304,10404,10506,10629,10762,10872,11008,11150,11281,11485,11619,11743,11873,12007,12108,12206,12324,12455,12554,12656,12769,12907,13053,13167,13276,13352,13450,13550,13664,13751,13848,13956,14036,14124,14222,14335,14430,14541,14631,14746,14848,14961,15093,15173,15280", "endColumns": "119,125,125,122,99,93,110,151,117,156,84,104,99,101,122,132,109,135,141,130,203,133,123,129,133,100,97,117,130,98,101,112,137,145,113,108,75,97,99,113,86,96,107,79,87,97,112,94,110,89,114,101,112,131,79,106,96", "endOffsets": "9002,9128,9254,9377,9477,9571,9682,9834,9952,10109,10194,10299,10399,10501,10624,10757,10867,11003,11145,11276,11480,11614,11738,11868,12002,12103,12201,12319,12450,12549,12651,12764,12902,13048,13162,13271,13347,13445,13545,13659,13746,13843,13951,14031,14119,14217,14330,14425,14536,14626,14741,14843,14956,15088,15168,15275,15372"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-ml/values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2960,3062,3165,3267,3371,3474,3575,16390", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "3057,3160,3262,3366,3469,3570,3692,16486"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-ml/values-ml.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "95", "endOffsets": "146"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15377", "endColumns": "95", "endOffsets": "15468"}}]}]}