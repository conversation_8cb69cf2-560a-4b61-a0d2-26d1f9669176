{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-iw/values-iw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,319,412,504,626,734,829,927,1046,1164,1266,1390,1532,1650,1767,1873,1963,2088,2178,2281,2389,2495,2592,2710,2813,2920,3027,3135,3249,3372,3496,3613,3731,3818,3901,4015,4152,4304", "endColumns": "109,103,92,91,121,107,94,97,118,117,101,123,141,117,116,105,89,124,89,102,107,105,96,117,102,106,106,107,113,122,123,116,117,86,82,113,136,151,87", "endOffsets": "210,314,407,499,621,729,824,922,1041,1159,1261,1385,1527,1645,1762,1868,1958,2083,2173,2276,2384,2490,2587,2705,2808,2915,3022,3130,3244,3367,3491,3608,3726,3813,3896,4010,4147,4299,4387"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4134,4244,4348,4441,4533,4655,4763,4858,4956,5075,5193,5295,5419,5561,5679,5796,5902,5992,6117,6207,6310,6418,6524,6621,6739,6842,6949,7056,7164,7278,7401,7525,7642,7760,7847,7930,8044,8181,14675", "endColumns": "109,103,92,91,121,107,94,97,118,117,101,123,141,117,116,105,89,124,89,102,107,105,96,117,102,106,106,107,113,122,123,116,117,86,82,113,136,151,87", "endOffsets": "4239,4343,4436,4528,4650,4758,4853,4951,5070,5188,5290,5414,5556,5674,5791,5897,5987,6112,6202,6305,6413,6519,6616,6734,6837,6944,7051,7159,7273,7396,7520,7637,7755,7842,7925,8039,8176,8328,14758"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,288,406,521,620,715,828,955,1070,1210,1295,1393,1484,1580,1697,1817,1920,2056,2191,2312,2465,2583,2693,2808,2926,3018,3115,3227,3351,3449,3548,3652,3786,3927,4034,4134,4215,4320,4424,4534,4620,4705,4808,4888,4972,5073,5172,5263,5358,5444,5546,5645,5742,5867,5947,6048", "endColumns": "116,115,117,114,98,94,112,126,114,139,84,97,90,95,116,119,102,135,134,120,152,117,109,114,117,91,96,111,123,97,98,103,133,140,106,99,80,104,103,109,85,84,102,79,83,100,98,90,94,85,101,98,96,124,79,100,95", "endOffsets": "167,283,401,516,615,710,823,950,1065,1205,1290,1388,1479,1575,1692,1812,1915,2051,2186,2307,2460,2578,2688,2803,2921,3013,3110,3222,3346,3444,3543,3647,3781,3922,4029,4129,4210,4315,4419,4529,4615,4700,4803,4883,4967,5068,5167,5258,5353,5439,5541,5640,5737,5862,5942,6043,6139"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8333,8450,8566,8684,8799,8898,8993,9106,9233,9348,9488,9573,9671,9762,9858,9975,10095,10198,10334,10469,10590,10743,10861,10971,11086,11204,11296,11393,11505,11629,11727,11826,11930,12064,12205,12312,12412,12493,12598,12702,12812,12898,12983,13086,13166,13250,13351,13450,13541,13636,13722,13824,13923,14020,14145,14225,14326", "endColumns": "116,115,117,114,98,94,112,126,114,139,84,97,90,95,116,119,102,135,134,120,152,117,109,114,117,91,96,111,123,97,98,103,133,140,106,99,80,104,103,109,85,84,102,79,83,100,98,90,94,85,101,98,96,124,79,100,95", "endOffsets": "8445,8561,8679,8794,8893,8988,9101,9228,9343,9483,9568,9666,9757,9853,9970,10090,10193,10329,10464,10585,10738,10856,10966,11081,11199,11291,11388,11500,11624,11722,11821,11925,12059,12200,12307,12407,12488,12593,12697,12807,12893,12978,13081,13161,13245,13346,13445,13536,13631,13717,13819,13918,14015,14140,14220,14321,14417"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,135,222", "endColumns": "79,86,87", "endOffsets": "130,217,305"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2751,15750,15837", "endColumns": "79,86,87", "endOffsets": "2826,15832,15920"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,308,416,500,600,714,792,870,961,1055,1151,1245,1346,1439,1534,1631,1722,1814,1895,1997,2101,2199,2302,2403,2503,2655,2751", "endColumns": "103,98,107,83,99,113,77,77,90,93,95,93,100,92,94,96,90,91,80,101,103,97,102,100,99,151,95,80", "endOffsets": "204,303,411,495,595,709,787,865,956,1050,1146,1240,1341,1434,1529,1626,1717,1809,1890,1992,2096,2194,2297,2398,2498,2650,2746,2827"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,308,416,500,600,714,792,870,961,1055,1151,1245,1346,1439,1534,1631,1722,1814,1895,1997,2101,2199,2302,2403,2503,2655,14923", "endColumns": "103,98,107,83,99,113,77,77,90,93,95,93,100,92,94,96,90,91,80,101,103,97,102,100,99,151,95,80", "endOffsets": "204,303,411,495,595,709,787,865,956,1050,1146,1240,1341,1434,1529,1626,1717,1809,1890,1992,2096,2194,2297,2398,2498,2650,2746,14999"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2831,2925,3027,3124,3221,3322,3422,15385", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "2920,3022,3119,3216,3317,3417,3523,15481"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-iw/values-iw.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "186,275,359,452,547,630,707,792,878,957,1035,1117,1186,1270,1344,1422,1498,1572,1643", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,83,73,77,75,73,70,118", "endOffsets": "270,354,447,542,625,702,787,873,952,1030,1112,1181,1265,1339,1417,1493,1567,1638,1757"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3528,3617,3701,3794,3889,3972,4049,14510,14596,14763,14841,15004,15073,15157,15231,15309,15486,15560,15631", "endColumns": "88,83,92,94,82,76,84,85,78,77,81,68,83,73,77,75,73,70,118", "endOffsets": "3612,3696,3789,3884,3967,4044,4129,14591,14670,14836,14918,15068,15152,15226,15304,15380,15555,15626,15745"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-iw/values-iw.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "87", "endOffsets": "138"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "14422", "endColumns": "87", "endOffsets": "14505"}}]}]}