{"logs": [{"outputFile": "com.google.chuangke.app-mergeDebugResources-61:/values-kn/values-kn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/958b5f6474cad75df0616f65956716cb/transformed/foundation-release/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,129,217", "endColumns": "73,87,94", "endOffsets": "124,212,307"}, "to": {"startLines": "29,155,156", "startColumns": "4,4,4", "startOffsets": "2866,16458,16546", "endColumns": "73,87,94", "endOffsets": "2935,16541,16636"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5160d2cfecf781584320b1c761a0dddc/transformed/core-1.16.0/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "30,31,32,33,34,35,36,151", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2940,3038,3141,3242,3348,3449,3557,16093", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3033,3136,3237,3343,3444,3552,3680,16189"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/82a05c0bd10eec0234413cd77e6c785f/transformed/material3-release/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,425,548,647,745,860,1017,1147,1299,1385,1491,1587,1689,1805,1938,2049,2188,2323,2456,2634,2758,2876,2997,3124,3221,3318,3440,3578,3684,3793,3899,4038,4183,4293,4402,4478,4578,4678,4794,4881,4970,5081,5161,5245,5345,5453,5553,5654,5741,5854,5956,6061,6182,6262,6372", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,115,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "174,298,420,543,642,740,855,1012,1142,1294,1380,1486,1582,1684,1800,1933,2044,2183,2318,2451,2629,2753,2871,2992,3119,3216,3313,3435,3573,3679,3788,3894,4033,4178,4288,4397,4473,4573,4673,4789,4876,4965,5076,5156,5240,5340,5448,5548,5649,5736,5849,5951,6056,6177,6257,6367,6464"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8675,8799,8923,9045,9168,9267,9365,9480,9637,9767,9919,10005,10111,10207,10309,10425,10558,10669,10808,10943,11076,11254,11378,11496,11617,11744,11841,11938,12060,12198,12304,12413,12519,12658,12803,12913,13022,13098,13198,13298,13414,13501,13590,13701,13781,13865,13965,14073,14173,14274,14361,14474,14576,14681,14802,14882,14992", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,115,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "8794,8918,9040,9163,9262,9360,9475,9632,9762,9914,10000,10106,10202,10304,10420,10553,10664,10803,10938,11071,11249,11373,11491,11612,11739,11836,11933,12055,12193,12299,12408,12514,12653,12798,12908,13017,13093,13193,13293,13409,13496,13585,13696,13776,13860,13960,14068,14168,14269,14356,14469,14571,14676,14797,14877,14987,15084"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/002e17bbfcb70d61eb34c460d61e4989/transformed/leanback-1.2.0/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,511,635,749,846,941,1084,1223,1331,1457,1601,1737,1869,1980,2070,2206,2295,2408,2525,2635,2729,2840,2959,3087,3211,3318,3429,3544,3662,3774,3889,3976,4060,4160,4295,4452", "endColumns": "106,100,98,98,123,113,96,94,142,138,107,125,143,135,131,110,89,135,88,112,116,109,93,110,118,127,123,106,110,114,117,111,114,86,83,99,134,156,90", "endOffsets": "207,308,407,506,630,744,841,936,1079,1218,1326,1452,1596,1732,1864,1975,2065,2201,2290,2403,2520,2630,2724,2835,2954,3082,3206,3313,3424,3539,3657,3769,3884,3971,4055,4155,4290,4447,4538"}, "to": {"startLines": "44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4328,4435,4536,4635,4734,4858,4972,5069,5164,5307,5446,5554,5680,5824,5960,6092,6203,6293,6429,6518,6631,6748,6858,6952,7063,7182,7310,7434,7541,7652,7767,7885,7997,8112,8199,8283,8383,8518,15356", "endColumns": "106,100,98,98,123,113,96,94,142,138,107,125,143,135,131,110,89,135,88,112,116,109,93,110,118,127,123,106,110,114,117,111,114,86,83,99,134,156,90", "endOffsets": "4430,4531,4630,4729,4853,4967,5064,5159,5302,5441,5549,5675,5819,5955,6087,6198,6288,6424,6513,6626,6743,6853,6947,7058,7177,7305,7429,7536,7647,7762,7880,7992,8107,8194,8278,8378,8513,8670,15442"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b032eb6a6dffd056dc34dacb3ca857cf/transformed/material-release/res/values-kn/values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "90", "endOffsets": "141"}, "to": {"startLines": "139", "startColumns": "4", "startOffsets": "15089", "endColumns": "90", "endOffsets": "15175"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/6211b832f06b784c04c6c3927869d674/transformed/ui-release/res/values-kn/values-kn.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "180,277,361,457,557,646,730,823,914,999,1081,1167,1246,1333,1408,1486,1563,1640,1709", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,86,74,77,76,76,68,117", "endOffsets": "272,356,452,552,641,725,818,909,994,1076,1162,1241,1328,1403,1481,1558,1635,1704,1822"}, "to": {"startLines": "37,38,39,40,41,42,43,140,141,143,144,146,147,148,149,150,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3685,3782,3866,3962,4062,4151,4235,15180,15271,15447,15529,15697,15776,15863,15938,16016,16194,16271,16340", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,86,74,77,76,76,68,117", "endOffsets": "3777,3861,3957,4057,4146,4230,4323,15266,15351,15524,15610,15771,15858,15933,16011,16088,16266,16335,16453"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1ab9a5f17051217fb5e1b56ef2a5bb82/transformed/appcompat-1.0.0/res/values-kn/values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,335,448,536,643,770,847,924,1015,1108,1204,1298,1399,1492,1587,1681,1772,1863,1945,2061,2172,2271,2384,2488,2602,2766,2866", "endColumns": "117,111,112,87,106,126,76,76,90,92,95,93,100,92,94,93,90,90,81,115,110,98,112,103,113,163,99,81", "endOffsets": "218,330,443,531,638,765,842,919,1010,1103,1199,1293,1394,1487,1582,1676,1767,1858,1940,2056,2167,2266,2379,2483,2597,2761,2861,2943"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,335,448,536,643,770,847,924,1015,1108,1204,1298,1399,1492,1587,1681,1772,1863,1945,2061,2172,2271,2384,2488,2602,2766,15615", "endColumns": "117,111,112,87,106,126,76,76,90,92,95,93,100,92,94,93,90,90,81,115,110,98,112,103,113,163,99,81", "endOffsets": "218,330,443,531,638,765,842,919,1010,1103,1199,1293,1394,1487,1582,1676,1767,1858,1940,2056,2167,2266,2379,2483,2597,2761,2861,15692"}}]}]}