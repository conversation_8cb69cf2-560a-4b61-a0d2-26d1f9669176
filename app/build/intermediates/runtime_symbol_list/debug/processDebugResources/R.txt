int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim lb_decelerator_2 0x7f01000c
int anim lb_decelerator_4 0x7f01000d
int animator lb_guidedactions_item_pressed 0x7f020000
int animator lb_guidedactions_item_unpressed 0x7f020001
int animator lb_guidedstep_slide_down 0x7f020002
int animator lb_guidedstep_slide_up 0x7f020003
int animator lb_onboarding_description_enter 0x7f020004
int animator lb_onboarding_logo_enter 0x7f020005
int animator lb_onboarding_logo_exit 0x7f020006
int animator lb_onboarding_page_indicator_enter 0x7f020007
int animator lb_onboarding_page_indicator_fade_in 0x7f020008
int animator lb_onboarding_page_indicator_fade_out 0x7f020009
int animator lb_onboarding_start_button_fade_in 0x7f02000a
int animator lb_onboarding_start_button_fade_out 0x7f02000b
int animator lb_onboarding_title_enter 0x7f02000c
int animator lb_playback_bg_fade_in 0x7f02000d
int animator lb_playback_bg_fade_out 0x7f02000e
int animator lb_playback_controls_fade_in 0x7f02000f
int animator lb_playback_controls_fade_out 0x7f020010
int animator lb_playback_description_fade_in 0x7f020011
int animator lb_playback_description_fade_out 0x7f020012
int animator lb_playback_rows_fade_in 0x7f020013
int animator lb_playback_rows_fade_out 0x7f020014
int attr action 0x7f030000
int attr actionBarDivider 0x7f030001
int attr actionBarItemBackground 0x7f030002
int attr actionBarPopupTheme 0x7f030003
int attr actionBarSize 0x7f030004
int attr actionBarSplitStyle 0x7f030005
int attr actionBarStyle 0x7f030006
int attr actionBarTabBarStyle 0x7f030007
int attr actionBarTabStyle 0x7f030008
int attr actionBarTabTextStyle 0x7f030009
int attr actionBarTheme 0x7f03000a
int attr actionBarWidgetTheme 0x7f03000b
int attr actionButtonStyle 0x7f03000c
int attr actionDropDownStyle 0x7f03000d
int attr actionLayout 0x7f03000e
int attr actionMenuTextAppearance 0x7f03000f
int attr actionMenuTextColor 0x7f030010
int attr actionModeBackground 0x7f030011
int attr actionModeCloseButtonStyle 0x7f030012
int attr actionModeCloseDrawable 0x7f030013
int attr actionModeCopyDrawable 0x7f030014
int attr actionModeCutDrawable 0x7f030015
int attr actionModeFindDrawable 0x7f030016
int attr actionModePasteDrawable 0x7f030017
int attr actionModePopupWindowStyle 0x7f030018
int attr actionModeSelectAllDrawable 0x7f030019
int attr actionModeShareDrawable 0x7f03001a
int attr actionModeSplitBackground 0x7f03001b
int attr actionModeStyle 0x7f03001c
int attr actionModeWebSearchDrawable 0x7f03001d
int attr actionOverflowButtonStyle 0x7f03001e
int attr actionOverflowMenuStyle 0x7f03001f
int attr actionProviderClass 0x7f030020
int attr actionViewClass 0x7f030021
int attr activatedAnimationDuration 0x7f030022
int attr activityChooserViewStyle 0x7f030023
int attr alertDialogButtonGroupStyle 0x7f030024
int attr alertDialogCenterButtons 0x7f030025
int attr alertDialogStyle 0x7f030026
int attr alertDialogTheme 0x7f030027
int attr allowStacking 0x7f030028
int attr alpha 0x7f030029
int attr alphabeticModifiers 0x7f03002a
int attr argType 0x7f03002b
int attr arrowBgColor 0x7f03002c
int attr arrowColor 0x7f03002d
int attr arrowHeadLength 0x7f03002e
int attr arrowRadius 0x7f03002f
int attr arrowShaftLength 0x7f030030
int attr autoCompleteTextViewStyle 0x7f030031
int attr autoSizeMaxTextSize 0x7f030032
int attr autoSizeMinTextSize 0x7f030033
int attr autoSizePresetSizes 0x7f030034
int attr autoSizeStepGranularity 0x7f030035
int attr autoSizeTextType 0x7f030036
int attr background 0x7f030037
int attr backgroundSplit 0x7f030038
int attr backgroundStacked 0x7f030039
int attr backgroundTint 0x7f03003a
int attr backgroundTintMode 0x7f03003b
int attr barLength 0x7f03003c
int attr baseCardViewStyle 0x7f03003d
int attr borderlessButtonStyle 0x7f03003e
int attr browsePaddingBottom 0x7f03003f
int attr browsePaddingEnd 0x7f030040
int attr browsePaddingStart 0x7f030041
int attr browsePaddingTop 0x7f030042
int attr browseRowsFadingEdgeLength 0x7f030043
int attr browseRowsMarginStart 0x7f030044
int attr browseRowsMarginTop 0x7f030045
int attr browseTitleIconStyle 0x7f030046
int attr browseTitleTextStyle 0x7f030047
int attr browseTitleViewLayout 0x7f030048
int attr browseTitleViewStyle 0x7f030049
int attr buttonBarButtonStyle 0x7f03004a
int attr buttonBarNegativeButtonStyle 0x7f03004b
int attr buttonBarNeutralButtonStyle 0x7f03004c
int attr buttonBarPositiveButtonStyle 0x7f03004d
int attr buttonBarStyle 0x7f03004e
int attr buttonGravity 0x7f03004f
int attr buttonIconDimen 0x7f030050
int attr buttonPanelSideLayout 0x7f030051
int attr buttonStyle 0x7f030052
int attr buttonStyleSmall 0x7f030053
int attr buttonTint 0x7f030054
int attr buttonTintMode 0x7f030055
int attr cardBackground 0x7f030056
int attr cardForeground 0x7f030057
int attr cardType 0x7f030058
int attr checkboxStyle 0x7f030059
int attr checkedTextViewStyle 0x7f03005a
int attr closeIcon 0x7f03005b
int attr closeItemLayout 0x7f03005c
int attr closed_captioning 0x7f03005d
int attr collapseContentDescription 0x7f03005e
int attr collapseIcon 0x7f03005f
int attr color 0x7f030060
int attr colorAccent 0x7f030061
int attr colorBackgroundFloating 0x7f030062
int attr colorButtonNormal 0x7f030063
int attr colorControlActivated 0x7f030064
int attr colorControlHighlight 0x7f030065
int attr colorControlNormal 0x7f030066
int attr colorError 0x7f030067
int attr colorPrimary 0x7f030068
int attr colorPrimaryDark 0x7f030069
int attr colorSwitchThumbNormal 0x7f03006a
int attr columnCount 0x7f03006b
int attr columnWidth 0x7f03006c
int attr commitIcon 0x7f03006d
int attr contentDescription 0x7f03006e
int attr contentInsetEnd 0x7f03006f
int attr contentInsetEndWithActions 0x7f030070
int attr contentInsetLeft 0x7f030071
int attr contentInsetRight 0x7f030072
int attr contentInsetStart 0x7f030073
int attr contentInsetStartWithNavigation 0x7f030074
int attr controlBackground 0x7f030075
int attr coordinatorLayoutStyle 0x7f030076
int attr customNavigationLayout 0x7f030077
int attr data 0x7f030078
int attr dataPattern 0x7f030079
int attr datePickerFormat 0x7f03007a
int attr datePickerStyle 0x7f03007b
int attr defaultBrandColor 0x7f03007c
int attr defaultBrandColorDark 0x7f03007d
int attr defaultQueryHint 0x7f03007e
int attr defaultSearchBrightColor 0x7f03007f
int attr defaultSearchColor 0x7f030080
int attr defaultSearchIcon 0x7f030081
int attr defaultSearchIconColor 0x7f030082
int attr defaultSectionHeaderColor 0x7f030083
int attr destination 0x7f030084
int attr detailsActionButtonStyle 0x7f030085
int attr detailsDescriptionBodyStyle 0x7f030086
int attr detailsDescriptionSubtitleStyle 0x7f030087
int attr detailsDescriptionTitleStyle 0x7f030088
int attr dialogCornerRadius 0x7f030089
int attr dialogPreferredPadding 0x7f03008a
int attr dialogTheme 0x7f03008b
int attr displayOptions 0x7f03008c
int attr divider 0x7f03008d
int attr dividerHorizontal 0x7f03008e
int attr dividerPadding 0x7f03008f
int attr dividerVertical 0x7f030090
int attr dotBgColor 0x7f030091
int attr dotToArrowGap 0x7f030092
int attr dotToDotGap 0x7f030093
int attr drawableSize 0x7f030094
int attr drawerArrowStyle 0x7f030095
int attr dropDownListViewStyle 0x7f030096
int attr dropdownListPreferredItemHeight 0x7f030097
int attr editTextBackground 0x7f030098
int attr editTextColor 0x7f030099
int attr editTextStyle 0x7f03009a
int attr elevation 0x7f03009b
int attr enterAnim 0x7f03009c
int attr errorMessageStyle 0x7f03009d
int attr exitAnim 0x7f03009e
int attr expandActivityOverflowButtonDrawable 0x7f03009f
int attr extraVisibility 0x7f0300a0
int attr fastScrollEnabled 0x7f0300a1
int attr fastScrollHorizontalThumbDrawable 0x7f0300a2
int attr fastScrollHorizontalTrackDrawable 0x7f0300a3
int attr fastScrollVerticalThumbDrawable 0x7f0300a4
int attr fastScrollVerticalTrackDrawable 0x7f0300a5
int attr fast_forward 0x7f0300a6
int attr firstBaselineToTopHeight 0x7f0300a7
int attr focusOutEnd 0x7f0300a8
int attr focusOutFront 0x7f0300a9
int attr focusOutSideEnd 0x7f0300aa
int attr focusOutSideStart 0x7f0300ab
int attr font 0x7f0300ac
int attr fontFamily 0x7f0300ad
int attr fontProviderAuthority 0x7f0300ae
int attr fontProviderCerts 0x7f0300af
int attr fontProviderFallbackQuery 0x7f0300b0
int attr fontProviderFetchStrategy 0x7f0300b1
int attr fontProviderFetchTimeout 0x7f0300b2
int attr fontProviderPackage 0x7f0300b3
int attr fontProviderQuery 0x7f0300b4
int attr fontProviderSystemFontFamily 0x7f0300b5
int attr fontStyle 0x7f0300b6
int attr fontVariationSettings 0x7f0300b7
int attr fontWeight 0x7f0300b8
int attr gapBetweenBars 0x7f0300b9
int attr goIcon 0x7f0300ba
int attr graph 0x7f0300bb
int attr guidanceBreadcrumbStyle 0x7f0300bc
int attr guidanceContainerStyle 0x7f0300bd
int attr guidanceDescriptionStyle 0x7f0300be
int attr guidanceEntryAnimation 0x7f0300bf
int attr guidanceIconStyle 0x7f0300c0
int attr guidanceTitleStyle 0x7f0300c1
int attr guidedActionCheckedAnimation 0x7f0300c2
int attr guidedActionContentWidth 0x7f0300c3
int attr guidedActionContentWidthNoIcon 0x7f0300c4
int attr guidedActionContentWidthWeight 0x7f0300c5
int attr guidedActionContentWidthWeightTwoPanels 0x7f0300c6
int attr guidedActionDescriptionMinLines 0x7f0300c7
int attr guidedActionDisabledChevronAlpha 0x7f0300c8
int attr guidedActionEnabledChevronAlpha 0x7f0300c9
int attr guidedActionItemCheckmarkStyle 0x7f0300ca
int attr guidedActionItemChevronStyle 0x7f0300cb
int attr guidedActionItemContainerStyle 0x7f0300cc
int attr guidedActionItemContentStyle 0x7f0300cd
int attr guidedActionItemDescriptionStyle 0x7f0300ce
int attr guidedActionItemIconStyle 0x7f0300cf
int attr guidedActionItemTitleStyle 0x7f0300d0
int attr guidedActionPressedAnimation 0x7f0300d1
int attr guidedActionTitleMaxLines 0x7f0300d2
int attr guidedActionTitleMinLines 0x7f0300d3
int attr guidedActionUncheckedAnimation 0x7f0300d4
int attr guidedActionUnpressedAnimation 0x7f0300d5
int attr guidedActionVerticalPadding 0x7f0300d6
int attr guidedActionsBackground 0x7f0300d7
int attr guidedActionsBackgroundDark 0x7f0300d8
int attr guidedActionsContainerStyle 0x7f0300d9
int attr guidedActionsElevation 0x7f0300da
int attr guidedActionsEntryAnimation 0x7f0300db
int attr guidedActionsListStyle 0x7f0300dc
int attr guidedActionsSelectorDrawable 0x7f0300dd
int attr guidedActionsSelectorHideAnimation 0x7f0300de
int attr guidedActionsSelectorShowAnimation 0x7f0300df
int attr guidedActionsSelectorStyle 0x7f0300e0
int attr guidedActionsShadowWidth 0x7f0300e1
int attr guidedButtonActionsListStyle 0x7f0300e2
int attr guidedButtonActionsWidthWeight 0x7f0300e3
int attr guidedStepBackground 0x7f0300e4
int attr guidedStepEntryAnimation 0x7f0300e5
int attr guidedStepExitAnimation 0x7f0300e6
int attr guidedStepHeightWeight 0x7f0300e7
int attr guidedStepImeAppearingAnimation 0x7f0300e8
int attr guidedStepImeDisappearingAnimation 0x7f0300e9
int attr guidedStepKeyline 0x7f0300ea
int attr guidedStepReentryAnimation 0x7f0300eb
int attr guidedStepReturnAnimation 0x7f0300ec
int attr guidedStepTheme 0x7f0300ed
int attr guidedStepThemeFlag 0x7f0300ee
int attr guidedSubActionsListStyle 0x7f0300ef
int attr headerStyle 0x7f0300f0
int attr headersVerticalGridStyle 0x7f0300f1
int attr height 0x7f0300f2
int attr hideOnContentScroll 0x7f0300f3
int attr high_quality 0x7f0300f4
int attr homeAsUpIndicator 0x7f0300f5
int attr homeLayout 0x7f0300f6
int attr horizontalMargin 0x7f0300f7
int attr icon 0x7f0300f8
int attr iconTint 0x7f0300f9
int attr iconTintMode 0x7f0300fa
int attr iconifiedByDefault 0x7f0300fb
int attr imageButtonStyle 0x7f0300fc
int attr imageCardViewBadgeStyle 0x7f0300fd
int attr imageCardViewContentStyle 0x7f0300fe
int attr imageCardViewImageStyle 0x7f0300ff
int attr imageCardViewInfoAreaStyle 0x7f030100
int attr imageCardViewStyle 0x7f030101
int attr imageCardViewTitleStyle 0x7f030102
int attr indeterminateProgressStyle 0x7f030103
int attr infoAreaBackground 0x7f030104
int attr infoVisibility 0x7f030105
int attr initialActivityCount 0x7f030106
int attr is24HourFormat 0x7f030107
int attr isLightTheme 0x7f030108
int attr itemPadding 0x7f030109
int attr itemsVerticalGridStyle 0x7f03010a
int attr keylines 0x7f03010b
int attr lStar 0x7f03010c
int attr lastBaselineToBottomHeight 0x7f03010d
int attr launchSingleTop 0x7f03010e
int attr layout 0x7f03010f
int attr layoutManager 0x7f030110
int attr layout_anchor 0x7f030111
int attr layout_anchorGravity 0x7f030112
int attr layout_behavior 0x7f030113
int attr layout_dodgeInsetEdges 0x7f030114
int attr layout_insetEdge 0x7f030115
int attr layout_keyline 0x7f030116
int attr layout_viewType 0x7f030117
int attr lbDotRadius 0x7f030118
int attr lbImageCardViewType 0x7f030119
int attr lb_slideEdge 0x7f03011a
int attr lineHeight 0x7f03011b
int attr listChoiceBackgroundIndicator 0x7f03011c
int attr listDividerAlertDialog 0x7f03011d
int attr listItemLayout 0x7f03011e
int attr listLayout 0x7f03011f
int attr listMenuViewStyle 0x7f030120
int attr listPopupWindowStyle 0x7f030121
int attr listPreferredItemHeight 0x7f030122
int attr listPreferredItemHeightLarge 0x7f030123
int attr listPreferredItemHeightSmall 0x7f030124
int attr listPreferredItemPaddingLeft 0x7f030125
int attr listPreferredItemPaddingRight 0x7f030126
int attr logo 0x7f030127
int attr logoDescription 0x7f030128
int attr maintainLineSpacing 0x7f030129
int attr maxButtonHeight 0x7f03012a
int attr measureWithLargestChild 0x7f03012b
int attr mimeType 0x7f03012c
int attr multiChoiceItemLayout 0x7f03012d
int attr navGraph 0x7f03012e
int attr navigationContentDescription 0x7f03012f
int attr navigationIcon 0x7f030130
int attr navigationMode 0x7f030131
int attr nestedScrollViewStyle 0x7f030132
int attr nullable 0x7f030133
int attr numberOfColumns 0x7f030134
int attr numberOfRows 0x7f030135
int attr numericModifiers 0x7f030136
int attr onboardingDescriptionStyle 0x7f030137
int attr onboardingHeaderStyle 0x7f030138
int attr onboardingLogoStyle 0x7f030139
int attr onboardingMainIconStyle 0x7f03013a
int attr onboardingNavigatorContainerStyle 0x7f03013b
int attr onboardingPageIndicatorStyle 0x7f03013c
int attr onboardingStartButtonStyle 0x7f03013d
int attr onboardingTheme 0x7f03013e
int attr onboardingTitleStyle 0x7f03013f
int attr overlapAnchor 0x7f030140
int attr overlayDimActiveLevel 0x7f030141
int attr overlayDimDimmedLevel 0x7f030142
int attr overlayDimMaskColor 0x7f030143
int attr paddingBottomNoButtons 0x7f030144
int attr paddingEnd 0x7f030145
int attr paddingStart 0x7f030146
int attr paddingTopNoTitle 0x7f030147
int attr panelBackground 0x7f030148
int attr panelMenuListTheme 0x7f030149
int attr panelMenuListWidth 0x7f03014a
int attr pause 0x7f03014b
int attr pickerItemLayout 0x7f03014c
int attr pickerItemTextViewId 0x7f03014d
int attr pickerStyle 0x7f03014e
int attr picture_in_picture 0x7f03014f
int attr pinPickerStyle 0x7f030150
int attr play 0x7f030151
int attr playbackControlButtonLabelStyle 0x7f030152
int attr playbackControlsActionIcons 0x7f030153
int attr playbackControlsAutoHideTickleTimeout 0x7f030154
int attr playbackControlsAutoHideTimeout 0x7f030155
int attr playbackControlsButtonStyle 0x7f030156
int attr playbackControlsIconHighlightColor 0x7f030157
int attr playbackControlsTimeStyle 0x7f030158
int attr playbackMediaItemDetailsStyle 0x7f030159
int attr playbackMediaItemDurationStyle 0x7f03015a
int attr playbackMediaItemNameStyle 0x7f03015b
int attr playbackMediaItemNumberStyle 0x7f03015c
int attr playbackMediaItemNumberViewFlipperLayout 0x7f03015d
int attr playbackMediaItemNumberViewFlipperStyle 0x7f03015e
int attr playbackMediaItemPaddingStart 0x7f03015f
int attr playbackMediaItemRowStyle 0x7f030160
int attr playbackMediaItemSeparatorStyle 0x7f030161
int attr playbackMediaListHeaderStyle 0x7f030162
int attr playbackMediaListHeaderTitleStyle 0x7f030163
int attr playbackPaddingEnd 0x7f030164
int attr playbackPaddingStart 0x7f030165
int attr playbackProgressPrimaryColor 0x7f030166
int attr playbackProgressSecondaryColor 0x7f030167
int attr popEnterAnim 0x7f030168
int attr popExitAnim 0x7f030169
int attr popUpTo 0x7f03016a
int attr popUpToInclusive 0x7f03016b
int attr popUpToSaveState 0x7f03016c
int attr popupMenuStyle 0x7f03016d
int attr popupTheme 0x7f03016e
int attr popupWindowStyle 0x7f03016f
int attr preserveIconSpacing 0x7f030170
int attr progressBarPadding 0x7f030171
int attr progressBarStyle 0x7f030172
int attr queryBackground 0x7f030173
int attr queryHint 0x7f030174
int attr queryPatterns 0x7f030175
int attr radioButtonStyle 0x7f030176
int attr ratingBarStyle 0x7f030177
int attr ratingBarStyleIndicator 0x7f030178
int attr ratingBarStyleSmall 0x7f030179
int attr recyclerViewStyle 0x7f03017a
int attr repeat 0x7f03017b
int attr repeat_one 0x7f03017c
int attr resizeTrigger 0x7f03017d
int attr resizedPaddingAdjustmentBottom 0x7f03017e
int attr resizedPaddingAdjustmentTop 0x7f03017f
int attr resizedTextSize 0x7f030180
int attr restoreState 0x7f030181
int attr reverseLayout 0x7f030182
int attr rewind 0x7f030183
int attr route 0x7f030184
int attr rowHeaderDescriptionStyle 0x7f030185
int attr rowHeaderDockStyle 0x7f030186
int attr rowHeaderStyle 0x7f030187
int attr rowHeight 0x7f030188
int attr rowHorizontalGridStyle 0x7f030189
int attr rowHoverCardDescriptionStyle 0x7f03018a
int attr rowHoverCardTitleStyle 0x7f03018b
int attr rowsVerticalGridStyle 0x7f03018c
int attr searchHintIcon 0x7f03018d
int attr searchIcon 0x7f03018e
int attr searchOrbBrightColor 0x7f03018f
int attr searchOrbColor 0x7f030190
int attr searchOrbIcon 0x7f030191
int attr searchOrbIconColor 0x7f030192
int attr searchOrbViewStyle 0x7f030193
int attr searchViewStyle 0x7f030194
int attr sectionHeaderStyle 0x7f030195
int attr seekBarStyle 0x7f030196
int attr selectableItemBackground 0x7f030197
int attr selectableItemBackgroundBorderless 0x7f030198
int attr selectedAnimationDelay 0x7f030199
int attr selectedAnimationDuration 0x7f03019a
int attr shortcutMatchRequired 0x7f03019b
int attr showAsAction 0x7f03019c
int attr showDividers 0x7f03019d
int attr showText 0x7f03019e
int attr showTitle 0x7f03019f
int attr shuffle 0x7f0301a0
int attr singleChoiceItemLayout 0x7f0301a1
int attr skip_next 0x7f0301a2
int attr skip_previous 0x7f0301a3
int attr spanCount 0x7f0301a4
int attr spinBars 0x7f0301a5
int attr spinnerDropDownItemStyle 0x7f0301a6
int attr spinnerStyle 0x7f0301a7
int attr splitTrack 0x7f0301a8
int attr srcCompat 0x7f0301a9
int attr stackFromEnd 0x7f0301aa
int attr startDestination 0x7f0301ab
int attr state_above_anchor 0x7f0301ac
int attr statusBarBackground 0x7f0301ad
int attr subMenuArrow 0x7f0301ae
int attr submitBackground 0x7f0301af
int attr subtitle 0x7f0301b0
int attr subtitleTextAppearance 0x7f0301b1
int attr subtitleTextColor 0x7f0301b2
int attr subtitleTextStyle 0x7f0301b3
int attr suggestionRowLayout 0x7f0301b4
int attr switchMinWidth 0x7f0301b5
int attr switchPadding 0x7f0301b6
int attr switchStyle 0x7f0301b7
int attr switchTextAppearance 0x7f0301b8
int attr targetPackage 0x7f0301b9
int attr textAllCaps 0x7f0301ba
int attr textAppearanceLargePopupMenu 0x7f0301bb
int attr textAppearanceListItem 0x7f0301bc
int attr textAppearanceListItemSecondary 0x7f0301bd
int attr textAppearanceListItemSmall 0x7f0301be
int attr textAppearancePopupMenuHeader 0x7f0301bf
int attr textAppearanceSearchResultSubtitle 0x7f0301c0
int attr textAppearanceSearchResultTitle 0x7f0301c1
int attr textAppearanceSmallPopupMenu 0x7f0301c2
int attr textColorAlertDialogListItem 0x7f0301c3
int attr textColorSearchUrl 0x7f0301c4
int attr theme 0x7f0301c5
int attr thickness 0x7f0301c6
int attr thumbTextPadding 0x7f0301c7
int attr thumbTint 0x7f0301c8
int attr thumbTintMode 0x7f0301c9
int attr thumb_down 0x7f0301ca
int attr thumb_down_outline 0x7f0301cb
int attr thumb_up 0x7f0301cc
int attr thumb_up_outline 0x7f0301cd
int attr tickMark 0x7f0301ce
int attr tickMarkTint 0x7f0301cf
int attr tickMarkTintMode 0x7f0301d0
int attr timePickerStyle 0x7f0301d1
int attr tint 0x7f0301d2
int attr tintMode 0x7f0301d3
int attr title 0x7f0301d4
int attr titleMargin 0x7f0301d5
int attr titleMarginBottom 0x7f0301d6
int attr titleMarginEnd 0x7f0301d7
int attr titleMarginStart 0x7f0301d8
int attr titleMarginTop 0x7f0301d9
int attr titleMargins 0x7f0301da
int attr titleTextAppearance 0x7f0301db
int attr titleTextColor 0x7f0301dc
int attr titleTextStyle 0x7f0301dd
int attr toolbarNavigationButtonStyle 0x7f0301de
int attr toolbarStyle 0x7f0301df
int attr tooltipForegroundColor 0x7f0301e0
int attr tooltipFrameBackground 0x7f0301e1
int attr tooltipText 0x7f0301e2
int attr track 0x7f0301e3
int attr trackTint 0x7f0301e4
int attr trackTintMode 0x7f0301e5
int attr ttcIndex 0x7f0301e6
int attr uri 0x7f0301e7
int attr useCurrentTime 0x7f0301e8
int attr verticalMargin 0x7f0301e9
int attr viewInflaterClass 0x7f0301ea
int attr voiceIcon 0x7f0301eb
int attr windowActionBar 0x7f0301ec
int attr windowActionBarOverlay 0x7f0301ed
int attr windowActionModeOverlay 0x7f0301ee
int attr windowFixedHeightMajor 0x7f0301ef
int attr windowFixedHeightMinor 0x7f0301f0
int attr windowFixedWidthMajor 0x7f0301f1
int attr windowFixedWidthMinor 0x7f0301f2
int attr windowMinWidthMajor 0x7f0301f3
int attr windowMinWidthMinor 0x7f0301f4
int attr windowNoTitle 0x7f0301f5
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_allow_stacked_button_bar 0x7f040001
int bool abc_config_actionMenuItemAllCaps 0x7f040002
int color abc_background_cache_hint_selector_material_dark 0x7f050000
int color abc_background_cache_hint_selector_material_light 0x7f050001
int color abc_btn_colored_borderless_text_material 0x7f050002
int color abc_btn_colored_text_material 0x7f050003
int color abc_color_highlight_material 0x7f050004
int color abc_hint_foreground_material_dark 0x7f050005
int color abc_hint_foreground_material_light 0x7f050006
int color abc_input_method_navigation_guard 0x7f050007
int color abc_primary_text_disable_only_material_dark 0x7f050008
int color abc_primary_text_disable_only_material_light 0x7f050009
int color abc_primary_text_material_dark 0x7f05000a
int color abc_primary_text_material_light 0x7f05000b
int color abc_search_url_text 0x7f05000c
int color abc_search_url_text_normal 0x7f05000d
int color abc_search_url_text_pressed 0x7f05000e
int color abc_search_url_text_selected 0x7f05000f
int color abc_secondary_text_material_dark 0x7f050010
int color abc_secondary_text_material_light 0x7f050011
int color abc_tint_btn_checkable 0x7f050012
int color abc_tint_default 0x7f050013
int color abc_tint_edittext 0x7f050014
int color abc_tint_seek_thumb 0x7f050015
int color abc_tint_spinner 0x7f050016
int color abc_tint_switch_track 0x7f050017
int color accent_material_dark 0x7f050018
int color accent_material_light 0x7f050019
int color androidx_core_ripple_material_light 0x7f05001a
int color androidx_core_secondary_text_default_material_light 0x7f05001b
int color background_floating_material_dark 0x7f05001c
int color background_floating_material_light 0x7f05001d
int color background_material_dark 0x7f05001e
int color background_material_light 0x7f05001f
int color bright_foreground_disabled_material_dark 0x7f050020
int color bright_foreground_disabled_material_light 0x7f050021
int color bright_foreground_inverse_material_dark 0x7f050022
int color bright_foreground_inverse_material_light 0x7f050023
int color bright_foreground_material_dark 0x7f050024
int color bright_foreground_material_light 0x7f050025
int color button_material_dark 0x7f050026
int color button_material_light 0x7f050027
int color call_notification_answer_color 0x7f050028
int color call_notification_decline_color 0x7f050029
int color dim_foreground_disabled_material_dark 0x7f05002a
int color dim_foreground_disabled_material_light 0x7f05002b
int color dim_foreground_material_dark 0x7f05002c
int color dim_foreground_material_light 0x7f05002d
int color error_color_material_dark 0x7f05002e
int color error_color_material_light 0x7f05002f
int color foreground_material_dark 0x7f050030
int color foreground_material_light 0x7f050031
int color highlighted_text_material_dark 0x7f050032
int color highlighted_text_material_light 0x7f050033
int color lb_action_text_color 0x7f050034
int color lb_background_protection 0x7f050035
int color lb_basic_card_bg_color 0x7f050036
int color lb_basic_card_content_text_color 0x7f050037
int color lb_basic_card_info_bg_color 0x7f050038
int color lb_basic_card_title_text_color 0x7f050039
int color lb_browse_header_color 0x7f05003a
int color lb_browse_header_description_color 0x7f05003b
int color lb_browse_title_color 0x7f05003c
int color lb_control_button_color 0x7f05003d
int color lb_control_button_text 0x7f05003e
int color lb_default_brand_color 0x7f05003f
int color lb_default_brand_color_dark 0x7f050040
int color lb_default_search_color 0x7f050041
int color lb_default_search_icon_color 0x7f050042
int color lb_details_description_body_color 0x7f050043
int color lb_details_description_color 0x7f050044
int color lb_details_overview_bg_color 0x7f050045
int color lb_error_background_color_opaque 0x7f050046
int color lb_error_background_color_translucent 0x7f050047
int color lb_error_message 0x7f050048
int color lb_grey 0x7f050049
int color lb_guidedactions_background 0x7f05004a
int color lb_guidedactions_background_dark 0x7f05004b
int color lb_guidedactions_item_unselected_text_color 0x7f05004c
int color lb_list_item_unselected_text_color 0x7f05004d
int color lb_media_background_color 0x7f05004e
int color lb_page_indicator_arrow_background 0x7f05004f
int color lb_page_indicator_arrow_shadow 0x7f050050
int color lb_page_indicator_dot 0x7f050051
int color lb_playback_background_progress_color 0x7f050052
int color lb_playback_controls_background_dark 0x7f050053
int color lb_playback_controls_background_light 0x7f050054
int color lb_playback_controls_time_text_color 0x7f050055
int color lb_playback_icon_highlight_no_theme 0x7f050056
int color lb_playback_media_row_highlight_color 0x7f050057
int color lb_playback_media_row_separator_highlight_color 0x7f050058
int color lb_playback_now_playing_bar_color 0x7f050059
int color lb_playback_progress_color_no_theme 0x7f05005a
int color lb_playback_progress_secondary_color_no_theme 0x7f05005b
int color lb_playback_secondary_progress_color 0x7f05005c
int color lb_search_bar_hint 0x7f05005d
int color lb_search_bar_hint_speech_mode 0x7f05005e
int color lb_search_bar_text 0x7f05005f
int color lb_search_bar_text_speech_mode 0x7f050060
int color lb_search_plate_hint_text_color 0x7f050061
int color lb_speech_orb_not_recording 0x7f050062
int color lb_speech_orb_not_recording_icon 0x7f050063
int color lb_speech_orb_not_recording_pulsed 0x7f050064
int color lb_speech_orb_recording 0x7f050065
int color lb_tv_white 0x7f050066
int color lb_view_dim_mask_color 0x7f050067
int color material_blue_grey_800 0x7f050068
int color material_blue_grey_900 0x7f050069
int color material_blue_grey_950 0x7f05006a
int color material_deep_teal_200 0x7f05006b
int color material_deep_teal_500 0x7f05006c
int color material_grey_100 0x7f05006d
int color material_grey_300 0x7f05006e
int color material_grey_50 0x7f05006f
int color material_grey_600 0x7f050070
int color material_grey_800 0x7f050071
int color material_grey_850 0x7f050072
int color material_grey_900 0x7f050073
int color md_theme_background 0x7f050074
int color md_theme_error 0x7f050075
int color md_theme_errorContainer 0x7f050076
int color md_theme_inverseOnSurface 0x7f050077
int color md_theme_inversePrimary 0x7f050078
int color md_theme_inverseSurface 0x7f050079
int color md_theme_onBackground 0x7f05007a
int color md_theme_onError 0x7f05007b
int color md_theme_onErrorContainer 0x7f05007c
int color md_theme_onPrimary 0x7f05007d
int color md_theme_onPrimaryContainer 0x7f05007e
int color md_theme_onSecondary 0x7f05007f
int color md_theme_onSecondaryContainer 0x7f050080
int color md_theme_onSurface 0x7f050081
int color md_theme_onSurfaceVariant 0x7f050082
int color md_theme_onTertiary 0x7f050083
int color md_theme_onTertiaryContainer 0x7f050084
int color md_theme_outline 0x7f050085
int color md_theme_outlineVariant 0x7f050086
int color md_theme_primary 0x7f050087
int color md_theme_primaryContainer 0x7f050088
int color md_theme_secondary 0x7f050089
int color md_theme_secondaryContainer 0x7f05008a
int color md_theme_surface 0x7f05008b
int color md_theme_surfaceVariant 0x7f05008c
int color md_theme_tertiary 0x7f05008d
int color md_theme_tertiaryContainer 0x7f05008e
int color notification_action_color_filter 0x7f05008f
int color notification_icon_bg_color 0x7f050090
int color notification_material_background_media_default_color 0x7f050091
int color primary_dark_material_dark 0x7f050092
int color primary_dark_material_light 0x7f050093
int color primary_material_dark 0x7f050094
int color primary_material_light 0x7f050095
int color primary_text_default_material_dark 0x7f050096
int color primary_text_default_material_light 0x7f050097
int color primary_text_disabled_material_dark 0x7f050098
int color primary_text_disabled_material_light 0x7f050099
int color ripple_material_dark 0x7f05009a
int color ripple_material_light 0x7f05009b
int color secondary_text_default_material_dark 0x7f05009c
int color secondary_text_default_material_light 0x7f05009d
int color secondary_text_disabled_material_dark 0x7f05009e
int color secondary_text_disabled_material_light 0x7f05009f
int color switch_thumb_disabled_material_dark 0x7f0500a0
int color switch_thumb_disabled_material_light 0x7f0500a1
int color switch_thumb_material_dark 0x7f0500a2
int color switch_thumb_material_light 0x7f0500a3
int color switch_thumb_normal_material_dark 0x7f0500a4
int color switch_thumb_normal_material_light 0x7f0500a5
int color tooltip_background_dark 0x7f0500a6
int color tooltip_background_light 0x7f0500a7
int color vector_tint_color 0x7f0500a8
int color vector_tint_theme_color 0x7f0500a9
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_padding_horizontal_material 0x7f060030
int dimen abc_panel_menu_list_width 0x7f060031
int dimen abc_progress_bar_height_material 0x7f060032
int dimen abc_search_view_preferred_height 0x7f060033
int dimen abc_search_view_preferred_width 0x7f060034
int dimen abc_seekbar_track_background_height_material 0x7f060035
int dimen abc_seekbar_track_progress_height_material 0x7f060036
int dimen abc_select_dialog_padding_start_material 0x7f060037
int dimen abc_switch_padding 0x7f060038
int dimen abc_text_size_body_1_material 0x7f060039
int dimen abc_text_size_body_2_material 0x7f06003a
int dimen abc_text_size_button_material 0x7f06003b
int dimen abc_text_size_caption_material 0x7f06003c
int dimen abc_text_size_display_1_material 0x7f06003d
int dimen abc_text_size_display_2_material 0x7f06003e
int dimen abc_text_size_display_3_material 0x7f06003f
int dimen abc_text_size_display_4_material 0x7f060040
int dimen abc_text_size_headline_material 0x7f060041
int dimen abc_text_size_large_material 0x7f060042
int dimen abc_text_size_medium_material 0x7f060043
int dimen abc_text_size_menu_header_material 0x7f060044
int dimen abc_text_size_menu_material 0x7f060045
int dimen abc_text_size_small_material 0x7f060046
int dimen abc_text_size_subhead_material 0x7f060047
int dimen abc_text_size_subtitle_material_toolbar 0x7f060048
int dimen abc_text_size_title_material 0x7f060049
int dimen abc_text_size_title_material_toolbar 0x7f06004a
int dimen compat_button_inset_horizontal_material 0x7f06004b
int dimen compat_button_inset_vertical_material 0x7f06004c
int dimen compat_button_padding_horizontal_material 0x7f06004d
int dimen compat_button_padding_vertical_material 0x7f06004e
int dimen compat_control_corner_material 0x7f06004f
int dimen compat_notification_large_icon_max_height 0x7f060050
int dimen compat_notification_large_icon_max_width 0x7f060051
int dimen disabled_alpha_material_dark 0x7f060052
int dimen disabled_alpha_material_light 0x7f060053
int dimen fastscroll_default_thickness 0x7f060054
int dimen fastscroll_margin 0x7f060055
int dimen fastscroll_minimum_range 0x7f060056
int dimen highlight_alpha_material_colored 0x7f060057
int dimen highlight_alpha_material_dark 0x7f060058
int dimen highlight_alpha_material_light 0x7f060059
int dimen hint_alpha_material_dark 0x7f06005a
int dimen hint_alpha_material_light 0x7f06005b
int dimen hint_pressed_alpha_material_dark 0x7f06005c
int dimen hint_pressed_alpha_material_light 0x7f06005d
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f06005e
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f06005f
int dimen item_touch_helper_swipe_escape_velocity 0x7f060060
int dimen lb_action_1_line_height 0x7f060061
int dimen lb_action_2_lines_height 0x7f060062
int dimen lb_action_button_corner_radius 0x7f060063
int dimen lb_action_icon_margin 0x7f060064
int dimen lb_action_padding_horizontal 0x7f060065
int dimen lb_action_text_size 0x7f060066
int dimen lb_action_with_icon_padding_end 0x7f060067
int dimen lb_action_with_icon_padding_start 0x7f060068
int dimen lb_basic_card_content_text_size 0x7f060069
int dimen lb_basic_card_info_badge_margin 0x7f06006a
int dimen lb_basic_card_info_badge_size 0x7f06006b
int dimen lb_basic_card_info_height 0x7f06006c
int dimen lb_basic_card_info_height_no_content 0x7f06006d
int dimen lb_basic_card_info_padding_bottom 0x7f06006e
int dimen lb_basic_card_info_padding_horizontal 0x7f06006f
int dimen lb_basic_card_info_padding_top 0x7f060070
int dimen lb_basic_card_info_text_margin 0x7f060071
int dimen lb_basic_card_main_height 0x7f060072
int dimen lb_basic_card_main_width 0x7f060073
int dimen lb_basic_card_title_text_size 0x7f060074
int dimen lb_browse_expanded_row_no_hovercard_bottom_padding 0x7f060075
int dimen lb_browse_expanded_selected_row_top_padding 0x7f060076
int dimen lb_browse_header_description_text_size 0x7f060077
int dimen lb_browse_header_fading_length 0x7f060078
int dimen lb_browse_header_height 0x7f060079
int dimen lb_browse_header_padding_end 0x7f06007a
int dimen lb_browse_header_select_duration 0x7f06007b
int dimen lb_browse_header_select_scale 0x7f06007c
int dimen lb_browse_header_text_size 0x7f06007d
int dimen lb_browse_headers_vertical_spacing 0x7f06007e
int dimen lb_browse_headers_width 0x7f06007f
int dimen lb_browse_headers_z 0x7f060080
int dimen lb_browse_item_horizontal_spacing 0x7f060081
int dimen lb_browse_item_vertical_spacing 0x7f060082
int dimen lb_browse_padding_bottom 0x7f060083
int dimen lb_browse_padding_end 0x7f060084
int dimen lb_browse_padding_start 0x7f060085
int dimen lb_browse_padding_top 0x7f060086
int dimen lb_browse_row_hovercard_description_font_size 0x7f060087
int dimen lb_browse_row_hovercard_max_width 0x7f060088
int dimen lb_browse_row_hovercard_title_font_size 0x7f060089
int dimen lb_browse_rows_fading_edge 0x7f06008a
int dimen lb_browse_rows_margin_start 0x7f06008b
int dimen lb_browse_rows_margin_top 0x7f06008c
int dimen lb_browse_section_header_text_size 0x7f06008d
int dimen lb_browse_selected_row_top_padding 0x7f06008e
int dimen lb_browse_title_height 0x7f06008f
int dimen lb_browse_title_icon_height 0x7f060090
int dimen lb_browse_title_icon_max_width 0x7f060091
int dimen lb_browse_title_text_size 0x7f060092
int dimen lb_control_button_diameter 0x7f060093
int dimen lb_control_button_height 0x7f060094
int dimen lb_control_button_secondary_diameter 0x7f060095
int dimen lb_control_button_secondary_height 0x7f060096
int dimen lb_control_button_text_size 0x7f060097
int dimen lb_control_icon_height 0x7f060098
int dimen lb_control_icon_width 0x7f060099
int dimen lb_details_cover_drawable_parallax_movement 0x7f06009a
int dimen lb_details_description_body_line_spacing 0x7f06009b
int dimen lb_details_description_body_text_size 0x7f06009c
int dimen lb_details_description_subtitle_text_size 0x7f06009d
int dimen lb_details_description_title_baseline 0x7f06009e
int dimen lb_details_description_title_line_spacing 0x7f06009f
int dimen lb_details_description_title_padding_adjust_bottom 0x7f0600a0
int dimen lb_details_description_title_padding_adjust_top 0x7f0600a1
int dimen lb_details_description_title_resized_text_size 0x7f0600a2
int dimen lb_details_description_title_text_size 0x7f0600a3
int dimen lb_details_description_under_subtitle_baseline_margin 0x7f0600a4
int dimen lb_details_description_under_title_baseline_margin 0x7f0600a5
int dimen lb_details_overview_action_items_spacing 0x7f0600a6
int dimen lb_details_overview_action_select_duration 0x7f0600a7
int dimen lb_details_overview_actions_fade_size 0x7f0600a8
int dimen lb_details_overview_actions_height 0x7f0600a9
int dimen lb_details_overview_actions_padding_end 0x7f0600aa
int dimen lb_details_overview_actions_padding_start 0x7f0600ab
int dimen lb_details_overview_description_margin_bottom 0x7f0600ac
int dimen lb_details_overview_description_margin_end 0x7f0600ad
int dimen lb_details_overview_description_margin_start 0x7f0600ae
int dimen lb_details_overview_description_margin_top 0x7f0600af
int dimen lb_details_overview_height_large 0x7f0600b0
int dimen lb_details_overview_height_small 0x7f0600b1
int dimen lb_details_overview_image_margin_horizontal 0x7f0600b2
int dimen lb_details_overview_image_margin_vertical 0x7f0600b3
int dimen lb_details_overview_margin_bottom 0x7f0600b4
int dimen lb_details_overview_margin_end 0x7f0600b5
int dimen lb_details_overview_margin_start 0x7f0600b6
int dimen lb_details_overview_z 0x7f0600b7
int dimen lb_details_rows_align_top 0x7f0600b8
int dimen lb_details_v2_actions_height 0x7f0600b9
int dimen lb_details_v2_align_pos_for_actions 0x7f0600ba
int dimen lb_details_v2_align_pos_for_description 0x7f0600bb
int dimen lb_details_v2_blank_height 0x7f0600bc
int dimen lb_details_v2_card_height 0x7f0600bd
int dimen lb_details_v2_description_margin_end 0x7f0600be
int dimen lb_details_v2_description_margin_start 0x7f0600bf
int dimen lb_details_v2_description_margin_top 0x7f0600c0
int dimen lb_details_v2_left 0x7f0600c1
int dimen lb_details_v2_logo_margin_start 0x7f0600c2
int dimen lb_details_v2_logo_max_height 0x7f0600c3
int dimen lb_details_v2_logo_max_width 0x7f0600c4
int dimen lb_error_image_max_height 0x7f0600c5
int dimen lb_error_message_max_width 0x7f0600c6
int dimen lb_error_message_text_size 0x7f0600c7
int dimen lb_error_under_image_baseline_margin 0x7f0600c8
int dimen lb_error_under_message_baseline_margin 0x7f0600c9
int dimen lb_guidedactions_elevation 0x7f0600ca
int dimen lb_guidedactions_item_bottom_padding 0x7f0600cb
int dimen lb_guidedactions_item_checkmark_diameter 0x7f0600cc
int dimen lb_guidedactions_item_delimiter_padding 0x7f0600cd
int dimen lb_guidedactions_item_description_font_size 0x7f0600ce
int dimen lb_guidedactions_item_disabled_chevron_alpha 0x7f0600cf
int dimen lb_guidedactions_item_disabled_description_text_alpha 0x7f0600d0
int dimen lb_guidedactions_item_disabled_text_alpha 0x7f0600d1
int dimen lb_guidedactions_item_enabled_chevron_alpha 0x7f0600d2
int dimen lb_guidedactions_item_end_padding 0x7f0600d3
int dimen lb_guidedactions_item_icon_height 0x7f0600d4
int dimen lb_guidedactions_item_icon_width 0x7f0600d5
int dimen lb_guidedactions_item_space_between_title_and_description 0x7f0600d6
int dimen lb_guidedactions_item_start_padding 0x7f0600d7
int dimen lb_guidedactions_item_text_width 0x7f0600d8
int dimen lb_guidedactions_item_text_width_no_icon 0x7f0600d9
int dimen lb_guidedactions_item_title_font_size 0x7f0600da
int dimen lb_guidedactions_item_top_padding 0x7f0600db
int dimen lb_guidedactions_item_unselected_description_text_alpha 0x7f0600dc
int dimen lb_guidedactions_item_unselected_text_alpha 0x7f0600dd
int dimen lb_guidedactions_list_padding_end 0x7f0600de
int dimen lb_guidedactions_list_padding_start 0x7f0600df
int dimen lb_guidedactions_list_vertical_spacing 0x7f0600e0
int dimen lb_guidedactions_section_shadow_width 0x7f0600e1
int dimen lb_guidedactions_sublist_bottom_margin 0x7f0600e2
int dimen lb_guidedactions_sublist_padding_bottom 0x7f0600e3
int dimen lb_guidedactions_sublist_padding_top 0x7f0600e4
int dimen lb_guidedactions_vertical_padding 0x7f0600e5
int dimen lb_guidedactions_width_weight 0x7f0600e6
int dimen lb_guidedactions_width_weight_two_panels 0x7f0600e7
int dimen lb_guidedbuttonactions_width_weight 0x7f0600e8
int dimen lb_guidedstep_height_weight 0x7f0600e9
int dimen lb_guidedstep_height_weight_translucent 0x7f0600ea
int dimen lb_guidedstep_keyline 0x7f0600eb
int dimen lb_guidedstep_slide_ime_distance 0x7f0600ec
int dimen lb_list_row_height 0x7f0600ed
int dimen lb_material_shadow_details_z 0x7f0600ee
int dimen lb_material_shadow_focused_z 0x7f0600ef
int dimen lb_material_shadow_normal_z 0x7f0600f0
int dimen lb_onboarding_content_margin_bottom 0x7f0600f1
int dimen lb_onboarding_content_margin_top 0x7f0600f2
int dimen lb_onboarding_content_width 0x7f0600f3
int dimen lb_onboarding_header_height 0x7f0600f4
int dimen lb_onboarding_header_margin_top 0x7f0600f5
int dimen lb_onboarding_navigation_height 0x7f0600f6
int dimen lb_onboarding_start_button_height 0x7f0600f7
int dimen lb_onboarding_start_button_margin_bottom 0x7f0600f8
int dimen lb_onboarding_start_button_translation_offset 0x7f0600f9
int dimen lb_page_indicator_arrow_gap 0x7f0600fa
int dimen lb_page_indicator_arrow_radius 0x7f0600fb
int dimen lb_page_indicator_arrow_shadow_offset 0x7f0600fc
int dimen lb_page_indicator_arrow_shadow_radius 0x7f0600fd
int dimen lb_page_indicator_dot_gap 0x7f0600fe
int dimen lb_page_indicator_dot_radius 0x7f0600ff
int dimen lb_playback_controls_card_height 0x7f060100
int dimen lb_playback_controls_child_margin_bigger 0x7f060101
int dimen lb_playback_controls_child_margin_biggest 0x7f060102
int dimen lb_playback_controls_child_margin_default 0x7f060103
int dimen lb_playback_controls_margin_bottom 0x7f060104
int dimen lb_playback_controls_margin_end 0x7f060105
int dimen lb_playback_controls_margin_start 0x7f060106
int dimen lb_playback_controls_padding_bottom 0x7f060107
int dimen lb_playback_controls_time_text_size 0x7f060108
int dimen lb_playback_controls_z 0x7f060109
int dimen lb_playback_current_time_margin_start 0x7f06010a
int dimen lb_playback_description_margin_end 0x7f06010b
int dimen lb_playback_description_margin_start 0x7f06010c
int dimen lb_playback_description_margin_top 0x7f06010d
int dimen lb_playback_major_fade_translate_y 0x7f06010e
int dimen lb_playback_media_item_radio_icon_size 0x7f06010f
int dimen lb_playback_media_radio_width_with_padding 0x7f060110
int dimen lb_playback_media_row_details_selector_width 0x7f060111
int dimen lb_playback_media_row_horizontal_padding 0x7f060112
int dimen lb_playback_media_row_radio_selector_width 0x7f060113
int dimen lb_playback_media_row_selector_round_rect_radius 0x7f060114
int dimen lb_playback_media_row_separator_height 0x7f060115
int dimen lb_playback_minor_fade_translate_y 0x7f060116
int dimen lb_playback_now_playing_bar_height 0x7f060117
int dimen lb_playback_now_playing_bar_left_margin 0x7f060118
int dimen lb_playback_now_playing_bar_margin 0x7f060119
int dimen lb_playback_now_playing_bar_top_margin 0x7f06011a
int dimen lb_playback_now_playing_bar_width 0x7f06011b
int dimen lb_playback_now_playing_view_size 0x7f06011c
int dimen lb_playback_other_rows_center_to_bottom 0x7f06011d
int dimen lb_playback_play_icon_size 0x7f06011e
int dimen lb_playback_time_padding_top 0x7f06011f
int dimen lb_playback_total_time_margin_end 0x7f060120
int dimen lb_playback_transport_control_info_margin_bottom 0x7f060121
int dimen lb_playback_transport_control_row_padding_bottom 0x7f060122
int dimen lb_playback_transport_controlbar_margin_start 0x7f060123
int dimen lb_playback_transport_hero_thumbs_height 0x7f060124
int dimen lb_playback_transport_hero_thumbs_width 0x7f060125
int dimen lb_playback_transport_image_height 0x7f060126
int dimen lb_playback_transport_image_margin_end 0x7f060127
int dimen lb_playback_transport_progressbar_active_bar_height 0x7f060128
int dimen lb_playback_transport_progressbar_active_radius 0x7f060129
int dimen lb_playback_transport_progressbar_bar_height 0x7f06012a
int dimen lb_playback_transport_progressbar_height 0x7f06012b
int dimen lb_playback_transport_thumbs_bottom_margin 0x7f06012c
int dimen lb_playback_transport_thumbs_height 0x7f06012d
int dimen lb_playback_transport_thumbs_margin 0x7f06012e
int dimen lb_playback_transport_thumbs_width 0x7f06012f
int dimen lb_playback_transport_time_margin 0x7f060130
int dimen lb_playback_transport_time_margin_top 0x7f060131
int dimen lb_rounded_rect_corner_radius 0x7f060132
int dimen lb_search_bar_edit_text_margin_start 0x7f060133
int dimen lb_search_bar_height 0x7f060134
int dimen lb_search_bar_hint_margin_start 0x7f060135
int dimen lb_search_bar_icon_height 0x7f060136
int dimen lb_search_bar_icon_margin_start 0x7f060137
int dimen lb_search_bar_icon_width 0x7f060138
int dimen lb_search_bar_inner_margin_bottom 0x7f060139
int dimen lb_search_bar_inner_margin_top 0x7f06013a
int dimen lb_search_bar_items_height 0x7f06013b
int dimen lb_search_bar_items_layout_margin_top 0x7f06013c
int dimen lb_search_bar_items_margin_start 0x7f06013d
int dimen lb_search_bar_items_width 0x7f06013e
int dimen lb_search_bar_padding_start 0x7f06013f
int dimen lb_search_bar_padding_top 0x7f060140
int dimen lb_search_bar_speech_orb_margin_start 0x7f060141
int dimen lb_search_bar_speech_orb_size 0x7f060142
int dimen lb_search_bar_text_size 0x7f060143
int dimen lb_search_bar_unfocused_text_size 0x7f060144
int dimen lb_search_browse_row_padding_start 0x7f060145
int dimen lb_search_browse_rows_align_top 0x7f060146
int dimen lb_search_orb_focused_z 0x7f060147
int dimen lb_search_orb_margin_bottom 0x7f060148
int dimen lb_search_orb_margin_end 0x7f060149
int dimen lb_search_orb_margin_start 0x7f06014a
int dimen lb_search_orb_margin_top 0x7f06014b
int dimen lb_search_orb_size 0x7f06014c
int dimen lb_search_orb_unfocused_z 0x7f06014d
int dimen lb_vertical_grid_padding_bottom 0x7f06014e
int dimen notification_action_icon_size 0x7f06014f
int dimen notification_action_text_size 0x7f060150
int dimen notification_big_circle_margin 0x7f060151
int dimen notification_content_margin_start 0x7f060152
int dimen notification_large_icon_height 0x7f060153
int dimen notification_large_icon_width 0x7f060154
int dimen notification_main_column_padding_top 0x7f060155
int dimen notification_media_narrow_margin 0x7f060156
int dimen notification_right_icon_size 0x7f060157
int dimen notification_right_side_padding_top 0x7f060158
int dimen notification_small_icon_background_padding 0x7f060159
int dimen notification_small_icon_size_as_large 0x7f06015a
int dimen notification_subtext_size 0x7f06015b
int dimen notification_top_pad 0x7f06015c
int dimen notification_top_pad_large_text 0x7f06015d
int dimen picker_column_horizontal_padding 0x7f06015e
int dimen picker_item_height 0x7f06015f
int dimen picker_item_spacing 0x7f060160
int dimen picker_separator_horizontal_padding 0x7f060161
int dimen pinpicker_text_size 0x7f060162
int dimen subtitle_corner_radius 0x7f060163
int dimen subtitle_outline_width 0x7f060164
int dimen subtitle_shadow_offset 0x7f060165
int dimen subtitle_shadow_radius 0x7f060166
int dimen tooltip_corner_radius 0x7f060167
int dimen tooltip_horizontal_padding 0x7f060168
int dimen tooltip_margin 0x7f060169
int dimen tooltip_precise_anchor_extra_offset 0x7f06016a
int dimen tooltip_precise_anchor_threshold 0x7f06016b
int dimen tooltip_vertical_padding 0x7f06016c
int dimen tooltip_y_offset_non_touch 0x7f06016d
int dimen tooltip_y_offset_touch 0x7f06016e
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070000
int drawable abc_action_bar_item_background_material 0x7f070001
int drawable abc_btn_borderless_material 0x7f070002
int drawable abc_btn_check_material 0x7f070003
int drawable abc_btn_check_to_on_mtrl_000 0x7f070004
int drawable abc_btn_check_to_on_mtrl_015 0x7f070005
int drawable abc_btn_colored_material 0x7f070006
int drawable abc_btn_default_mtrl_shape 0x7f070007
int drawable abc_btn_radio_material 0x7f070008
int drawable abc_btn_radio_to_on_mtrl_000 0x7f070009
int drawable abc_btn_radio_to_on_mtrl_015 0x7f07000a
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f07000b
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f07000c
int drawable abc_cab_background_internal_bg 0x7f07000d
int drawable abc_cab_background_top_material 0x7f07000e
int drawable abc_cab_background_top_mtrl_alpha 0x7f07000f
int drawable abc_control_background_material 0x7f070010
int drawable abc_dialog_material_background 0x7f070011
int drawable abc_edit_text_material 0x7f070012
int drawable abc_ic_ab_back_material 0x7f070013
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f070014
int drawable abc_ic_clear_material 0x7f070015
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f070016
int drawable abc_ic_go_search_api_material 0x7f070017
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f070018
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f070019
int drawable abc_ic_menu_overflow_material 0x7f07001a
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f07001b
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f07001c
int drawable abc_ic_menu_share_mtrl_alpha 0x7f07001d
int drawable abc_ic_search_api_material 0x7f07001e
int drawable abc_ic_star_black_16dp 0x7f07001f
int drawable abc_ic_star_black_36dp 0x7f070020
int drawable abc_ic_star_black_48dp 0x7f070021
int drawable abc_ic_star_half_black_16dp 0x7f070022
int drawable abc_ic_star_half_black_36dp 0x7f070023
int drawable abc_ic_star_half_black_48dp 0x7f070024
int drawable abc_ic_voice_search_api_material 0x7f070025
int drawable abc_item_background_holo_dark 0x7f070026
int drawable abc_item_background_holo_light 0x7f070027
int drawable abc_list_divider_material 0x7f070028
int drawable abc_list_divider_mtrl_alpha 0x7f070029
int drawable abc_list_focused_holo 0x7f07002a
int drawable abc_list_longpressed_holo 0x7f07002b
int drawable abc_list_pressed_holo_dark 0x7f07002c
int drawable abc_list_pressed_holo_light 0x7f07002d
int drawable abc_list_selector_background_transition_holo_dark 0x7f07002e
int drawable abc_list_selector_background_transition_holo_light 0x7f07002f
int drawable abc_list_selector_disabled_holo_dark 0x7f070030
int drawable abc_list_selector_disabled_holo_light 0x7f070031
int drawable abc_list_selector_holo_dark 0x7f070032
int drawable abc_list_selector_holo_light 0x7f070033
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f070034
int drawable abc_popup_background_mtrl_mult 0x7f070035
int drawable abc_ratingbar_indicator_material 0x7f070036
int drawable abc_ratingbar_material 0x7f070037
int drawable abc_ratingbar_small_material 0x7f070038
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f070039
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f07003a
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f07003b
int drawable abc_scrubber_primary_mtrl_alpha 0x7f07003c
int drawable abc_scrubber_track_mtrl_alpha 0x7f07003d
int drawable abc_seekbar_thumb_material 0x7f07003e
int drawable abc_seekbar_tick_mark_material 0x7f07003f
int drawable abc_seekbar_track_material 0x7f070040
int drawable abc_spinner_mtrl_am_alpha 0x7f070041
int drawable abc_spinner_textfield_background_material 0x7f070042
int drawable abc_switch_thumb_material 0x7f070043
int drawable abc_switch_track_mtrl_alpha 0x7f070044
int drawable abc_tab_indicator_material 0x7f070045
int drawable abc_tab_indicator_mtrl_alpha 0x7f070046
int drawable abc_text_cursor_material 0x7f070047
int drawable abc_text_select_handle_left_mtrl_dark 0x7f070048
int drawable abc_text_select_handle_left_mtrl_light 0x7f070049
int drawable abc_text_select_handle_middle_mtrl_dark 0x7f07004a
int drawable abc_text_select_handle_middle_mtrl_light 0x7f07004b
int drawable abc_text_select_handle_right_mtrl_dark 0x7f07004c
int drawable abc_text_select_handle_right_mtrl_light 0x7f07004d
int drawable abc_textfield_activated_mtrl_alpha 0x7f07004e
int drawable abc_textfield_default_mtrl_alpha 0x7f07004f
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f070050
int drawable abc_textfield_search_default_mtrl_alpha 0x7f070051
int drawable abc_textfield_search_material 0x7f070052
int drawable abc_vector_test 0x7f070053
int drawable app_banner 0x7f070054
int drawable ic_call_answer 0x7f070055
int drawable ic_call_answer_low 0x7f070056
int drawable ic_call_answer_video 0x7f070057
int drawable ic_call_answer_video_low 0x7f070058
int drawable ic_call_decline 0x7f070059
int drawable ic_call_decline_low 0x7f07005a
int drawable ic_launcher_foreground 0x7f07005b
int drawable lb_action_bg 0x7f07005c
int drawable lb_action_bg_focused 0x7f07005d
int drawable lb_background 0x7f07005e
int drawable lb_card_foreground 0x7f07005f
int drawable lb_card_shadow_focused 0x7f070060
int drawable lb_card_shadow_normal 0x7f070061
int drawable lb_control_button_primary 0x7f070062
int drawable lb_control_button_secondary 0x7f070063
int drawable lb_headers_right_fading 0x7f070064
int drawable lb_ic_actions_right_arrow 0x7f070065
int drawable lb_ic_cc 0x7f070066
int drawable lb_ic_fast_forward 0x7f070067
int drawable lb_ic_fast_rewind 0x7f070068
int drawable lb_ic_guidedactions_item_chevron 0x7f070069
int drawable lb_ic_hq 0x7f07006a
int drawable lb_ic_in_app_search 0x7f07006b
int drawable lb_ic_loop 0x7f07006c
int drawable lb_ic_loop_one 0x7f07006d
int drawable lb_ic_more 0x7f07006e
int drawable lb_ic_nav_arrow 0x7f07006f
int drawable lb_ic_pause 0x7f070070
int drawable lb_ic_pip 0x7f070071
int drawable lb_ic_play 0x7f070072
int drawable lb_ic_play_fit 0x7f070073
int drawable lb_ic_playback_loop 0x7f070074
int drawable lb_ic_replay 0x7f070075
int drawable lb_ic_sad_cloud 0x7f070076
int drawable lb_ic_search_mic 0x7f070077
int drawable lb_ic_search_mic_out 0x7f070078
int drawable lb_ic_shuffle 0x7f070079
int drawable lb_ic_skip_next 0x7f07007a
int drawable lb_ic_skip_previous 0x7f07007b
int drawable lb_ic_stop 0x7f07007c
int drawable lb_ic_thumb_down 0x7f07007d
int drawable lb_ic_thumb_down_outline 0x7f07007e
int drawable lb_ic_thumb_up 0x7f07007f
int drawable lb_ic_thumb_up_outline 0x7f070080
int drawable lb_in_app_search_bg 0x7f070081
int drawable lb_in_app_search_shadow_focused 0x7f070082
int drawable lb_in_app_search_shadow_normal 0x7f070083
int drawable lb_onboarding_start_button_background 0x7f070084
int drawable lb_playback_now_playing_bar 0x7f070085
int drawable lb_playback_progress_bar 0x7f070086
int drawable lb_search_orb 0x7f070087
int drawable lb_selectable_item_rounded_rect 0x7f070088
int drawable lb_speech_orb 0x7f070089
int drawable lb_text_dot_one 0x7f07008a
int drawable lb_text_dot_one_small 0x7f07008b
int drawable lb_text_dot_two 0x7f07008c
int drawable lb_text_dot_two_small 0x7f07008d
int drawable notification_action_background 0x7f07008e
int drawable notification_bg 0x7f07008f
int drawable notification_bg_low 0x7f070090
int drawable notification_bg_low_normal 0x7f070091
int drawable notification_bg_low_pressed 0x7f070092
int drawable notification_bg_normal 0x7f070093
int drawable notification_bg_normal_pressed 0x7f070094
int drawable notification_icon_background 0x7f070095
int drawable notification_oversize_large_icon_bg 0x7f070096
int drawable notification_template_icon_bg 0x7f070097
int drawable notification_template_icon_low_bg 0x7f070098
int drawable notification_tile_bg 0x7f070099
int drawable notify_panel_notification_icon_bg 0x7f07009a
int drawable tooltip_frame_dark 0x7f07009b
int drawable tooltip_frame_light 0x7f07009c
int fraction lb_browse_header_unselect_alpha 0x7f080000
int fraction lb_browse_rows_scale 0x7f080001
int fraction lb_focus_zoom_factor_large 0x7f080002
int fraction lb_focus_zoom_factor_medium 0x7f080003
int fraction lb_focus_zoom_factor_small 0x7f080004
int fraction lb_focus_zoom_factor_xsmall 0x7f080005
int fraction lb_search_bar_speech_orb_max_level_zoom 0x7f080006
int fraction lb_search_orb_focused_zoom 0x7f080007
int fraction lb_view_active_level 0x7f080008
int fraction lb_view_dimmed_level 0x7f080009
int id ALT 0x7f090000
int id CTRL 0x7f090001
int id Content 0x7f090002
int id FUNCTION 0x7f090003
int id IconOnLeft 0x7f090004
int id IconOnRight 0x7f090005
int id ImageOnly 0x7f090006
int id META 0x7f090007
int id SHIFT 0x7f090008
int id SYM 0x7f090009
int id Title 0x7f09000a
int id accessibility_action_clickable_span 0x7f09000b
int id accessibility_custom_action_0 0x7f09000c
int id accessibility_custom_action_1 0x7f09000d
int id accessibility_custom_action_10 0x7f09000e
int id accessibility_custom_action_11 0x7f09000f
int id accessibility_custom_action_12 0x7f090010
int id accessibility_custom_action_13 0x7f090011
int id accessibility_custom_action_14 0x7f090012
int id accessibility_custom_action_15 0x7f090013
int id accessibility_custom_action_16 0x7f090014
int id accessibility_custom_action_17 0x7f090015
int id accessibility_custom_action_18 0x7f090016
int id accessibility_custom_action_19 0x7f090017
int id accessibility_custom_action_2 0x7f090018
int id accessibility_custom_action_20 0x7f090019
int id accessibility_custom_action_21 0x7f09001a
int id accessibility_custom_action_22 0x7f09001b
int id accessibility_custom_action_23 0x7f09001c
int id accessibility_custom_action_24 0x7f09001d
int id accessibility_custom_action_25 0x7f09001e
int id accessibility_custom_action_26 0x7f09001f
int id accessibility_custom_action_27 0x7f090020
int id accessibility_custom_action_28 0x7f090021
int id accessibility_custom_action_29 0x7f090022
int id accessibility_custom_action_3 0x7f090023
int id accessibility_custom_action_30 0x7f090024
int id accessibility_custom_action_31 0x7f090025
int id accessibility_custom_action_4 0x7f090026
int id accessibility_custom_action_5 0x7f090027
int id accessibility_custom_action_6 0x7f090028
int id accessibility_custom_action_7 0x7f090029
int id accessibility_custom_action_8 0x7f09002a
int id accessibility_custom_action_9 0x7f09002b
int id action0 0x7f09002c
int id actionIcon 0x7f09002d
int id action_bar 0x7f09002e
int id action_bar_activity_content 0x7f09002f
int id action_bar_container 0x7f090030
int id action_bar_root 0x7f090031
int id action_bar_spinner 0x7f090032
int id action_bar_subtitle 0x7f090033
int id action_bar_title 0x7f090034
int id action_container 0x7f090035
int id action_context_bar 0x7f090036
int id action_divider 0x7f090037
int id action_fragment 0x7f090038
int id action_fragment_background 0x7f090039
int id action_fragment_root 0x7f09003a
int id action_image 0x7f09003b
int id action_menu_divider 0x7f09003c
int id action_menu_presenter 0x7f09003d
int id action_mode_bar 0x7f09003e
int id action_mode_bar_stub 0x7f09003f
int id action_mode_close_button 0x7f090040
int id action_text 0x7f090041
int id actions 0x7f090042
int id activated 0x7f090043
int id activity_chooser_view_content 0x7f090044
int id add 0x7f090045
int id alertTitle 0x7f090046
int id all 0x7f090047
int id always 0x7f090048
int id androidx_compose_ui_view_composition_context 0x7f090049
int id async 0x7f09004a
int id background 0x7f09004b
int id background_container 0x7f09004c
int id background_imagein 0x7f09004d
int id background_imageout 0x7f09004e
int id bar1 0x7f09004f
int id bar2 0x7f090050
int id bar3 0x7f090051
int id beginning 0x7f090052
int id blocking 0x7f090053
int id bottom 0x7f090054
int id bottom_spacer 0x7f090055
int id browse_container_dock 0x7f090056
int id browse_dummy 0x7f090057
int id browse_frame 0x7f090058
int id browse_grid 0x7f090059
int id browse_grid_dock 0x7f09005a
int id browse_headers 0x7f09005b
int id browse_headers_dock 0x7f09005c
int id browse_headers_root 0x7f09005d
int id browse_title_group 0x7f09005e
int id button 0x7f09005f
int id buttonPanel 0x7f090060
int id button_start 0x7f090061
int id cancel_action 0x7f090062
int id center 0x7f090063
int id center_horizontal 0x7f090064
int id center_vertical 0x7f090065
int id checkbox 0x7f090066
int id chronometer 0x7f090067
int id clip_horizontal 0x7f090068
int id clip_vertical 0x7f090069
int id collapseActionView 0x7f09006a
int id column 0x7f09006b
int id compose_view_saveable_id_tag 0x7f09006c
int id consume_window_insets_tag 0x7f09006d
int id container_list 0x7f09006e
int id content 0x7f09006f
int id contentPanel 0x7f090070
int id content_container 0x7f090071
int id content_fragment 0x7f090072
int id content_frame 0x7f090073
int id content_text 0x7f090074
int id control_bar 0x7f090075
int id controls_card 0x7f090076
int id controls_card_right_panel 0x7f090077
int id controls_container 0x7f090078
int id controls_dock 0x7f090079
int id current_time 0x7f09007a
int id custom 0x7f09007b
int id customPanel 0x7f09007c
int id decor_content_parent 0x7f09007d
int id default_activity_button 0x7f09007e
int id description 0x7f09007f
int id description_dock 0x7f090080
int id details_background_view 0x7f090081
int id details_fragment_root 0x7f090082
int id details_frame 0x7f090083
int id details_overview 0x7f090084
int id details_overview_actions 0x7f090085
int id details_overview_actions_background 0x7f090086
int id details_overview_description 0x7f090087
int id details_overview_image 0x7f090088
int id details_overview_right_panel 0x7f090089
int id details_root 0x7f09008a
int id details_rows_dock 0x7f09008b
int id dialog_button 0x7f09008c
int id disableHome 0x7f09008d
int id dummy 0x7f09008e
int id edit_query 0x7f09008f
int id edit_text_id 0x7f090090
int id end 0x7f090091
int id end_padder 0x7f090092
int id error_frame 0x7f090093
int id expand_activities_button 0x7f090094
int id expanded_menu 0x7f090095
int id extra 0x7f090096
int id extra_badge 0x7f090097
int id fade_out_edge 0x7f090098
int id fill 0x7f090099
int id fill_horizontal 0x7f09009a
int id fill_vertical 0x7f09009b
int id foreground_container 0x7f09009c
int id forever 0x7f09009d
int id grid_frame 0x7f09009e
int id group_divider 0x7f09009f
int id guidance_breadcrumb 0x7f0900a0
int id guidance_container 0x7f0900a1
int id guidance_description 0x7f0900a2
int id guidance_icon 0x7f0900a3
int id guidance_title 0x7f0900a4
int id guidedactions_activator_item 0x7f0900a5
int id guidedactions_content 0x7f0900a6
int id guidedactions_content2 0x7f0900a7
int id guidedactions_item_checkmark 0x7f0900a8
int id guidedactions_item_chevron 0x7f0900a9
int id guidedactions_item_content 0x7f0900aa
int id guidedactions_item_description 0x7f0900ab
int id guidedactions_item_icon 0x7f0900ac
int id guidedactions_item_title 0x7f0900ad
int id guidedactions_list 0x7f0900ae
int id guidedactions_list2 0x7f0900af
int id guidedactions_list_background 0x7f0900b0
int id guidedactions_list_background2 0x7f0900b1
int id guidedactions_root 0x7f0900b2
int id guidedactions_root2 0x7f0900b3
int id guidedactions_sub_list 0x7f0900b4
int id guidedactions_sub_list_background 0x7f0900b5
int id guidedstep_background 0x7f0900b6
int id guidedstep_background_view_root 0x7f0900b7
int id guidedstep_root 0x7f0900b8
int id hide_graphics_layer_in_inspector_tag 0x7f0900b9
int id hide_ime_id 0x7f0900ba
int id hide_in_inspector_tag 0x7f0900bb
int id home 0x7f0900bc
int id homeAsUp 0x7f0900bd
int id hovercard_panel 0x7f0900be
int id icon 0x7f0900bf
int id icon_group 0x7f0900c0
int id ifRoom 0x7f0900c1
int id image 0x7f0900c2
int id info 0x7f0900c3
int id infoOver 0x7f0900c4
int id infoUnder 0x7f0900c5
int id infoUnderWithExtra 0x7f0900c6
int id info_field 0x7f0900c7
int id initial 0x7f0900c8
int id inspection_slot_table_set 0x7f0900c9
int id is_pooling_container_tag 0x7f0900ca
int id italic 0x7f0900cb
int id item_touch_helper_previous_elevation 0x7f0900cc
int id label 0x7f0900cd
int id lb_action_button 0x7f0900ce
int id lb_control_closed_captioning 0x7f0900cf
int id lb_control_fast_forward 0x7f0900d0
int id lb_control_fast_rewind 0x7f0900d1
int id lb_control_high_quality 0x7f0900d2
int id lb_control_more_actions 0x7f0900d3
int id lb_control_picture_in_picture 0x7f0900d4
int id lb_control_play_pause 0x7f0900d5
int id lb_control_repeat 0x7f0900d6
int id lb_control_shuffle 0x7f0900d7
int id lb_control_skip_next 0x7f0900d8
int id lb_control_skip_previous 0x7f0900d9
int id lb_control_thumbs_down 0x7f0900da
int id lb_control_thumbs_up 0x7f0900db
int id lb_details_description_body 0x7f0900dc
int id lb_details_description_subtitle 0x7f0900dd
int id lb_details_description_title 0x7f0900de
int id lb_focus_animator 0x7f0900df
int id lb_guidedstep_background 0x7f0900e0
int id lb_parallax_source 0x7f0900e1
int id lb_results_frame 0x7f0900e2
int id lb_row_container_header_dock 0x7f0900e3
int id lb_search_bar 0x7f0900e4
int id lb_search_bar_badge 0x7f0900e5
int id lb_search_bar_items 0x7f0900e6
int id lb_search_bar_speech_orb 0x7f0900e7
int id lb_search_frame 0x7f0900e8
int id lb_search_text_editor 0x7f0900e9
int id lb_shadow_focused 0x7f0900ea
int id lb_shadow_impl 0x7f0900eb
int id lb_shadow_normal 0x7f0900ec
int id lb_slide_transition_value 0x7f0900ed
int id left 0x7f0900ee
int id line1 0x7f0900ef
int id line3 0x7f0900f0
int id listMode 0x7f0900f1
int id list_item 0x7f0900f2
int id logo 0x7f0900f3
int id main 0x7f0900f4
int id mainOnly 0x7f0900f5
int id main_icon 0x7f0900f6
int id main_image 0x7f0900f7
int id maxLines 0x7f0900f8
int id mediaItemActionsContainer 0x7f0900f9
int id mediaItemDetails 0x7f0900fa
int id mediaItemDuration 0x7f0900fb
int id mediaItemName 0x7f0900fc
int id mediaItemNumberViewFlipper 0x7f0900fd
int id mediaItemRow 0x7f0900fe
int id mediaListHeader 0x7f0900ff
int id mediaRowSelector 0x7f090100
int id mediaRowSeparator 0x7f090101
int id media_actions 0x7f090102
int id message 0x7f090103
int id middle 0x7f090104
int id more_actions_dock 0x7f090105
int id multiply 0x7f090106
int id nav_controller_view_tag 0x7f090107
int id navigator_container 0x7f090108
int id never 0x7f090109
int id none 0x7f09010a
int id normal 0x7f09010b
int id notification_background 0x7f09010c
int id notification_main_column 0x7f09010d
int id notification_main_column_container 0x7f09010e
int id onboarding_fragment_root 0x7f09010f
int id page_container 0x7f090110
int id page_indicator 0x7f090111
int id parentPanel 0x7f090112
int id paused 0x7f090113
int id picker 0x7f090114
int id playback_controls_dock 0x7f090115
int id playback_fragment_background 0x7f090116
int id playback_fragment_root 0x7f090117
int id playback_progress 0x7f090118
int id playing 0x7f090119
int id pooling_container_listener_holder_tag 0x7f09011a
int id progress_circular 0x7f09011b
int id progress_horizontal 0x7f09011c
int id radio 0x7f09011d
int id report_drawn 0x7f09011e
int id right 0x7f09011f
int id right_icon 0x7f090120
int id right_side 0x7f090121
int id row_content 0x7f090122
int id row_header 0x7f090123
int id row_header_description 0x7f090124
int id scale_frame 0x7f090125
int id screen 0x7f090126
int id scrollIndicatorDown 0x7f090127
int id scrollIndicatorUp 0x7f090128
int id scrollView 0x7f090129
int id search_badge 0x7f09012a
int id search_bar 0x7f09012b
int id search_button 0x7f09012c
int id search_close_btn 0x7f09012d
int id search_edit_frame 0x7f09012e
int id search_go_btn 0x7f09012f
int id search_mag_icon 0x7f090130
int id search_orb 0x7f090131
int id search_plate 0x7f090132
int id search_src_text 0x7f090133
int id search_voice_btn 0x7f090134
int id secondary_controls_dock 0x7f090135
int id select_dialog_listview 0x7f090136
int id selected 0x7f090137
int id separate_time 0x7f090138
int id separator 0x7f090139
int id shortcut 0x7f09013a
int id showCustom 0x7f09013b
int id showHome 0x7f09013c
int id showTitle 0x7f09013d
int id spacer 0x7f09013e
int id split_action_bar 0x7f09013f
int id src_atop 0x7f090140
int id src_in 0x7f090141
int id src_over 0x7f090142
int id start 0x7f090143
int id status_bar_latest_event_content 0x7f090144
int id submenuarrow 0x7f090145
int id submit_area 0x7f090146
int id tabMode 0x7f090147
int id tag_accessibility_actions 0x7f090148
int id tag_accessibility_clickable_spans 0x7f090149
int id tag_accessibility_heading 0x7f09014a
int id tag_accessibility_pane_title 0x7f09014b
int id tag_compat_insets_dispatch 0x7f09014c
int id tag_on_apply_window_listener 0x7f09014d
int id tag_on_receive_content_listener 0x7f09014e
int id tag_on_receive_content_mime_types 0x7f09014f
int id tag_screen_reader_focusable 0x7f090150
int id tag_state_description 0x7f090151
int id tag_system_bar_state_monitor 0x7f090152
int id tag_transition_group 0x7f090153
int id tag_unhandled_key_event_manager 0x7f090154
int id tag_unhandled_key_listeners 0x7f090155
int id tag_window_insets_animation_callback 0x7f090156
int id text 0x7f090157
int id text2 0x7f090158
int id textSpacerNoButtons 0x7f090159
int id textSpacerNoTitle 0x7f09015a
int id thumbs_row 0x7f09015b
int id time 0x7f09015c
int id title 0x7f09015d
int id titleDividerNoCustom 0x7f09015e
int id title_badge 0x7f09015f
int id title_orb 0x7f090160
int id title_template 0x7f090161
int id title_text 0x7f090162
int id top 0x7f090163
int id topPanel 0x7f090164
int id total_time 0x7f090165
int id transitionPosition 0x7f090166
int id transport_row 0x7f090167
int id uniform 0x7f090168
int id up 0x7f090169
int id useLogo 0x7f09016a
int id video_surface 0x7f09016b
int id video_surface_container 0x7f09016c
int id view_tree_disjoint_parent 0x7f09016d
int id view_tree_lifecycle_owner 0x7f09016e
int id view_tree_on_back_pressed_dispatcher_owner 0x7f09016f
int id view_tree_saved_state_registry_owner 0x7f090170
int id view_tree_view_model_store_owner 0x7f090171
int id withText 0x7f090172
int id wrap_content 0x7f090173
int id wrapped_composition_tag 0x7f090174
int integer abc_config_activityDefaultDur 0x7f0a0000
int integer abc_config_activityShortDur 0x7f0a0001
int integer cancel_button_image_alpha 0x7f0a0002
int integer config_tooltipAnimTime 0x7f0a0003
int integer lb_browse_headers_transition_delay 0x7f0a0004
int integer lb_browse_headers_transition_duration 0x7f0a0005
int integer lb_browse_rows_anim_duration 0x7f0a0006
int integer lb_card_activated_animation_duration 0x7f0a0007
int integer lb_card_selected_animation_delay 0x7f0a0008
int integer lb_card_selected_animation_duration 0x7f0a0009
int integer lb_details_description_body_max_lines 0x7f0a000a
int integer lb_details_description_body_min_lines 0x7f0a000b
int integer lb_details_description_subtitle_max_lines 0x7f0a000c
int integer lb_details_description_title_max_lines 0x7f0a000d
int integer lb_error_message_max_lines 0x7f0a000e
int integer lb_guidedactions_item_animation_duration 0x7f0a000f
int integer lb_guidedactions_item_description_min_lines 0x7f0a0010
int integer lb_guidedactions_item_title_max_lines 0x7f0a0011
int integer lb_guidedactions_item_title_min_lines 0x7f0a0012
int integer lb_guidedstep_activity_background_fade_duration_ms 0x7f0a0013
int integer lb_onboarding_header_description_delay 0x7f0a0014
int integer lb_onboarding_header_title_delay 0x7f0a0015
int integer lb_playback_bg_fade_in_ms 0x7f0a0016
int integer lb_playback_bg_fade_out_ms 0x7f0a0017
int integer lb_playback_controls_fade_in_ms 0x7f0a0018
int integer lb_playback_controls_fade_out_ms 0x7f0a0019
int integer lb_playback_controls_show_time_ms 0x7f0a001a
int integer lb_playback_controls_tickle_timeout_ms 0x7f0a001b
int integer lb_playback_description_fade_in_ms 0x7f0a001c
int integer lb_playback_description_fade_out_ms 0x7f0a001d
int integer lb_playback_rows_fade_delay_ms 0x7f0a001e
int integer lb_playback_rows_fade_in_ms 0x7f0a001f
int integer lb_playback_rows_fade_out_ms 0x7f0a0020
int integer lb_search_bar_speech_mode_background_alpha 0x7f0a0021
int integer lb_search_bar_text_mode_background_alpha 0x7f0a0022
int integer lb_search_orb_pulse_duration_ms 0x7f0a0023
int integer lb_search_orb_scale_duration_ms 0x7f0a0024
int integer m3c_window_layout_in_display_cutout_mode 0x7f0a0025
int integer slideEdgeEnd 0x7f0a0026
int integer slideEdgeStart 0x7f0a0027
int integer status_bar_notification_info_maxnum 0x7f0a0028
int layout abc_action_bar_title_item 0x7f0b0000
int layout abc_action_bar_up_container 0x7f0b0001
int layout abc_action_menu_item_layout 0x7f0b0002
int layout abc_action_menu_layout 0x7f0b0003
int layout abc_action_mode_bar 0x7f0b0004
int layout abc_action_mode_close_item_material 0x7f0b0005
int layout abc_activity_chooser_view 0x7f0b0006
int layout abc_activity_chooser_view_list_item 0x7f0b0007
int layout abc_alert_dialog_button_bar_material 0x7f0b0008
int layout abc_alert_dialog_material 0x7f0b0009
int layout abc_alert_dialog_title_material 0x7f0b000a
int layout abc_cascading_menu_item_layout 0x7f0b000b
int layout abc_dialog_title_material 0x7f0b000c
int layout abc_expanded_menu_layout 0x7f0b000d
int layout abc_list_menu_item_checkbox 0x7f0b000e
int layout abc_list_menu_item_icon 0x7f0b000f
int layout abc_list_menu_item_layout 0x7f0b0010
int layout abc_list_menu_item_radio 0x7f0b0011
int layout abc_popup_menu_header_item_layout 0x7f0b0012
int layout abc_popup_menu_item_layout 0x7f0b0013
int layout abc_screen_content_include 0x7f0b0014
int layout abc_screen_simple 0x7f0b0015
int layout abc_screen_simple_overlay_action_mode 0x7f0b0016
int layout abc_screen_toolbar 0x7f0b0017
int layout abc_search_dropdown_item_icons_2line 0x7f0b0018
int layout abc_search_view 0x7f0b0019
int layout abc_select_dialog_material 0x7f0b001a
int layout abc_tooltip 0x7f0b001b
int layout custom_dialog 0x7f0b001c
int layout ime_base_split_test_activity 0x7f0b001d
int layout ime_secondary_split_test_activity 0x7f0b001e
int layout lb_action_1_line 0x7f0b001f
int layout lb_action_2_lines 0x7f0b0020
int layout lb_background_window 0x7f0b0021
int layout lb_browse_fragment 0x7f0b0022
int layout lb_browse_title 0x7f0b0023
int layout lb_control_bar 0x7f0b0024
int layout lb_control_button_primary 0x7f0b0025
int layout lb_control_button_secondary 0x7f0b0026
int layout lb_details_description 0x7f0b0027
int layout lb_details_fragment 0x7f0b0028
int layout lb_details_overview 0x7f0b0029
int layout lb_divider 0x7f0b002a
int layout lb_error_fragment 0x7f0b002b
int layout lb_fullwidth_details_overview 0x7f0b002c
int layout lb_fullwidth_details_overview_logo 0x7f0b002d
int layout lb_guidance 0x7f0b002e
int layout lb_guidedactions 0x7f0b002f
int layout lb_guidedactions_datepicker_item 0x7f0b0030
int layout lb_guidedactions_item 0x7f0b0031
int layout lb_guidedbuttonactions 0x7f0b0032
int layout lb_guidedstep_background 0x7f0b0033
int layout lb_guidedstep_fragment 0x7f0b0034
int layout lb_header 0x7f0b0035
int layout lb_headers_fragment 0x7f0b0036
int layout lb_image_card_view 0x7f0b0037
int layout lb_image_card_view_themed_badge_left 0x7f0b0038
int layout lb_image_card_view_themed_badge_right 0x7f0b0039
int layout lb_image_card_view_themed_content 0x7f0b003a
int layout lb_image_card_view_themed_title 0x7f0b003b
int layout lb_list_row 0x7f0b003c
int layout lb_list_row_hovercard 0x7f0b003d
int layout lb_media_item_number_view_flipper 0x7f0b003e
int layout lb_media_list_header 0x7f0b003f
int layout lb_onboarding_fragment 0x7f0b0040
int layout lb_picker 0x7f0b0041
int layout lb_picker_column 0x7f0b0042
int layout lb_picker_item 0x7f0b0043
int layout lb_picker_separator 0x7f0b0044
int layout lb_pinpicker_item 0x7f0b0045
int layout lb_playback_controls 0x7f0b0046
int layout lb_playback_controls_row 0x7f0b0047
int layout lb_playback_fragment 0x7f0b0048
int layout lb_playback_now_playing_bars 0x7f0b0049
int layout lb_playback_transport_controls 0x7f0b004a
int layout lb_playback_transport_controls_row 0x7f0b004b
int layout lb_row_container 0x7f0b004c
int layout lb_row_header 0x7f0b004d
int layout lb_row_media_item 0x7f0b004e
int layout lb_row_media_item_action 0x7f0b004f
int layout lb_rows_fragment 0x7f0b0050
int layout lb_search_bar 0x7f0b0051
int layout lb_search_fragment 0x7f0b0052
int layout lb_search_orb 0x7f0b0053
int layout lb_section_header 0x7f0b0054
int layout lb_shadow 0x7f0b0055
int layout lb_speech_orb 0x7f0b0056
int layout lb_title_view 0x7f0b0057
int layout lb_vertical_grid 0x7f0b0058
int layout lb_vertical_grid_fragment 0x7f0b0059
int layout lb_video_surface 0x7f0b005a
int layout notification_action 0x7f0b005b
int layout notification_action_tombstone 0x7f0b005c
int layout notification_media_action 0x7f0b005d
int layout notification_media_cancel_action 0x7f0b005e
int layout notification_template_big_media 0x7f0b005f
int layout notification_template_big_media_custom 0x7f0b0060
int layout notification_template_big_media_narrow 0x7f0b0061
int layout notification_template_big_media_narrow_custom 0x7f0b0062
int layout notification_template_custom_big 0x7f0b0063
int layout notification_template_icon_group 0x7f0b0064
int layout notification_template_lines_media 0x7f0b0065
int layout notification_template_media 0x7f0b0066
int layout notification_template_media_custom 0x7f0b0067
int layout notification_template_part_chronometer 0x7f0b0068
int layout notification_template_part_time 0x7f0b0069
int layout select_dialog_item_material 0x7f0b006a
int layout select_dialog_multichoice_material 0x7f0b006b
int layout select_dialog_singlechoice_material 0x7f0b006c
int layout support_simple_spinner_dropdown_item 0x7f0b006d
int layout video_surface_fragment 0x7f0b006e
int mipmap ic_launcher 0x7f0c0000
int mipmap ic_launcher_round 0x7f0c0001
int raw lb_voice_failure 0x7f0d0000
int raw lb_voice_no_input 0x7f0d0001
int raw lb_voice_open 0x7f0d0002
int raw lb_voice_success 0x7f0d0003
int string abc_action_bar_home_description 0x7f0e0000
int string abc_action_bar_up_description 0x7f0e0001
int string abc_action_menu_overflow_description 0x7f0e0002
int string abc_action_mode_done 0x7f0e0003
int string abc_activity_chooser_view_see_all 0x7f0e0004
int string abc_activitychooserview_choose_application 0x7f0e0005
int string abc_capital_off 0x7f0e0006
int string abc_capital_on 0x7f0e0007
int string abc_font_family_body_1_material 0x7f0e0008
int string abc_font_family_body_2_material 0x7f0e0009
int string abc_font_family_button_material 0x7f0e000a
int string abc_font_family_caption_material 0x7f0e000b
int string abc_font_family_display_1_material 0x7f0e000c
int string abc_font_family_display_2_material 0x7f0e000d
int string abc_font_family_display_3_material 0x7f0e000e
int string abc_font_family_display_4_material 0x7f0e000f
int string abc_font_family_headline_material 0x7f0e0010
int string abc_font_family_menu_material 0x7f0e0011
int string abc_font_family_subhead_material 0x7f0e0012
int string abc_font_family_title_material 0x7f0e0013
int string abc_menu_alt_shortcut_label 0x7f0e0014
int string abc_menu_ctrl_shortcut_label 0x7f0e0015
int string abc_menu_delete_shortcut_label 0x7f0e0016
int string abc_menu_enter_shortcut_label 0x7f0e0017
int string abc_menu_function_shortcut_label 0x7f0e0018
int string abc_menu_meta_shortcut_label 0x7f0e0019
int string abc_menu_shift_shortcut_label 0x7f0e001a
int string abc_menu_space_shortcut_label 0x7f0e001b
int string abc_menu_sym_shortcut_label 0x7f0e001c
int string abc_prepend_shortcut_label 0x7f0e001d
int string abc_search_hint 0x7f0e001e
int string abc_searchview_description_clear 0x7f0e001f
int string abc_searchview_description_query 0x7f0e0020
int string abc_searchview_description_search 0x7f0e0021
int string abc_searchview_description_submit 0x7f0e0022
int string abc_searchview_description_voice 0x7f0e0023
int string abc_shareactionprovider_share_with 0x7f0e0024
int string abc_shareactionprovider_share_with_application 0x7f0e0025
int string abc_toolbar_collapse_description 0x7f0e0026
int string androidx_startup 0x7f0e0027
int string app_name 0x7f0e0028
int string autofill 0x7f0e0029
int string call_notification_answer_action 0x7f0e002a
int string call_notification_answer_video_action 0x7f0e002b
int string call_notification_decline_action 0x7f0e002c
int string call_notification_hang_up_action 0x7f0e002d
int string call_notification_incoming_text 0x7f0e002e
int string call_notification_ongoing_text 0x7f0e002f
int string call_notification_screening_text 0x7f0e0030
int string close_drawer 0x7f0e0031
int string close_sheet 0x7f0e0032
int string default_error_message 0x7f0e0033
int string default_popup_window_title 0x7f0e0034
int string dropdown_menu 0x7f0e0035
int string in_progress 0x7f0e0036
int string indeterminate 0x7f0e0037
int string lb_control_display_fast_forward_multiplier 0x7f0e0038
int string lb_control_display_rewind_multiplier 0x7f0e0039
int string lb_guidedaction_continue_title 0x7f0e003a
int string lb_guidedaction_finish_title 0x7f0e003b
int string lb_media_player_error 0x7f0e003c
int string lb_navigation_menu_contentDescription 0x7f0e003d
int string lb_onboarding_accessibility_next 0x7f0e003e
int string lb_onboarding_get_started 0x7f0e003f
int string lb_playback_controls_closed_captioning_disable 0x7f0e0040
int string lb_playback_controls_closed_captioning_enable 0x7f0e0041
int string lb_playback_controls_fast_forward 0x7f0e0042
int string lb_playback_controls_fast_forward_multiplier 0x7f0e0043
int string lb_playback_controls_hidden 0x7f0e0044
int string lb_playback_controls_high_quality_disable 0x7f0e0045
int string lb_playback_controls_high_quality_enable 0x7f0e0046
int string lb_playback_controls_more_actions 0x7f0e0047
int string lb_playback_controls_pause 0x7f0e0048
int string lb_playback_controls_picture_in_picture 0x7f0e0049
int string lb_playback_controls_play 0x7f0e004a
int string lb_playback_controls_repeat_all 0x7f0e004b
int string lb_playback_controls_repeat_none 0x7f0e004c
int string lb_playback_controls_repeat_one 0x7f0e004d
int string lb_playback_controls_rewind 0x7f0e004e
int string lb_playback_controls_rewind_multiplier 0x7f0e004f
int string lb_playback_controls_shown 0x7f0e0050
int string lb_playback_controls_shuffle_disable 0x7f0e0051
int string lb_playback_controls_shuffle_enable 0x7f0e0052
int string lb_playback_controls_skip_next 0x7f0e0053
int string lb_playback_controls_skip_previous 0x7f0e0054
int string lb_playback_controls_thumb_down 0x7f0e0055
int string lb_playback_controls_thumb_down_outline 0x7f0e0056
int string lb_playback_controls_thumb_up 0x7f0e0057
int string lb_playback_controls_thumb_up_outline 0x7f0e0058
int string lb_playback_time_separator 0x7f0e0059
int string lb_search_bar_hint 0x7f0e005a
int string lb_search_bar_hint_speech 0x7f0e005b
int string lb_search_bar_hint_with_title 0x7f0e005c
int string lb_search_bar_hint_with_title_speech 0x7f0e005d
int string m3c_bottom_sheet_collapse_description 0x7f0e005e
int string m3c_bottom_sheet_dismiss_description 0x7f0e005f
int string m3c_bottom_sheet_drag_handle_description 0x7f0e0060
int string m3c_bottom_sheet_expand_description 0x7f0e0061
int string m3c_bottom_sheet_pane_title 0x7f0e0062
int string m3c_date_input_headline 0x7f0e0063
int string m3c_date_input_headline_description 0x7f0e0064
int string m3c_date_input_invalid_for_pattern 0x7f0e0065
int string m3c_date_input_invalid_not_allowed 0x7f0e0066
int string m3c_date_input_invalid_year_range 0x7f0e0067
int string m3c_date_input_label 0x7f0e0068
int string m3c_date_input_no_input_description 0x7f0e0069
int string m3c_date_input_title 0x7f0e006a
int string m3c_date_picker_headline 0x7f0e006b
int string m3c_date_picker_headline_description 0x7f0e006c
int string m3c_date_picker_navigate_to_year_description 0x7f0e006d
int string m3c_date_picker_no_selection_description 0x7f0e006e
int string m3c_date_picker_scroll_to_earlier_years 0x7f0e006f
int string m3c_date_picker_scroll_to_later_years 0x7f0e0070
int string m3c_date_picker_switch_to_calendar_mode 0x7f0e0071
int string m3c_date_picker_switch_to_day_selection 0x7f0e0072
int string m3c_date_picker_switch_to_input_mode 0x7f0e0073
int string m3c_date_picker_switch_to_next_month 0x7f0e0074
int string m3c_date_picker_switch_to_previous_month 0x7f0e0075
int string m3c_date_picker_switch_to_year_selection 0x7f0e0076
int string m3c_date_picker_title 0x7f0e0077
int string m3c_date_picker_today_description 0x7f0e0078
int string m3c_date_picker_year_picker_pane_title 0x7f0e0079
int string m3c_date_range_input_invalid_range_input 0x7f0e007a
int string m3c_date_range_input_title 0x7f0e007b
int string m3c_date_range_picker_day_in_range 0x7f0e007c
int string m3c_date_range_picker_end_headline 0x7f0e007d
int string m3c_date_range_picker_scroll_to_next_month 0x7f0e007e
int string m3c_date_range_picker_scroll_to_previous_month 0x7f0e007f
int string m3c_date_range_picker_start_headline 0x7f0e0080
int string m3c_date_range_picker_title 0x7f0e0081
int string m3c_dialog 0x7f0e0082
int string m3c_dropdown_menu_collapsed 0x7f0e0083
int string m3c_dropdown_menu_expanded 0x7f0e0084
int string m3c_dropdown_menu_toggle 0x7f0e0085
int string m3c_search_bar_search 0x7f0e0086
int string m3c_snackbar_dismiss 0x7f0e0087
int string m3c_suggestions_available 0x7f0e0088
int string m3c_time_picker_am 0x7f0e0089
int string m3c_time_picker_hour 0x7f0e008a
int string m3c_time_picker_hour_24h_suffix 0x7f0e008b
int string m3c_time_picker_hour_selection 0x7f0e008c
int string m3c_time_picker_hour_suffix 0x7f0e008d
int string m3c_time_picker_hour_text_field 0x7f0e008e
int string m3c_time_picker_minute 0x7f0e008f
int string m3c_time_picker_minute_selection 0x7f0e0090
int string m3c_time_picker_minute_suffix 0x7f0e0091
int string m3c_time_picker_minute_text_field 0x7f0e0092
int string m3c_time_picker_period_toggle_description 0x7f0e0093
int string m3c_time_picker_pm 0x7f0e0094
int string m3c_tooltip_long_press_label 0x7f0e0095
int string m3c_tooltip_pane_description 0x7f0e0096
int string mc2_snackbar_pane_title 0x7f0e0097
int string menu_toggle 0x7f0e0098
int string nav_favorites 0x7f0e0099
int string nav_home 0x7f0e009a
int string nav_live 0x7f0e009b
int string nav_movies 0x7f0e009c
int string nav_search 0x7f0e009d
int string nav_settings 0x7f0e009e
int string nav_tv_shows 0x7f0e009f
int string navigation_drawer 0x7f0e00a0
int string navigation_menu 0x7f0e00a1
int string not_selected 0x7f0e00a2
int string orb_search_action 0x7f0e00a3
int string range_end 0x7f0e00a4
int string range_start 0x7f0e00a5
int string search_menu_title 0x7f0e00a6
int string selected 0x7f0e00a7
int string snackbar_pane_title 0x7f0e00a8
int string state_empty 0x7f0e00a9
int string state_off 0x7f0e00aa
int string state_on 0x7f0e00ab
int string status_bar_notification_info_overflow 0x7f0e00ac
int string switch_role 0x7f0e00ad
int string tab 0x7f0e00ae
int string template_percent 0x7f0e00af
int string tooltip_description 0x7f0e00b0
int string tooltip_label 0x7f0e00b1
int style AlertDialog_AppCompat 0x7f0f0000
int style AlertDialog_AppCompat_Light 0x7f0f0001
int style Animation_AppCompat_Dialog 0x7f0f0002
int style Animation_AppCompat_DropDownUp 0x7f0f0003
int style Animation_AppCompat_Tooltip 0x7f0f0004
int style Base_AlertDialog_AppCompat 0x7f0f0005
int style Base_AlertDialog_AppCompat_Light 0x7f0f0006
int style Base_Animation_AppCompat_Dialog 0x7f0f0007
int style Base_Animation_AppCompat_DropDownUp 0x7f0f0008
int style Base_Animation_AppCompat_Tooltip 0x7f0f0009
int style Base_DialogWindowTitle_AppCompat 0x7f0f000a
int style Base_DialogWindowTitleBackground_AppCompat 0x7f0f000b
int style Base_TextAppearance_AppCompat 0x7f0f000c
int style Base_TextAppearance_AppCompat_Body1 0x7f0f000d
int style Base_TextAppearance_AppCompat_Body2 0x7f0f000e
int style Base_TextAppearance_AppCompat_Button 0x7f0f000f
int style Base_TextAppearance_AppCompat_Caption 0x7f0f0010
int style Base_TextAppearance_AppCompat_Display1 0x7f0f0011
int style Base_TextAppearance_AppCompat_Display2 0x7f0f0012
int style Base_TextAppearance_AppCompat_Display3 0x7f0f0013
int style Base_TextAppearance_AppCompat_Display4 0x7f0f0014
int style Base_TextAppearance_AppCompat_Headline 0x7f0f0015
int style Base_TextAppearance_AppCompat_Inverse 0x7f0f0016
int style Base_TextAppearance_AppCompat_Large 0x7f0f0017
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f0f0018
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f0019
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f001a
int style Base_TextAppearance_AppCompat_Medium 0x7f0f001b
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f0f001c
int style Base_TextAppearance_AppCompat_Menu 0x7f0f001d
int style Base_TextAppearance_AppCompat_SearchResult 0x7f0f001e
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f001f
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f0f0020
int style Base_TextAppearance_AppCompat_Small 0x7f0f0021
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f0f0022
int style Base_TextAppearance_AppCompat_Subhead 0x7f0f0023
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f0f0024
int style Base_TextAppearance_AppCompat_Title 0x7f0f0025
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f0f0026
int style Base_TextAppearance_AppCompat_Tooltip 0x7f0f0027
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f0028
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f0029
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f002a
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f002b
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f002c
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f002d
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f002e
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f0f002f
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f0030
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f0031
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f0032
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f0033
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f0034
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f0035
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f0036
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f0f0037
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f0038
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f0039
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f003a
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f003b
int style Base_Theme_AppCompat 0x7f0f003c
int style Base_Theme_AppCompat_CompactMenu 0x7f0f003d
int style Base_Theme_AppCompat_Dialog 0x7f0f003e
int style Base_Theme_AppCompat_Dialog_Alert 0x7f0f003f
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f0f0040
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f0f0041
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f0f0042
int style Base_Theme_AppCompat_Light 0x7f0f0043
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f0f0044
int style Base_Theme_AppCompat_Light_Dialog 0x7f0f0045
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f0f0046
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f0f0047
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f0048
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f0f0049
int style Base_ThemeOverlay_AppCompat 0x7f0f004a
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f0f004b
int style Base_ThemeOverlay_AppCompat_Dark 0x7f0f004c
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f004d
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f0f004e
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f004f
int style Base_ThemeOverlay_AppCompat_Light 0x7f0f0050
int style Base_V21_Theme_AppCompat 0x7f0f0051
int style Base_V21_Theme_AppCompat_Dialog 0x7f0f0052
int style Base_V21_Theme_AppCompat_Light 0x7f0f0053
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f0f0054
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f0f0055
int style Base_V22_Theme_AppCompat 0x7f0f0056
int style Base_V22_Theme_AppCompat_Light 0x7f0f0057
int style Base_V23_Theme_AppCompat 0x7f0f0058
int style Base_V23_Theme_AppCompat_Light 0x7f0f0059
int style Base_V26_Theme_AppCompat 0x7f0f005a
int style Base_V26_Theme_AppCompat_Light 0x7f0f005b
int style Base_V26_Widget_AppCompat_Toolbar 0x7f0f005c
int style Base_V28_Theme_AppCompat 0x7f0f005d
int style Base_V28_Theme_AppCompat_Light 0x7f0f005e
int style Base_V7_Theme_AppCompat 0x7f0f005f
int style Base_V7_Theme_AppCompat_Dialog 0x7f0f0060
int style Base_V7_Theme_AppCompat_Light 0x7f0f0061
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f0f0062
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f0f0063
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f0f0064
int style Base_V7_Widget_AppCompat_EditText 0x7f0f0065
int style Base_V7_Widget_AppCompat_Toolbar 0x7f0f0066
int style Base_Widget_AppCompat_ActionBar 0x7f0f0067
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f0f0068
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f0f0069
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f0f006a
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f0f006b
int style Base_Widget_AppCompat_ActionButton 0x7f0f006c
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f0f006d
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f0f006e
int style Base_Widget_AppCompat_ActionMode 0x7f0f006f
int style Base_Widget_AppCompat_ActivityChooserView 0x7f0f0070
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f0f0071
int style Base_Widget_AppCompat_Button 0x7f0f0072
int style Base_Widget_AppCompat_Button_Borderless 0x7f0f0073
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f0f0074
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f0075
int style Base_Widget_AppCompat_Button_Colored 0x7f0f0076
int style Base_Widget_AppCompat_Button_Small 0x7f0f0077
int style Base_Widget_AppCompat_ButtonBar 0x7f0f0078
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f0079
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f0f007a
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f0f007b
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f0f007c
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f0f007d
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f0f007e
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f0f007f
int style Base_Widget_AppCompat_EditText 0x7f0f0080
int style Base_Widget_AppCompat_ImageButton 0x7f0f0081
int style Base_Widget_AppCompat_Light_ActionBar 0x7f0f0082
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f0f0083
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f0084
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f0f0085
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f0086
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f0f0087
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f0f0088
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f0089
int style Base_Widget_AppCompat_ListMenuView 0x7f0f008a
int style Base_Widget_AppCompat_ListPopupWindow 0x7f0f008b
int style Base_Widget_AppCompat_ListView 0x7f0f008c
int style Base_Widget_AppCompat_ListView_DropDown 0x7f0f008d
int style Base_Widget_AppCompat_ListView_Menu 0x7f0f008e
int style Base_Widget_AppCompat_PopupMenu 0x7f0f008f
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f0f0090
int style Base_Widget_AppCompat_PopupWindow 0x7f0f0091
int style Base_Widget_AppCompat_ProgressBar 0x7f0f0092
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f0f0093
int style Base_Widget_AppCompat_RatingBar 0x7f0f0094
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f0f0095
int style Base_Widget_AppCompat_RatingBar_Small 0x7f0f0096
int style Base_Widget_AppCompat_SearchView 0x7f0f0097
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f0f0098
int style Base_Widget_AppCompat_SeekBar 0x7f0f0099
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f0f009a
int style Base_Widget_AppCompat_Spinner 0x7f0f009b
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f0f009c
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f0f009d
int style Base_Widget_AppCompat_Toolbar 0x7f0f009e
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f009f
int style DialogWindowTheme 0x7f0f00a0
int style EdgeToEdgeFloatingDialogTheme 0x7f0f00a1
int style EdgeToEdgeFloatingDialogWindowTheme 0x7f0f00a2
int style FloatingDialogTheme 0x7f0f00a3
int style FloatingDialogWindowTheme 0x7f0f00a4
int style Platform_AppCompat 0x7f0f00a5
int style Platform_AppCompat_Light 0x7f0f00a6
int style Platform_ThemeOverlay_AppCompat 0x7f0f00a7
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f0f00a8
int style Platform_ThemeOverlay_AppCompat_Light 0x7f0f00a9
int style Platform_V21_AppCompat 0x7f0f00aa
int style Platform_V21_AppCompat_Light 0x7f0f00ab
int style Platform_V25_AppCompat 0x7f0f00ac
int style Platform_V25_AppCompat_Light 0x7f0f00ad
int style Platform_Widget_AppCompat_Spinner 0x7f0f00ae
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f0f00af
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f0f00b0
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f0f00b1
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f0f00b2
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f0f00b3
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f0f00b4
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f0f00b5
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f0f00b6
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f0f00b7
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f0f00b8
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f0f00b9
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f0f00ba
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f0f00bb
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f0f00bc
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f0f00bd
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f0f00be
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f0f00bf
int style TextAppearance_AppCompat 0x7f0f00c0
int style TextAppearance_AppCompat_Body1 0x7f0f00c1
int style TextAppearance_AppCompat_Body2 0x7f0f00c2
int style TextAppearance_AppCompat_Button 0x7f0f00c3
int style TextAppearance_AppCompat_Caption 0x7f0f00c4
int style TextAppearance_AppCompat_Display1 0x7f0f00c5
int style TextAppearance_AppCompat_Display2 0x7f0f00c6
int style TextAppearance_AppCompat_Display3 0x7f0f00c7
int style TextAppearance_AppCompat_Display4 0x7f0f00c8
int style TextAppearance_AppCompat_Headline 0x7f0f00c9
int style TextAppearance_AppCompat_Inverse 0x7f0f00ca
int style TextAppearance_AppCompat_Large 0x7f0f00cb
int style TextAppearance_AppCompat_Large_Inverse 0x7f0f00cc
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f0f00cd
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f0f00ce
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f00cf
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f00d0
int style TextAppearance_AppCompat_Medium 0x7f0f00d1
int style TextAppearance_AppCompat_Medium_Inverse 0x7f0f00d2
int style TextAppearance_AppCompat_Menu 0x7f0f00d3
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f00d4
int style TextAppearance_AppCompat_SearchResult_Title 0x7f0f00d5
int style TextAppearance_AppCompat_Small 0x7f0f00d6
int style TextAppearance_AppCompat_Small_Inverse 0x7f0f00d7
int style TextAppearance_AppCompat_Subhead 0x7f0f00d8
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f0f00d9
int style TextAppearance_AppCompat_Title 0x7f0f00da
int style TextAppearance_AppCompat_Title_Inverse 0x7f0f00db
int style TextAppearance_AppCompat_Tooltip 0x7f0f00dc
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f00dd
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f00de
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f00df
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f00e0
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f00e1
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f00e2
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f0f00e3
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f00e4
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f0f00e5
int style TextAppearance_AppCompat_Widget_Button 0x7f0f00e6
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f00e7
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f00e8
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f00e9
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f00ea
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f00eb
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f00ec
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f00ed
int style TextAppearance_AppCompat_Widget_Switch 0x7f0f00ee
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f00ef
int style TextAppearance_Compat_Notification 0x7f0f00f0
int style TextAppearance_Compat_Notification_Info 0x7f0f00f1
int style TextAppearance_Compat_Notification_Info_Media 0x7f0f00f2
int style TextAppearance_Compat_Notification_Line2 0x7f0f00f3
int style TextAppearance_Compat_Notification_Line2_Media 0x7f0f00f4
int style TextAppearance_Compat_Notification_Media 0x7f0f00f5
int style TextAppearance_Compat_Notification_Time 0x7f0f00f6
int style TextAppearance_Compat_Notification_Time_Media 0x7f0f00f7
int style TextAppearance_Compat_Notification_Title 0x7f0f00f8
int style TextAppearance_Compat_Notification_Title_Media 0x7f0f00f9
int style TextAppearance_Leanback 0x7f0f00fa
int style TextAppearance_Leanback_DetailsActionButton 0x7f0f00fb
int style TextAppearance_Leanback_DetailsDescriptionBody 0x7f0f00fc
int style TextAppearance_Leanback_DetailsDescriptionSubtitle 0x7f0f00fd
int style TextAppearance_Leanback_DetailsDescriptionTitle 0x7f0f00fe
int style TextAppearance_Leanback_ErrorMessage 0x7f0f00ff
int style TextAppearance_Leanback_Header 0x7f0f0100
int style TextAppearance_Leanback_Header_Section 0x7f0f0101
int style TextAppearance_Leanback_ImageCardView 0x7f0f0102
int style TextAppearance_Leanback_ImageCardView_Content 0x7f0f0103
int style TextAppearance_Leanback_ImageCardView_Title 0x7f0f0104
int style TextAppearance_Leanback_PlaybackControlLabel 0x7f0f0105
int style TextAppearance_Leanback_PlaybackControlsTime 0x7f0f0106
int style TextAppearance_Leanback_PlaybackMediaItemDuration 0x7f0f0107
int style TextAppearance_Leanback_PlaybackMediaItemName 0x7f0f0108
int style TextAppearance_Leanback_PlaybackMediaItemNumber 0x7f0f0109
int style TextAppearance_Leanback_PlaybackMediaListHeaderTitle 0x7f0f010a
int style TextAppearance_Leanback_Row_Header 0x7f0f010b
int style TextAppearance_Leanback_Row_Header_Description 0x7f0f010c
int style TextAppearance_Leanback_Row_HoverCardDescription 0x7f0f010d
int style TextAppearance_Leanback_Row_HoverCardTitle 0x7f0f010e
int style TextAppearance_Leanback_SearchTextEdit 0x7f0f010f
int style TextAppearance_Leanback_Title 0x7f0f0110
int style TextAppearance_LeanbackBase 0x7f0f0111
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f0112
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f0113
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f0114
int style Theme_AppCompat 0x7f0f0115
int style Theme_AppCompat_CompactMenu 0x7f0f0116
int style Theme_AppCompat_DayNight 0x7f0f0117
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f0f0118
int style Theme_AppCompat_DayNight_Dialog 0x7f0f0119
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f0f011a
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f0f011b
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f0f011c
int style Theme_AppCompat_DayNight_NoActionBar 0x7f0f011d
int style Theme_AppCompat_Dialog 0x7f0f011e
int style Theme_AppCompat_Dialog_Alert 0x7f0f011f
int style Theme_AppCompat_Dialog_MinWidth 0x7f0f0120
int style Theme_AppCompat_DialogWhenLarge 0x7f0f0121
int style Theme_AppCompat_Leanback 0x7f0f0122
int style Theme_AppCompat_Leanback_Browse 0x7f0f0123
int style Theme_AppCompat_Leanback_Details 0x7f0f0124
int style Theme_AppCompat_Leanback_Details_NoSharedElementTransition 0x7f0f0125
int style Theme_AppCompat_Leanback_GuidedStep 0x7f0f0126
int style Theme_AppCompat_Leanback_GuidedStep_Half 0x7f0f0127
int style Theme_AppCompat_Leanback_GuidedStep_HalfBase 0x7f0f0128
int style Theme_AppCompat_Leanback_GuidedStepBase 0x7f0f0129
int style Theme_AppCompat_Leanback_Onboarding 0x7f0f012a
int style Theme_AppCompat_Leanback_VerticalGrid 0x7f0f012b
int style Theme_AppCompat_LeanbackBase 0x7f0f012c
int style Theme_AppCompat_Light 0x7f0f012d
int style Theme_AppCompat_Light_DarkActionBar 0x7f0f012e
int style Theme_AppCompat_Light_Dialog 0x7f0f012f
int style Theme_AppCompat_Light_Dialog_Alert 0x7f0f0130
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f0131
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f0f0132
int style Theme_AppCompat_Light_NoActionBar 0x7f0f0133
int style Theme_AppCompat_NoActionBar 0x7f0f0134
int style Theme_Leanback 0x7f0f0135
int style Theme_Leanback_Browse 0x7f0f0136
int style Theme_Leanback_Details 0x7f0f0137
int style Theme_Leanback_Details_NoSharedElementTransition 0x7f0f0138
int style Theme_Leanback_GuidedStep 0x7f0f0139
int style Theme_Leanback_GuidedStep_Half 0x7f0f013a
int style Theme_Leanback_GuidedStep_HalfBase 0x7f0f013b
int style Theme_Leanback_GuidedStepBase 0x7f0f013c
int style Theme_Leanback_Onboarding 0x7f0f013d
int style Theme_Leanback_VerticalGrid 0x7f0f013e
int style Theme_LeanbackBase 0x7f0f013f
int style ThemeOverlay_AppCompat 0x7f0f0140
int style ThemeOverlay_AppCompat_ActionBar 0x7f0f0141
int style ThemeOverlay_AppCompat_Dark 0x7f0f0142
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f0143
int style ThemeOverlay_AppCompat_Dialog 0x7f0f0144
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f0145
int style ThemeOverlay_AppCompat_Light 0x7f0f0146
int style Widget_AppCompat_ActionBar 0x7f0f0147
int style Widget_AppCompat_ActionBar_Solid 0x7f0f0148
int style Widget_AppCompat_ActionBar_TabBar 0x7f0f0149
int style Widget_AppCompat_ActionBar_TabText 0x7f0f014a
int style Widget_AppCompat_ActionBar_TabView 0x7f0f014b
int style Widget_AppCompat_ActionButton 0x7f0f014c
int style Widget_AppCompat_ActionButton_CloseMode 0x7f0f014d
int style Widget_AppCompat_ActionButton_Overflow 0x7f0f014e
int style Widget_AppCompat_ActionMode 0x7f0f014f
int style Widget_AppCompat_ActivityChooserView 0x7f0f0150
int style Widget_AppCompat_AutoCompleteTextView 0x7f0f0151
int style Widget_AppCompat_Button 0x7f0f0152
int style Widget_AppCompat_Button_Borderless 0x7f0f0153
int style Widget_AppCompat_Button_Borderless_Colored 0x7f0f0154
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f0155
int style Widget_AppCompat_Button_Colored 0x7f0f0156
int style Widget_AppCompat_Button_Small 0x7f0f0157
int style Widget_AppCompat_ButtonBar 0x7f0f0158
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f0159
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f0f015a
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f0f015b
int style Widget_AppCompat_CompoundButton_Switch 0x7f0f015c
int style Widget_AppCompat_DrawerArrowToggle 0x7f0f015d
int style Widget_AppCompat_DropDownItem_Spinner 0x7f0f015e
int style Widget_AppCompat_EditText 0x7f0f015f
int style Widget_AppCompat_ImageButton 0x7f0f0160
int style Widget_AppCompat_Light_ActionBar 0x7f0f0161
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f0f0162
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f0f0163
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f0164
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f0f0165
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f0f0166
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f0167
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f0f0168
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f0f0169
int style Widget_AppCompat_Light_ActionButton 0x7f0f016a
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f0f016b
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f0f016c
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f0f016d
int style Widget_AppCompat_Light_ActivityChooserView 0x7f0f016e
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f0f016f
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f0f0170
int style Widget_AppCompat_Light_ListPopupWindow 0x7f0f0171
int style Widget_AppCompat_Light_ListView_DropDown 0x7f0f0172
int style Widget_AppCompat_Light_PopupMenu 0x7f0f0173
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f0174
int style Widget_AppCompat_Light_SearchView 0x7f0f0175
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f0f0176
int style Widget_AppCompat_ListMenuView 0x7f0f0177
int style Widget_AppCompat_ListPopupWindow 0x7f0f0178
int style Widget_AppCompat_ListView 0x7f0f0179
int style Widget_AppCompat_ListView_DropDown 0x7f0f017a
int style Widget_AppCompat_ListView_Menu 0x7f0f017b
int style Widget_AppCompat_PopupMenu 0x7f0f017c
int style Widget_AppCompat_PopupMenu_Overflow 0x7f0f017d
int style Widget_AppCompat_PopupWindow 0x7f0f017e
int style Widget_AppCompat_ProgressBar 0x7f0f017f
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f0f0180
int style Widget_AppCompat_RatingBar 0x7f0f0181
int style Widget_AppCompat_RatingBar_Indicator 0x7f0f0182
int style Widget_AppCompat_RatingBar_Small 0x7f0f0183
int style Widget_AppCompat_SearchView 0x7f0f0184
int style Widget_AppCompat_SearchView_ActionBar 0x7f0f0185
int style Widget_AppCompat_SeekBar 0x7f0f0186
int style Widget_AppCompat_SeekBar_Discrete 0x7f0f0187
int style Widget_AppCompat_Spinner 0x7f0f0188
int style Widget_AppCompat_Spinner_DropDown 0x7f0f0189
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f0f018a
int style Widget_AppCompat_Spinner_Underlined 0x7f0f018b
int style Widget_AppCompat_TextView_SpinnerItem 0x7f0f018c
int style Widget_AppCompat_Toolbar 0x7f0f018d
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f018e
int style Widget_Compat_NotificationActionContainer 0x7f0f018f
int style Widget_Compat_NotificationActionText 0x7f0f0190
int style Widget_Leanback 0x7f0f0191
int style Widget_Leanback_BaseCardViewStyle 0x7f0f0192
int style Widget_Leanback_DetailsActionButtonStyle 0x7f0f0193
int style Widget_Leanback_DetailsActionButtonStyleBase 0x7f0f0194
int style Widget_Leanback_DetailsDescriptionBodyStyle 0x7f0f0195
int style Widget_Leanback_DetailsDescriptionSubtitleStyle 0x7f0f0196
int style Widget_Leanback_DetailsDescriptionTitleStyle 0x7f0f0197
int style Widget_Leanback_ErrorMessageStyle 0x7f0f0198
int style Widget_Leanback_GridItems 0x7f0f0199
int style Widget_Leanback_GridItems_VerticalGridView 0x7f0f019a
int style Widget_Leanback_GuidanceBreadcrumbStyle 0x7f0f019b
int style Widget_Leanback_GuidanceContainerStyle 0x7f0f019c
int style Widget_Leanback_GuidanceDescriptionStyle 0x7f0f019d
int style Widget_Leanback_GuidanceIconStyle 0x7f0f019e
int style Widget_Leanback_GuidanceTitleStyle 0x7f0f019f
int style Widget_Leanback_GuidedActionItemCheckmarkStyle 0x7f0f01a0
int style Widget_Leanback_GuidedActionItemChevronStyle 0x7f0f01a1
int style Widget_Leanback_GuidedActionItemContainerStyle 0x7f0f01a2
int style Widget_Leanback_GuidedActionItemContentStyle 0x7f0f01a3
int style Widget_Leanback_GuidedActionItemDescriptionStyle 0x7f0f01a4
int style Widget_Leanback_GuidedActionItemIconStyle 0x7f0f01a5
int style Widget_Leanback_GuidedActionItemTitleStyle 0x7f0f01a6
int style Widget_Leanback_GuidedActionsContainerStyle 0x7f0f01a7
int style Widget_Leanback_GuidedActionsListStyle 0x7f0f01a8
int style Widget_Leanback_GuidedActionsSelectorStyle 0x7f0f01a9
int style Widget_Leanback_GuidedButtonActionsListStyle 0x7f0f01aa
int style Widget_Leanback_GuidedSubActionsListStyle 0x7f0f01ab
int style Widget_Leanback_Header 0x7f0f01ac
int style Widget_Leanback_Header_Section 0x7f0f01ad
int style Widget_Leanback_Headers 0x7f0f01ae
int style Widget_Leanback_Headers_VerticalGridView 0x7f0f01af
int style Widget_Leanback_ImageCardView 0x7f0f01b0
int style Widget_Leanback_ImageCardView_BadgeStyle 0x7f0f01b1
int style Widget_Leanback_ImageCardView_ContentStyle 0x7f0f01b2
int style Widget_Leanback_ImageCardView_ImageStyle 0x7f0f01b3
int style Widget_Leanback_ImageCardView_InfoAreaStyle 0x7f0f01b4
int style Widget_Leanback_ImageCardView_TitleStyle 0x7f0f01b5
int style Widget_Leanback_ImageCardViewStyle 0x7f0f01b6
int style Widget_Leanback_OnboardingDescriptionStyle 0x7f0f01b7
int style Widget_Leanback_OnboardingHeaderStyle 0x7f0f01b8
int style Widget_Leanback_OnboardingLogoStyle 0x7f0f01b9
int style Widget_Leanback_OnboardingMainIconStyle 0x7f0f01ba
int style Widget_Leanback_OnboardingNavigatorContainerStyle 0x7f0f01bb
int style Widget_Leanback_OnboardingPageIndicatorStyle 0x7f0f01bc
int style Widget_Leanback_OnboardingStartButtonStyle 0x7f0f01bd
int style Widget_Leanback_OnboardingStartButtonStyleBase 0x7f0f01be
int style Widget_Leanback_OnboardingTitleStyle 0x7f0f01bf
int style Widget_Leanback_PickerStyle 0x7f0f01c0
int style Widget_Leanback_PickerStyle_DatePickerStyle 0x7f0f01c1
int style Widget_Leanback_PickerStyle_PinPickerStyle 0x7f0f01c2
int style Widget_Leanback_PickerStyle_TimePickerStyle 0x7f0f01c3
int style Widget_Leanback_PlaybackControlLabelStyle 0x7f0f01c4
int style Widget_Leanback_PlaybackControlsActionIconsStyle 0x7f0f01c5
int style Widget_Leanback_PlaybackControlsButtonStyle 0x7f0f01c6
int style Widget_Leanback_PlaybackControlsTimeStyle 0x7f0f01c7
int style Widget_Leanback_PlaybackMediaItemDetailsStyle 0x7f0f01c8
int style Widget_Leanback_PlaybackMediaItemDurationStyle 0x7f0f01c9
int style Widget_Leanback_PlaybackMediaItemNameStyle 0x7f0f01ca
int style Widget_Leanback_PlaybackMediaItemNumberStyle 0x7f0f01cb
int style Widget_Leanback_PlaybackMediaItemNumberViewFlipperStyle 0x7f0f01cc
int style Widget_Leanback_PlaybackMediaItemRowStyle 0x7f0f01cd
int style Widget_Leanback_PlaybackMediaItemSeparatorStyle 0x7f0f01ce
int style Widget_Leanback_PlaybackMediaListHeaderStyle 0x7f0f01cf
int style Widget_Leanback_PlaybackMediaListHeaderTitleStyle 0x7f0f01d0
int style Widget_Leanback_PlaybackRow 0x7f0f01d1
int style Widget_Leanback_Row 0x7f0f01d2
int style Widget_Leanback_Row_Header 0x7f0f01d3
int style Widget_Leanback_Row_Header_Description 0x7f0f01d4
int style Widget_Leanback_Row_HeaderDock 0x7f0f01d5
int style Widget_Leanback_Row_HorizontalGridView 0x7f0f01d6
int style Widget_Leanback_Row_HoverCardDescription 0x7f0f01d7
int style Widget_Leanback_Row_HoverCardTitle 0x7f0f01d8
int style Widget_Leanback_Rows 0x7f0f01d9
int style Widget_Leanback_Rows_VerticalGridView 0x7f0f01da
int style Widget_Leanback_SearchOrbViewStyle 0x7f0f01db
int style Widget_Leanback_Title 0x7f0f01dc
int style Widget_Leanback_Title_Icon 0x7f0f01dd
int style Widget_Leanback_Title_Text 0x7f0f01de
int style Widget_Leanback_TitleView 0x7f0f01df
int style Widget_LeanbackBase 0x7f0f01e0
int style Widget_Support_CoordinatorLayout 0x7f0f01e1
int[] styleable ActionBar { 0x7f030037, 0x7f030038, 0x7f030039, 0x7f03006f, 0x7f030070, 0x7f030071, 0x7f030072, 0x7f030073, 0x7f030074, 0x7f030077, 0x7f03008c, 0x7f03008d, 0x7f03009b, 0x7f0300f2, 0x7f0300f3, 0x7f0300f5, 0x7f0300f6, 0x7f0300f8, 0x7f030103, 0x7f030109, 0x7f030127, 0x7f030131, 0x7f03016e, 0x7f030171, 0x7f030172, 0x7f0301b0, 0x7f0301b3, 0x7f0301d4, 0x7f0301dd }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f030037, 0x7f030038, 0x7f03005c, 0x7f0300f2, 0x7f0301b3, 0x7f0301dd }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f03009f, 0x7f030106 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityNavigator { 0x01010003, 0x7f030000, 0x7f030078, 0x7f030079, 0x7f0301b9 }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable AlertDialog { 0x010100f2, 0x7f030050, 0x7f030051, 0x7f03011e, 0x7f03011f, 0x7f03012d, 0x7f03019f, 0x7f0301a1 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppCompatImageView { 0x01010119, 0x7f0301a9, 0x7f0301d2, 0x7f0301d3 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f0301ce, 0x7f0301cf, 0x7f0301d0 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f030032, 0x7f030033, 0x7f030034, 0x7f030035, 0x7f030036, 0x7f0300a7, 0x7f0300ad, 0x7f03010d, 0x7f03011b, 0x7f0301ba }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_firstBaselineToTopHeight 6
int styleable AppCompatTextView_fontFamily 7
int styleable AppCompatTextView_lastBaselineToBottomHeight 8
int styleable AppCompatTextView_lineHeight 9
int styleable AppCompatTextView_textAllCaps 10
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f030001, 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000f, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030023, 0x7f030024, 0x7f030025, 0x7f030026, 0x7f030027, 0x7f030031, 0x7f03003e, 0x7f03004a, 0x7f03004b, 0x7f03004c, 0x7f03004d, 0x7f03004e, 0x7f030052, 0x7f030053, 0x7f030059, 0x7f03005a, 0x7f030061, 0x7f030062, 0x7f030063, 0x7f030064, 0x7f030065, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f030069, 0x7f03006a, 0x7f030075, 0x7f030089, 0x7f03008a, 0x7f03008b, 0x7f03008e, 0x7f030090, 0x7f030096, 0x7f030097, 0x7f030098, 0x7f030099, 0x7f03009a, 0x7f0300f5, 0x7f0300fc, 0x7f03011c, 0x7f03011d, 0x7f030120, 0x7f030121, 0x7f030122, 0x7f030123, 0x7f030124, 0x7f030125, 0x7f030126, 0x7f030148, 0x7f030149, 0x7f03014a, 0x7f03016d, 0x7f03016f, 0x7f030176, 0x7f030177, 0x7f030178, 0x7f030179, 0x7f030194, 0x7f030196, 0x7f030197, 0x7f030198, 0x7f0301a6, 0x7f0301a7, 0x7f0301b7, 0x7f0301bb, 0x7f0301bc, 0x7f0301bd, 0x7f0301be, 0x7f0301bf, 0x7f0301c0, 0x7f0301c1, 0x7f0301c2, 0x7f0301c3, 0x7f0301c4, 0x7f0301de, 0x7f0301df, 0x7f0301e0, 0x7f0301e1, 0x7f0301ea, 0x7f0301ec, 0x7f0301ed, 0x7f0301ee, 0x7f0301ef, 0x7f0301f0, 0x7f0301f1, 0x7f0301f2, 0x7f0301f3, 0x7f0301f4, 0x7f0301f5 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseDrawable 19
int styleable AppCompatTheme_actionModeCopyDrawable 20
int styleable AppCompatTheme_actionModeCutDrawable 21
int styleable AppCompatTheme_actionModeFindDrawable 22
int styleable AppCompatTheme_actionModePasteDrawable 23
int styleable AppCompatTheme_actionModePopupWindowStyle 24
int styleable AppCompatTheme_actionModeSelectAllDrawable 25
int styleable AppCompatTheme_actionModeShareDrawable 26
int styleable AppCompatTheme_actionModeSplitBackground 27
int styleable AppCompatTheme_actionModeStyle 28
int styleable AppCompatTheme_actionModeWebSearchDrawable 29
int styleable AppCompatTheme_actionOverflowButtonStyle 30
int styleable AppCompatTheme_actionOverflowMenuStyle 31
int styleable AppCompatTheme_activityChooserViewStyle 32
int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
int styleable AppCompatTheme_alertDialogCenterButtons 34
int styleable AppCompatTheme_alertDialogStyle 35
int styleable AppCompatTheme_alertDialogTheme 36
int styleable AppCompatTheme_autoCompleteTextViewStyle 37
int styleable AppCompatTheme_borderlessButtonStyle 38
int styleable AppCompatTheme_buttonBarButtonStyle 39
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
int styleable AppCompatTheme_buttonBarStyle 43
int styleable AppCompatTheme_buttonStyle 44
int styleable AppCompatTheme_buttonStyleSmall 45
int styleable AppCompatTheme_checkboxStyle 46
int styleable AppCompatTheme_checkedTextViewStyle 47
int styleable AppCompatTheme_colorAccent 48
int styleable AppCompatTheme_colorBackgroundFloating 49
int styleable AppCompatTheme_colorButtonNormal 50
int styleable AppCompatTheme_colorControlActivated 51
int styleable AppCompatTheme_colorControlHighlight 52
int styleable AppCompatTheme_colorControlNormal 53
int styleable AppCompatTheme_colorError 54
int styleable AppCompatTheme_colorPrimary 55
int styleable AppCompatTheme_colorPrimaryDark 56
int styleable AppCompatTheme_colorSwitchThumbNormal 57
int styleable AppCompatTheme_controlBackground 58
int styleable AppCompatTheme_dialogCornerRadius 59
int styleable AppCompatTheme_dialogPreferredPadding 60
int styleable AppCompatTheme_dialogTheme 61
int styleable AppCompatTheme_dividerHorizontal 62
int styleable AppCompatTheme_dividerVertical 63
int styleable AppCompatTheme_dropDownListViewStyle 64
int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
int styleable AppCompatTheme_editTextBackground 66
int styleable AppCompatTheme_editTextColor 67
int styleable AppCompatTheme_editTextStyle 68
int styleable AppCompatTheme_homeAsUpIndicator 69
int styleable AppCompatTheme_imageButtonStyle 70
int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
int styleable AppCompatTheme_listDividerAlertDialog 72
int styleable AppCompatTheme_listMenuViewStyle 73
int styleable AppCompatTheme_listPopupWindowStyle 74
int styleable AppCompatTheme_listPreferredItemHeight 75
int styleable AppCompatTheme_listPreferredItemHeightLarge 76
int styleable AppCompatTheme_listPreferredItemHeightSmall 77
int styleable AppCompatTheme_listPreferredItemPaddingLeft 78
int styleable AppCompatTheme_listPreferredItemPaddingRight 79
int styleable AppCompatTheme_panelBackground 80
int styleable AppCompatTheme_panelMenuListTheme 81
int styleable AppCompatTheme_panelMenuListWidth 82
int styleable AppCompatTheme_popupMenuStyle 83
int styleable AppCompatTheme_popupWindowStyle 84
int styleable AppCompatTheme_radioButtonStyle 85
int styleable AppCompatTheme_ratingBarStyle 86
int styleable AppCompatTheme_ratingBarStyleIndicator 87
int styleable AppCompatTheme_ratingBarStyleSmall 88
int styleable AppCompatTheme_searchViewStyle 89
int styleable AppCompatTheme_seekBarStyle 90
int styleable AppCompatTheme_selectableItemBackground 91
int styleable AppCompatTheme_selectableItemBackgroundBorderless 92
int styleable AppCompatTheme_spinnerDropDownItemStyle 93
int styleable AppCompatTheme_spinnerStyle 94
int styleable AppCompatTheme_switchStyle 95
int styleable AppCompatTheme_textAppearanceLargePopupMenu 96
int styleable AppCompatTheme_textAppearanceListItem 97
int styleable AppCompatTheme_textAppearanceListItemSecondary 98
int styleable AppCompatTheme_textAppearanceListItemSmall 99
int styleable AppCompatTheme_textAppearancePopupMenuHeader 100
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 101
int styleable AppCompatTheme_textAppearanceSearchResultTitle 102
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 103
int styleable AppCompatTheme_textColorAlertDialogListItem 104
int styleable AppCompatTheme_textColorSearchUrl 105
int styleable AppCompatTheme_toolbarNavigationButtonStyle 106
int styleable AppCompatTheme_toolbarStyle 107
int styleable AppCompatTheme_tooltipForegroundColor 108
int styleable AppCompatTheme_tooltipFrameBackground 109
int styleable AppCompatTheme_viewInflaterClass 110
int styleable AppCompatTheme_windowActionBar 111
int styleable AppCompatTheme_windowActionBarOverlay 112
int styleable AppCompatTheme_windowActionModeOverlay 113
int styleable AppCompatTheme_windowFixedHeightMajor 114
int styleable AppCompatTheme_windowFixedHeightMinor 115
int styleable AppCompatTheme_windowFixedWidthMajor 116
int styleable AppCompatTheme_windowFixedWidthMinor 117
int styleable AppCompatTheme_windowMinWidthMajor 118
int styleable AppCompatTheme_windowMinWidthMinor 119
int styleable AppCompatTheme_windowNoTitle 120
int[] styleable ButtonBarLayout { 0x7f030028 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f030175, 0x7f03019b }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f030029, 0x7f03010c }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f030054, 0x7f030055 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonTint 1
int styleable CompoundButton_buttonTintMode 2
int[] styleable CoordinatorLayout { 0x7f03010b, 0x7f0301ad }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f030111, 0x7f030112, 0x7f030113, 0x7f030114, 0x7f030115, 0x7f030116 }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable DrawerArrowToggle { 0x7f03002e, 0x7f030030, 0x7f03003c, 0x7f030060, 0x7f030094, 0x7f0300b9, 0x7f0301a5, 0x7f0301c6 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FontFamily { 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f0300b2, 0x7f0300b3, 0x7f0300b4, 0x7f0300b5 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFallbackQuery 2
int styleable FontFamily_fontProviderFetchStrategy 3
int styleable FontFamily_fontProviderFetchTimeout 4
int styleable FontFamily_fontProviderPackage 5
int styleable FontFamily_fontProviderQuery 6
int styleable FontFamily_fontProviderSystemFontFamily 7
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f0300ac, 0x7f0300b6, 0x7f0300b7, 0x7f0300b8, 0x7f0301e6 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LeanbackGuidedStepTheme { 0x7f0300bc, 0x7f0300bd, 0x7f0300be, 0x7f0300bf, 0x7f0300c0, 0x7f0300c1, 0x7f0300c2, 0x7f0300c3, 0x7f0300c4, 0x7f0300c5, 0x7f0300c6, 0x7f0300c7, 0x7f0300c8, 0x7f0300c9, 0x7f0300ca, 0x7f0300cb, 0x7f0300cc, 0x7f0300cd, 0x7f0300ce, 0x7f0300cf, 0x7f0300d0, 0x7f0300d1, 0x7f0300d2, 0x7f0300d3, 0x7f0300d4, 0x7f0300d5, 0x7f0300d6, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f0300dc, 0x7f0300dd, 0x7f0300de, 0x7f0300df, 0x7f0300e0, 0x7f0300e1, 0x7f0300e2, 0x7f0300e3, 0x7f0300e4, 0x7f0300e5, 0x7f0300e6, 0x7f0300e7, 0x7f0300e8, 0x7f0300e9, 0x7f0300ea, 0x7f0300eb, 0x7f0300ec, 0x7f0300ed, 0x7f0300ee, 0x7f0300ef }
int styleable LeanbackGuidedStepTheme_guidanceBreadcrumbStyle 0
int styleable LeanbackGuidedStepTheme_guidanceContainerStyle 1
int styleable LeanbackGuidedStepTheme_guidanceDescriptionStyle 2
int styleable LeanbackGuidedStepTheme_guidanceEntryAnimation 3
int styleable LeanbackGuidedStepTheme_guidanceIconStyle 4
int styleable LeanbackGuidedStepTheme_guidanceTitleStyle 5
int styleable LeanbackGuidedStepTheme_guidedActionCheckedAnimation 6
int styleable LeanbackGuidedStepTheme_guidedActionContentWidth 7
int styleable LeanbackGuidedStepTheme_guidedActionContentWidthNoIcon 8
int styleable LeanbackGuidedStepTheme_guidedActionContentWidthWeight 9
int styleable LeanbackGuidedStepTheme_guidedActionContentWidthWeightTwoPanels 10
int styleable LeanbackGuidedStepTheme_guidedActionDescriptionMinLines 11
int styleable LeanbackGuidedStepTheme_guidedActionDisabledChevronAlpha 12
int styleable LeanbackGuidedStepTheme_guidedActionEnabledChevronAlpha 13
int styleable LeanbackGuidedStepTheme_guidedActionItemCheckmarkStyle 14
int styleable LeanbackGuidedStepTheme_guidedActionItemChevronStyle 15
int styleable LeanbackGuidedStepTheme_guidedActionItemContainerStyle 16
int styleable LeanbackGuidedStepTheme_guidedActionItemContentStyle 17
int styleable LeanbackGuidedStepTheme_guidedActionItemDescriptionStyle 18
int styleable LeanbackGuidedStepTheme_guidedActionItemIconStyle 19
int styleable LeanbackGuidedStepTheme_guidedActionItemTitleStyle 20
int styleable LeanbackGuidedStepTheme_guidedActionPressedAnimation 21
int styleable LeanbackGuidedStepTheme_guidedActionTitleMaxLines 22
int styleable LeanbackGuidedStepTheme_guidedActionTitleMinLines 23
int styleable LeanbackGuidedStepTheme_guidedActionUncheckedAnimation 24
int styleable LeanbackGuidedStepTheme_guidedActionUnpressedAnimation 25
int styleable LeanbackGuidedStepTheme_guidedActionVerticalPadding 26
int styleable LeanbackGuidedStepTheme_guidedActionsBackground 27
int styleable LeanbackGuidedStepTheme_guidedActionsBackgroundDark 28
int styleable LeanbackGuidedStepTheme_guidedActionsContainerStyle 29
int styleable LeanbackGuidedStepTheme_guidedActionsElevation 30
int styleable LeanbackGuidedStepTheme_guidedActionsEntryAnimation 31
int styleable LeanbackGuidedStepTheme_guidedActionsListStyle 32
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorDrawable 33
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorHideAnimation 34
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorShowAnimation 35
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorStyle 36
int styleable LeanbackGuidedStepTheme_guidedActionsShadowWidth 37
int styleable LeanbackGuidedStepTheme_guidedButtonActionsListStyle 38
int styleable LeanbackGuidedStepTheme_guidedButtonActionsWidthWeight 39
int styleable LeanbackGuidedStepTheme_guidedStepBackground 40
int styleable LeanbackGuidedStepTheme_guidedStepEntryAnimation 41
int styleable LeanbackGuidedStepTheme_guidedStepExitAnimation 42
int styleable LeanbackGuidedStepTheme_guidedStepHeightWeight 43
int styleable LeanbackGuidedStepTheme_guidedStepImeAppearingAnimation 44
int styleable LeanbackGuidedStepTheme_guidedStepImeDisappearingAnimation 45
int styleable LeanbackGuidedStepTheme_guidedStepKeyline 46
int styleable LeanbackGuidedStepTheme_guidedStepReentryAnimation 47
int styleable LeanbackGuidedStepTheme_guidedStepReturnAnimation 48
int styleable LeanbackGuidedStepTheme_guidedStepTheme 49
int styleable LeanbackGuidedStepTheme_guidedStepThemeFlag 50
int styleable LeanbackGuidedStepTheme_guidedSubActionsListStyle 51
int[] styleable LeanbackOnboardingTheme { 0x7f030137, 0x7f030138, 0x7f030139, 0x7f03013a, 0x7f03013b, 0x7f03013c, 0x7f03013d, 0x7f03013e, 0x7f03013f }
int styleable LeanbackOnboardingTheme_onboardingDescriptionStyle 0
int styleable LeanbackOnboardingTheme_onboardingHeaderStyle 1
int styleable LeanbackOnboardingTheme_onboardingLogoStyle 2
int styleable LeanbackOnboardingTheme_onboardingMainIconStyle 3
int styleable LeanbackOnboardingTheme_onboardingNavigatorContainerStyle 4
int styleable LeanbackOnboardingTheme_onboardingPageIndicatorStyle 5
int styleable LeanbackOnboardingTheme_onboardingStartButtonStyle 6
int styleable LeanbackOnboardingTheme_onboardingTheme 7
int styleable LeanbackOnboardingTheme_onboardingTitleStyle 8
int[] styleable LeanbackTheme { 0x7f03003d, 0x7f03003f, 0x7f030040, 0x7f030041, 0x7f030042, 0x7f030043, 0x7f030044, 0x7f030045, 0x7f030046, 0x7f030047, 0x7f030048, 0x7f030049, 0x7f03007b, 0x7f03007c, 0x7f03007d, 0x7f03007f, 0x7f030080, 0x7f030081, 0x7f030082, 0x7f030083, 0x7f030085, 0x7f030086, 0x7f030087, 0x7f030088, 0x7f03009d, 0x7f0300f0, 0x7f0300f1, 0x7f0300fd, 0x7f0300fe, 0x7f0300ff, 0x7f030100, 0x7f030101, 0x7f030102, 0x7f03010a, 0x7f030141, 0x7f030142, 0x7f030143, 0x7f03014e, 0x7f030150, 0x7f030152, 0x7f030153, 0x7f030154, 0x7f030155, 0x7f030156, 0x7f030157, 0x7f030158, 0x7f030159, 0x7f03015a, 0x7f03015b, 0x7f03015c, 0x7f03015d, 0x7f03015e, 0x7f03015f, 0x7f030160, 0x7f030161, 0x7f030162, 0x7f030163, 0x7f030164, 0x7f030165, 0x7f030166, 0x7f030167, 0x7f030185, 0x7f030186, 0x7f030187, 0x7f030189, 0x7f03018a, 0x7f03018b, 0x7f03018c, 0x7f030193, 0x7f030195, 0x7f0301d1 }
int styleable LeanbackTheme_baseCardViewStyle 0
int styleable LeanbackTheme_browsePaddingBottom 1
int styleable LeanbackTheme_browsePaddingEnd 2
int styleable LeanbackTheme_browsePaddingStart 3
int styleable LeanbackTheme_browsePaddingTop 4
int styleable LeanbackTheme_browseRowsFadingEdgeLength 5
int styleable LeanbackTheme_browseRowsMarginStart 6
int styleable LeanbackTheme_browseRowsMarginTop 7
int styleable LeanbackTheme_browseTitleIconStyle 8
int styleable LeanbackTheme_browseTitleTextStyle 9
int styleable LeanbackTheme_browseTitleViewLayout 10
int styleable LeanbackTheme_browseTitleViewStyle 11
int styleable LeanbackTheme_datePickerStyle 12
int styleable LeanbackTheme_defaultBrandColor 13
int styleable LeanbackTheme_defaultBrandColorDark 14
int styleable LeanbackTheme_defaultSearchBrightColor 15
int styleable LeanbackTheme_defaultSearchColor 16
int styleable LeanbackTheme_defaultSearchIcon 17
int styleable LeanbackTheme_defaultSearchIconColor 18
int styleable LeanbackTheme_defaultSectionHeaderColor 19
int styleable LeanbackTheme_detailsActionButtonStyle 20
int styleable LeanbackTheme_detailsDescriptionBodyStyle 21
int styleable LeanbackTheme_detailsDescriptionSubtitleStyle 22
int styleable LeanbackTheme_detailsDescriptionTitleStyle 23
int styleable LeanbackTheme_errorMessageStyle 24
int styleable LeanbackTheme_headerStyle 25
int styleable LeanbackTheme_headersVerticalGridStyle 26
int styleable LeanbackTheme_imageCardViewBadgeStyle 27
int styleable LeanbackTheme_imageCardViewContentStyle 28
int styleable LeanbackTheme_imageCardViewImageStyle 29
int styleable LeanbackTheme_imageCardViewInfoAreaStyle 30
int styleable LeanbackTheme_imageCardViewStyle 31
int styleable LeanbackTheme_imageCardViewTitleStyle 32
int styleable LeanbackTheme_itemsVerticalGridStyle 33
int styleable LeanbackTheme_overlayDimActiveLevel 34
int styleable LeanbackTheme_overlayDimDimmedLevel 35
int styleable LeanbackTheme_overlayDimMaskColor 36
int styleable LeanbackTheme_pickerStyle 37
int styleable LeanbackTheme_pinPickerStyle 38
int styleable LeanbackTheme_playbackControlButtonLabelStyle 39
int styleable LeanbackTheme_playbackControlsActionIcons 40
int styleable LeanbackTheme_playbackControlsAutoHideTickleTimeout 41
int styleable LeanbackTheme_playbackControlsAutoHideTimeout 42
int styleable LeanbackTheme_playbackControlsButtonStyle 43
int styleable LeanbackTheme_playbackControlsIconHighlightColor 44
int styleable LeanbackTheme_playbackControlsTimeStyle 45
int styleable LeanbackTheme_playbackMediaItemDetailsStyle 46
int styleable LeanbackTheme_playbackMediaItemDurationStyle 47
int styleable LeanbackTheme_playbackMediaItemNameStyle 48
int styleable LeanbackTheme_playbackMediaItemNumberStyle 49
int styleable LeanbackTheme_playbackMediaItemNumberViewFlipperLayout 50
int styleable LeanbackTheme_playbackMediaItemNumberViewFlipperStyle 51
int styleable LeanbackTheme_playbackMediaItemPaddingStart 52
int styleable LeanbackTheme_playbackMediaItemRowStyle 53
int styleable LeanbackTheme_playbackMediaItemSeparatorStyle 54
int styleable LeanbackTheme_playbackMediaListHeaderStyle 55
int styleable LeanbackTheme_playbackMediaListHeaderTitleStyle 56
int styleable LeanbackTheme_playbackPaddingEnd 57
int styleable LeanbackTheme_playbackPaddingStart 58
int styleable LeanbackTheme_playbackProgressPrimaryColor 59
int styleable LeanbackTheme_playbackProgressSecondaryColor 60
int styleable LeanbackTheme_rowHeaderDescriptionStyle 61
int styleable LeanbackTheme_rowHeaderDockStyle 62
int styleable LeanbackTheme_rowHeaderStyle 63
int styleable LeanbackTheme_rowHorizontalGridStyle 64
int styleable LeanbackTheme_rowHoverCardDescriptionStyle 65
int styleable LeanbackTheme_rowHoverCardTitleStyle 66
int styleable LeanbackTheme_rowsVerticalGridStyle 67
int styleable LeanbackTheme_searchOrbViewStyle 68
int styleable LeanbackTheme_sectionHeaderStyle 69
int styleable LeanbackTheme_timePickerStyle 70
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f03008d, 0x7f03008f, 0x7f03012b, 0x7f03019d }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f03000e, 0x7f030020, 0x7f030021, 0x7f03002a, 0x7f03006e, 0x7f0300f9, 0x7f0300fa, 0x7f030136, 0x7f03019c, 0x7f0301e2 }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f030170, 0x7f0301ae }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable NavAction { 0x010100d0, 0x7f030084, 0x7f03009c, 0x7f03009e, 0x7f03010e, 0x7f030168, 0x7f030169, 0x7f03016a, 0x7f03016b, 0x7f03016c, 0x7f030181 }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f03002b, 0x7f030133 }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f030000, 0x7f03012c, 0x7f0301e7 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f0301ab }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f03012e }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f0300bb }
int styleable NavInclude_graph 0
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f030184 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable PagingIndicator { 0x7f03002c, 0x7f03002d, 0x7f03002f, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f030118 }
int styleable PagingIndicator_arrowBgColor 0
int styleable PagingIndicator_arrowColor 1
int styleable PagingIndicator_arrowRadius 2
int styleable PagingIndicator_dotBgColor 3
int styleable PagingIndicator_dotToArrowGap 4
int styleable PagingIndicator_dotToDotGap 5
int styleable PagingIndicator_lbDotRadius 6
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f030140 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f0301ac }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable RecycleListView { 0x7f030144, 0x7f030147 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f0300a1, 0x7f0300a2, 0x7f0300a3, 0x7f0300a4, 0x7f0300a5, 0x7f030110, 0x7f030182, 0x7f0301a4, 0x7f0301aa }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f03005b, 0x7f03006d, 0x7f03007e, 0x7f0300ba, 0x7f0300fb, 0x7f03010f, 0x7f030173, 0x7f030174, 0x7f03018d, 0x7f03018e, 0x7f0301af, 0x7f0301b4, 0x7f0301eb }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f03016e }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f03019e, 0x7f0301a8, 0x7f0301b5, 0x7f0301b6, 0x7f0301b8, 0x7f0301c7, 0x7f0301c8, 0x7f0301c9, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x7f0300ad, 0x7f0301ba }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_fontFamily 11
int styleable TextAppearance_textAllCaps 12
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f03004f, 0x7f03005e, 0x7f03005f, 0x7f03006f, 0x7f030070, 0x7f030071, 0x7f030072, 0x7f030073, 0x7f030074, 0x7f030127, 0x7f030128, 0x7f03012a, 0x7f03012f, 0x7f030130, 0x7f03016e, 0x7f0301b0, 0x7f0301b1, 0x7f0301b2, 0x7f0301d4, 0x7f0301d5, 0x7f0301d6, 0x7f0301d7, 0x7f0301d8, 0x7f0301d9, 0x7f0301da, 0x7f0301db, 0x7f0301dc }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_navigationContentDescription 14
int styleable Toolbar_navigationIcon 15
int styleable Toolbar_popupTheme 16
int styleable Toolbar_subtitle 17
int styleable Toolbar_subtitleTextAppearance 18
int styleable Toolbar_subtitleTextColor 19
int styleable Toolbar_title 20
int styleable Toolbar_titleMargin 21
int styleable Toolbar_titleMarginBottom 22
int styleable Toolbar_titleMarginEnd 23
int styleable Toolbar_titleMarginStart 24
int styleable Toolbar_titleMarginTop 25
int styleable Toolbar_titleMargins 26
int styleable Toolbar_titleTextAppearance 27
int styleable Toolbar_titleTextColor 28
int[] styleable View { 0x01010000, 0x010100da, 0x7f030145, 0x7f030146, 0x7f0301c5 }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f03003a, 0x7f03003b }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable lbBaseCardView { 0x7f030022, 0x7f030056, 0x7f030057, 0x7f030058, 0x7f0300a0, 0x7f030105, 0x7f030199, 0x7f03019a }
int styleable lbBaseCardView_activatedAnimationDuration 0
int styleable lbBaseCardView_cardBackground 1
int styleable lbBaseCardView_cardForeground 2
int styleable lbBaseCardView_cardType 3
int styleable lbBaseCardView_extraVisibility 4
int styleable lbBaseCardView_infoVisibility 5
int styleable lbBaseCardView_selectedAnimationDelay 6
int styleable lbBaseCardView_selectedAnimationDuration 7
int[] styleable lbBaseCardView_Layout { 0x7f030117 }
int styleable lbBaseCardView_Layout_layout_viewType 0
int[] styleable lbBaseGridView { 0x010100af, 0x01010114, 0x01010115, 0x7f0300a8, 0x7f0300a9, 0x7f0300aa, 0x7f0300ab, 0x7f0300f7, 0x7f0301e9 }
int styleable lbBaseGridView_android_gravity 0
int styleable lbBaseGridView_android_horizontalSpacing 1
int styleable lbBaseGridView_android_verticalSpacing 2
int styleable lbBaseGridView_focusOutEnd 3
int styleable lbBaseGridView_focusOutFront 4
int styleable lbBaseGridView_focusOutSideEnd 5
int styleable lbBaseGridView_focusOutSideStart 6
int styleable lbBaseGridView_horizontalMargin 7
int styleable lbBaseGridView_verticalMargin 8
int[] styleable lbDatePicker { 0x0101033f, 0x01010340, 0x7f03007a, 0x7f03014c, 0x7f03014d }
int styleable lbDatePicker_android_minDate 0
int styleable lbDatePicker_android_maxDate 1
int styleable lbDatePicker_datePickerFormat 2
int styleable lbDatePicker_pickerItemLayout 3
int styleable lbDatePicker_pickerItemTextViewId 4
int[] styleable lbHorizontalGridView { 0x7f030135, 0x7f030188 }
int styleable lbHorizontalGridView_numberOfRows 0
int styleable lbHorizontalGridView_rowHeight 1
int[] styleable lbImageCardView { 0x7f030104, 0x7f030119 }
int styleable lbImageCardView_infoAreaBackground 0
int styleable lbImageCardView_lbImageCardViewType 1
int[] styleable lbPicker { 0x7f03014c, 0x7f03014d }
int styleable lbPicker_pickerItemLayout 0
int styleable lbPicker_pickerItemTextViewId 1
int[] styleable lbPinPicker { 0x7f03006b, 0x7f03014c, 0x7f03014d }
int styleable lbPinPicker_columnCount 0
int styleable lbPinPicker_pickerItemLayout 1
int styleable lbPinPicker_pickerItemTextViewId 2
int[] styleable lbPlaybackControlsActionIcons { 0x7f03005d, 0x7f0300a6, 0x7f0300f4, 0x7f03014b, 0x7f03014f, 0x7f030151, 0x7f03017b, 0x7f03017c, 0x7f030183, 0x7f0301a0, 0x7f0301a2, 0x7f0301a3, 0x7f0301ca, 0x7f0301cb, 0x7f0301cc, 0x7f0301cd }
int styleable lbPlaybackControlsActionIcons_closed_captioning 0
int styleable lbPlaybackControlsActionIcons_fast_forward 1
int styleable lbPlaybackControlsActionIcons_high_quality 2
int styleable lbPlaybackControlsActionIcons_pause 3
int styleable lbPlaybackControlsActionIcons_picture_in_picture 4
int styleable lbPlaybackControlsActionIcons_play 5
int styleable lbPlaybackControlsActionIcons_repeat 6
int styleable lbPlaybackControlsActionIcons_repeat_one 7
int styleable lbPlaybackControlsActionIcons_rewind 8
int styleable lbPlaybackControlsActionIcons_shuffle 9
int styleable lbPlaybackControlsActionIcons_skip_next 10
int styleable lbPlaybackControlsActionIcons_skip_previous 11
int styleable lbPlaybackControlsActionIcons_thumb_down 12
int styleable lbPlaybackControlsActionIcons_thumb_down_outline 13
int styleable lbPlaybackControlsActionIcons_thumb_up 14
int styleable lbPlaybackControlsActionIcons_thumb_up_outline 15
int[] styleable lbResizingTextView { 0x7f030129, 0x7f03017d, 0x7f03017e, 0x7f03017f, 0x7f030180 }
int styleable lbResizingTextView_maintainLineSpacing 0
int styleable lbResizingTextView_resizeTrigger 1
int styleable lbResizingTextView_resizedPaddingAdjustmentBottom 2
int styleable lbResizingTextView_resizedPaddingAdjustmentTop 3
int styleable lbResizingTextView_resizedTextSize 4
int[] styleable lbSearchOrbView { 0x7f03018f, 0x7f030190, 0x7f030191, 0x7f030192 }
int styleable lbSearchOrbView_searchOrbBrightColor 0
int styleable lbSearchOrbView_searchOrbColor 1
int styleable lbSearchOrbView_searchOrbIcon 2
int styleable lbSearchOrbView_searchOrbIconColor 3
int[] styleable lbSlide { 0x01010141, 0x01010198, 0x010103e2, 0x7f03011a }
int styleable lbSlide_android_interpolator 0
int styleable lbSlide_android_duration 1
int styleable lbSlide_android_startDelay 2
int styleable lbSlide_lb_slideEdge 3
int[] styleable lbTimePicker { 0x7f030107, 0x7f03014c, 0x7f03014d, 0x7f0301e8 }
int styleable lbTimePicker_is24HourFormat 0
int styleable lbTimePicker_pickerItemLayout 1
int styleable lbTimePicker_pickerItemTextViewId 2
int styleable lbTimePicker_useCurrentTime 3
int[] styleable lbVerticalGridView { 0x7f03006c, 0x7f030134 }
int styleable lbVerticalGridView_columnWidth 0
int styleable lbVerticalGridView_numberOfColumns 1
int transition lb_browse_enter_transition 0x7f110000
int transition lb_browse_entrance_transition 0x7f110001
int transition lb_browse_headers_in 0x7f110002
int transition lb_browse_headers_out 0x7f110003
int transition lb_browse_return_transition 0x7f110004
int transition lb_details_enter_transition 0x7f110005
int transition lb_details_return_transition 0x7f110006
int transition lb_enter_transition 0x7f110007
int transition lb_guidedstep_activity_enter 0x7f110008
int transition lb_guidedstep_activity_enter_bottom 0x7f110009
int transition lb_return_transition 0x7f11000a
int transition lb_shared_element_enter_transition 0x7f11000b
int transition lb_shared_element_return_transition 0x7f11000c
int transition lb_title_in 0x7f11000d
int transition lb_title_out 0x7f11000e
int transition lb_vertical_grid_enter_transition 0x7f11000f
int transition lb_vertical_grid_entrance_transition 0x7f110010
int transition lb_vertical_grid_return_transition 0x7f110011
int xml backup_rules 0x7f120000
int xml data_extraction_rules 0x7f120001
