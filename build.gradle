// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    dependencies {
        // 将下面的版本号改成 8.6.0 或更高，比如 8.7.0
        classpath "com.android.tools.build:gradle:8.6.0"
    }
}

plugins {
    id 'com.android.application' version '8.2.0' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.20' apply false
//    id 'org.jetbrains.kotlin.plugin.compose' version '1.9.10' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
