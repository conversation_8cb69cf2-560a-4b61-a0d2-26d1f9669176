# 沉浸式导航栏使用指南

## 🎯 功能概述

荔枝TV采用了智能的沉浸式导航栏设计，能够根据用户的焦点位置自动调整显示状态，提供最佳的观看体验。

## 🔄 工作原理

### 自动展开/收缩机制

1. **导航栏焦点状态**
   - 当用户使用遥控器导航到左侧导航栏时
   - 导航栏自动展开，显示图标和文字标签
   - 提供完整的导航信息

2. **内容区域焦点状态**
   - 当用户导航到右侧内容区域时
   - 导航栏自动收缩为图标模式
   - 最大化内容显示空间

3. **页面切换行为**
   - 选择导航项目后，焦点自动转移到内容区域
   - 导航栏立即收缩，确保沉浸式体验

## 🎨 视觉设计特点

### 沉浸式效果
- **半透明背景** - 导航栏使用半透明背景，不完全遮挡内容
- **渐变效果** - 从左到右的渐变透明度，营造深度感
- **阴影投射** - 适度的阴影效果，增强层次感

### 状态指示
- **展开状态**
  - 显示应用图标和名称
  - 完整的导航项目文字标签
  - 选中项目的背景高亮

- **收缩状态**
  - 仅显示应用图标
  - 导航项目仅显示图标
  - 选中项目的右侧指示条

### 焦点反馈
- **图标缩放** - 焦点项目的图标轻微放大
- **颜色变化** - 焦点和选中状态的颜色区分
- **平滑动画** - 所有状态变化都有流畅的动画过渡

## 🎮 遥控器操作

### 基本导航
1. **左右方向键** - 在导航栏和内容区域间切换
2. **上下方向键** - 在导航项目间移动
3. **确认键** - 选择导航项目或激活功能

### 智能焦点管理
- 进入任何页面时，焦点自动定位到内容区域
- 导航栏自动收缩，提供最大的内容显示空间
- 按左方向键可快速返回导航栏

## 📱 页面适配

### 内容区域自适应
- **动态边距** - 内容区域根据导航栏状态调整左边距
  - 展开状态：240dp 左边距
  - 收缩状态：80dp 左边距
- **平滑过渡** - 边距变化使用动画过渡，避免突兀感

### 响应式布局
- 所有页面都支持导航栏状态变化
- 内容布局自动适应可用空间
- 确保在任何状态下都有最佳的显示效果

## 🔧 技术实现

### 焦点感知系统
```kotlin
// 主屏幕焦点管理
var isContentFocused by remember { mutableStateOf(false) }
val isNavigationExpanded by remember {
    derivedStateOf { !isContentFocused }
}
```

### 自动焦点请求
```kotlin
// 页面加载时自动请求焦点
LaunchedEffect(Unit) {
    contentFocusRequester.requestFocus()
}
```

### 动画配置
```kotlin
// 平滑的宽度动画
val navigationWidth by animateDpAsState(
    targetValue = if (isExpanded) 240.dp else 80.dp,
    animationSpec = tween(durationMillis = 300)
)
```

## 🎯 用户体验优势

1. **沉浸式观看** - 内容优先，导航栏不干扰观看体验
2. **智能适应** - 根据用户操作自动调整界面状态
3. **直观操作** - 符合用户直觉的导航行为
4. **流畅动画** - 所有状态变化都有平滑的视觉过渡
5. **空间优化** - 最大化利用屏幕空间显示内容

## 🔄 状态流转图

```
用户启动应用
    ↓
进入主页（焦点在内容区域）
    ↓
导航栏收缩（仅显示图标）
    ↓
用户按左键 → 导航栏展开（显示文字）
    ↓
选择其他页面 → 焦点转移到内容区域
    ↓
导航栏自动收缩 → 沉浸式内容浏览
```

这种设计确保了用户在浏览内容时能够获得最大的屏幕空间，同时在需要导航时能够快速访问所有功能。
